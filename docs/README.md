# Veterinary Care SaaS System

A comprehensive SaaS solution for veterinary clinics to manage clients, pets, appointments, medical records, and more.

## Table of Contents

- [Overview](#overview)
- [Features](#features)
- [System Architecture](#system-architecture)
- [Frontend Guide](#frontend-guide)
- [Backend Guide](#backend-guide)
- [Data Models](#data-models)
- [API Documentation](#api-documentation)
- [UI Components](#ui-components)
- [Best Practices](#best-practices)

## Overview

This veterinary care system is designed to streamline the operations of veterinary clinics, providing a comprehensive solution for managing clients, pets, appointments, medical records, and more. The system follows a client-server architecture with a React frontend and Node.js/Express backend.

## Features

- **Client Management**: Register, view, edit, and search for clients
- **Pet Management**: Add pets to clients, track medical history, manage species and breeds
- **Appointment Scheduling**: Book, reschedule, and manage appointments
- **Medical Records**: Track consultations, vaccinations, surgeries, and lab results
- **Inventory Management**: Track medications and supplies
- **Staff Management**: Manage clinic staff with role-based permissions
- **Billing and Invoicing**: Generate invoices and track payments

## System Architecture

The system follows a modern client-server architecture:

- **Frontend**: React with TypeScript, using TanStack Query for data fetching and state management
- **Backend**: Node.js with Express, using MongoDB as the database
- **Authentication**: JWT-based authentication system
- **API**: RESTful API design with proper error handling and validation

## Frontend Guide

### Project Structure

```
lovable-vetcare/
├── public/
├── src/
│   ├── components/
│   │   ├── common/           # Shared components
│   │   ├── layout/           # Layout components
│   │   └── ui/               # UI components (shadcn/ui)
│   ├── lib/                  # Utility functions
│   ├── pages/                # Page components
│   │   ├── admin/            # Admin pages
│   │   ├── animals/          # Species and breeds pages
│   │   ├── appointments/     # Appointment pages
│   │   ├── auth/             # Authentication pages
│   │   ├── clients/          # Client pages
│   │   ├── inventory/        # Inventory pages
│   │   ├── medical-records/  # Medical records pages
│   │   ├── pets/             # Pet pages
│   │   └── profile/          # User profile pages
│   ├── services/             # API service functions
│   ├── store/                # State management
│   ├── styles/               # Global styles
│   ├── App.tsx               # Main app component with routes
│   └── main.tsx              # Entry point
└── package.json
```

### Key Components and Pages

#### Species, Breeds, and Pets Management

The system provides a smooth workflow for managing species, breeds, and pets:

1. **Species Management**:
   - List all species with search and pagination
   - Add new species with image upload
   - Edit and delete existing species

2. **Breeds Management**:
   - List all breeds with filtering by species
   - Add new breeds linked to species
   - Edit and delete existing breeds

3. **Pets Management**:
   - Add pets to clients
   - Upload pet profile pictures
   - Track pet medical history
   - Link pets to species and breeds

#### Client Profile Page

The client profile page provides a comprehensive view of client information and their pets:

- Client personal information
- Summary of appointments and invoices
- List of pets with detailed information
- Quick actions for adding pets and scheduling appointments

### Best Practices for Frontend Development

1. **State Management**:
   - Use TanStack Query for server state
   - Keep local state minimal and close to where it's used
   - Use context for global state when necessary

2. **Form Handling**:
   - Use React Hook Form with Zod for validation
   - Implement proper error handling and feedback

3. **API Calls**:
   - Use service functions for all API calls
   - Handle loading, success, and error states
   - Implement proper error handling

4. **Search Implementation**:
   - Debounce search inputs (wait 500ms after typing stops)
   - Trigger search on Enter key press
   - Reset to page 1 when search criteria change

5. **UI Components**:
   - Use consistent styling and spacing
   - Ensure responsive design for all screen sizes
   - Implement proper loading states and error handling

## Backend Guide

### Project Structure

```
vet-care-systems/
├── config/               # Configuration files
├── controllers/          # Route controllers
├── middleware/           # Express middleware
├── models/               # Mongoose models
├── routes/               # Express routes
├── utils/                # Utility functions
├── uploads/              # File uploads
├── app.js                # Express app setup
└── server.js             # Server entry point
```

### Key Models and Controllers

#### Species and Breeds Management

The system provides APIs for managing species and breeds:

1. **Species Controller**:
   - Create, read, update, and delete species
   - Handle image uploads for species
   - Validate species data

2. **Breeds Controller**:
   - Create, read, update, and delete breeds
   - Link breeds to species
   - Filter breeds by species

3. **Pets Controller**:
   - Register new pets
   - Get pets by owner
   - Update pet information
   - Handle pet profile picture uploads

### Best Practices for Backend Development

1. **API Design**:
   - Follow RESTful API design principles
   - Use consistent naming conventions
   - Implement proper error handling and validation

2. **Database Operations**:
   - Use Mongoose for MongoDB interactions
   - Implement proper indexing for performance
   - Use lean() for read operations to improve performance

3. **Authentication and Authorization**:
   - Use JWT for authentication
   - Implement role-based access control
   - Validate user permissions for each operation

4. **File Uploads**:
   - Use multer for handling file uploads
   - Validate file types and sizes
   - Store file paths in the database

5. **Error Handling**:
   - Use try-catch blocks for all async operations
   - Return standardized error responses
   - Log errors for debugging

## Data Models

### Key Models

1. **User**: Base user model for authentication
2. **Client**: Client information linked to user
3. **Staff**: Staff information linked to user
4. **Species**: Animal species information
5. **Breed**: Animal breed information linked to species
6. **Pet**: Pet information linked to client and breed
7. **Appointment**: Appointment information linked to client, pet, and staff
8. **MedicalRecord**: Medical record information linked to pet
9. **Invoice**: Invoice information linked to client and appointments

## API Documentation

### Species API

- `GET /api/species`: Get all species with pagination and filtering
- `POST /api/species`: Create a new species
- `GET /api/species/:id`: Get a species by ID
- `PUT /api/species/:id`: Update a species
- `DELETE /api/species/:id`: Delete a species

### Breeds API

- `GET /api/breeds`: Get all breeds with pagination and filtering
- `GET /api/breeds/species/:speciesId`: Get breeds by species ID
- `POST /api/breeds`: Create a new breed
- `GET /api/breeds/:id`: Get a breed by ID
- `PUT /api/breeds/:id`: Update a breed
- `DELETE /api/breeds/:id`: Delete a breed

### Pets API

- `GET /api/pets`: Get all pets with pagination and filtering
- `GET /api/pets/:id`: Get a pet by ID
- `POST /api/pets`: Create a new pet
- `PUT /api/pets/:id`: Update a pet
- `DELETE /api/pets/:id`: Delete a pet

## UI Components

The system uses a component library based on shadcn/ui, which provides a set of accessible and customizable components:

- **Button**: Various button styles for different actions
- **Card**: Card components for displaying information
- **Dialog**: Modal dialogs for forms and confirmations
- **Dropdown**: Dropdown menus for actions
- **Form**: Form components with validation
- **Table**: Table components for displaying data
- **Tabs**: Tab components for organizing content
- **Toast**: Toast notifications for feedback

## Best Practices

### General Best Practices

1. **Code Organization**:
   - Follow a consistent folder structure
   - Use meaningful file and component names
   - Keep components small and focused

2. **Performance**:
   - Minimize unnecessary re-renders
   - Use pagination for large data sets
   - Implement proper caching strategies

3. **Accessibility**:
   - Ensure proper keyboard navigation
   - Use semantic HTML elements
   - Provide proper ARIA attributes

4. **Security**:
   - Validate all user inputs
   - Implement proper authentication and authorization
   - Sanitize data to prevent XSS attacks

5. **Testing**:
   - Write unit tests for critical functionality
   - Implement integration tests for key workflows
   - Use end-to-end tests for critical user journeys
