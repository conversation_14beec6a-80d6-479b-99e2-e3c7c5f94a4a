# Database Schema Design - Veterinary SaaS System

## Overview
This document defines the complete database schema for the veterinary SaaS system with multi-clinic support, AI integration, and optimized performance.

## Schema Design Principles

### 1. Auto-Incrementing IDs
- All models use numeric auto-incrementing IDs starting from 1000
- No MongoDB ObjectIds (_id) used in application logic
- Consistent ID format across all collections

### 2. Multi-Clinic Data Isolation
- Clinic-based data segregation
- Cross-clinic relationships properly managed
- Performance-optimized queries with clinic context

### 3. Strategic Denormalization
- Critical data denormalized for performance
- Appointment model contains essential client/pet/staff data
- Sync mechanisms for data consistency

## Core Models

### 1. User Model
```javascript
{
  userId: Number (auto-increment, start: 1001),
  email: String (unique, required),
  password: String (hashed, required),
  firstName: String (required),
  middleName: String,
  lastName: String (required),
  phoneNumber: String (required),
  address: String,
  dob: Date,
  roleId: Number (ref: Role),
  permissions: [Number] (ref: Permission),
  status: Number (0: inactive, 1: active),
  lastLogin: Date,
  loginCount: Number,
  loginHistory: [{
    timestamp: Date,
    ipAddress: String,
    userAgent: String,
    status: String (success|failed)
  }],
  createdAt: Date,
  updatedAt: Date
}
```

### 2. Clinic Model
```javascript
{
  clinicId: Number (auto-increment, start: 1001),
  clinicName: String (required),
  owner: Number (ref: Staff),
  managerId: Number (ref: Staff),
  phoneNumber: String (required),
  email: String (required),
  address: String (required),
  city: String,
  state: String,
  zipCode: String,
  country: String,
  website: String,
  description: String,
  operatingHours: {
    monday: { open: String, close: String, isOpen: Boolean },
    tuesday: { open: String, close: String, isOpen: Boolean },
    // ... other days
  },
  services: [Number] (ref: ServiceCategory),
  settings: {
    timezone: String,
    currency: String,
    taxRate: Number,
    appointmentDuration: Number,
    allowWalkIns: Boolean,
    requireDeposit: Boolean,
    cancellationPolicy: String
  },
  subscription: {
    plan: String,
    status: String,
    expiryDate: Date,
    features: [String]
  },
  status: Number (0: inactive, 1: active),
  createdAt: Date,
  updatedAt: Date
}
```

### 3. Staff Model (Enhanced)
```javascript
{
  staffId: Number (auto-increment, start: 1001),
  userId: Number (ref: User, required),
  clinicId: Number (ref: Clinic, required),
  additionalClinics: [Number] (ref: Clinic),
  primaryClinicId: Number (ref: Clinic, required),
  currentClinicId: Number (ref: Clinic),
  roleId: Number (ref: Role, required),
  specialPermissions: [Number] (ref: Permission),
  revokedPermissions: [Number] (ref: Permission),
  jobTitle: String (required),
  department: String,
  specializations: [String],
  qualifications: [String],
  licenseNumber: String,
  employmentDate: Date,
  salary: Number,
  isClinicOwner: Boolean,
  schedule: {
    sunday: { start: String, end: String },
    monday: { start: String, end: String },
    // ... other days
  },
  availability: {
    isAvailable: Boolean,
    unavailableDates: [Date],
    workingHours: Object
  },
  performance: {
    appointmentsCompleted: Number,
    averageRating: Number,
    efficiency: Number,
    lastReview: Date
  },
  status: Number (0: inactive, 1: active),
  lastLogin: Date,
  loginCount: Number,
  loginHistory: [Object],
  createdAt: Date,
  updatedAt: Date
}
```

### 4. Enhanced Appointment Model
```javascript
{
  appointmentId: Number (auto-increment, start: 1001),
  
  // Pet Information (denormalized)
  petId: Number (required),
  petName: String (required, indexed),
  petSpecies: String (required, indexed),
  petBreed: String (required),
  petGender: String (male|female),
  petAge: Number (months),
  petWeight: Number,
  
  // Client Information (denormalized)
  clientId: Number (required),
  clientName: String (required, indexed),
  clientPhone: String (required, indexed),
  clientEmail: String (required),
  isWalkInClient: Boolean (default: false),
  
  // Clinic Information
  clinicId: Number (required, indexed),
  clinicName: String (required),
  
  // Staff Information
  staffInCharge: Number (ref: Staff, required),
  staffInChargeName: String (required, indexed),
  staffInChargeJobTitle: String (required),
  
  // Appointment Details
  appointmentDate: Date (required, indexed),
  appointmentTime: String (required),
  estimatedDuration: Number (minutes),
  actualDuration: Number (minutes),
  status: String (scheduled|in_progress|completed|cancelled),
  priority: String (low|normal|high|urgent),
  
  // Categories and Services
  appointmentCategories: [{
    appointmentCategoryId: Number (ref: AppointmentCategory),
    categoryName: String,
    staffAssigned: Number (ref: Staff),
    staffAssignedName: String,
    staffRole: String,
    estimatedDuration: Number,
    priority: String,
    requiresEquipment: Boolean,
    requiresQualification: Boolean,
    
    // Services within category
    categoryServices: [{
      serviceId: Number,
      serviceName: String,
      description: String,
      status: String (not_started|in_progress|completed),
      notes: String,
      findings: String,
      
      // Medications used
      medicationsUsed: [{
        inventoryItemId: Number,
        medicationName: String,
        quantity: Number,
        unit: String,
        dosage: String,
        frequency: String,
        duration: String,
        instructions: String,
        batchNumber: String,
        expiryDate: Date,
        unitCost: Number,
        totalCost: Number
      }],
      
      startTime: Date,
      endTime: Date,
      duration: Number
    }],
    
    categoryStatus: String (not_started|in_progress|completed),
    isCompleted: Boolean,
    completedAt: Date,
    categoryNotes: String
  }],
  
  // AI Integration
  aiSuggestions: [{
    suggestionId: Number,
    type: String (scheduling|medical|workflow),
    suggestion: String,
    confidence: Number,
    reasoning: String,
    isAccepted: Boolean,
    feedback: String,
    createdAt: Date
  }],
  
  // Progress Tracking
  overallProgress: Number (0-100),
  completedCategories: Number,
  totalCategories: Number,
  
  // Billing Information
  totalCost: Number,
  discountApplied: Number,
  finalAmount: Number,
  paymentStatus: String (pending|partial|paid),
  invoiceId: Number (ref: Invoice),
  receiptId: Number (ref: Receipt),
  
  // Medical Records
  healthRecordId: Number (ref: HealthRecord),
  diagnosis: String,
  treatment: String,
  followUpRequired: Boolean,
  followUpDate: Date,
  followUpAppointmentId: Number (ref: Appointment),
  
  // Metadata
  notes: String,
  internalNotes: String,
  cancellationReason: String,
  createdBy: Number (ref: Staff),
  lastModifiedBy: Number (ref: Staff),
  createdAt: Date,
  updatedAt: Date
}
```

### 5. Client Model (Enhanced)
```javascript
{
  clientId: Number (auto-increment, start: 1001),
  firstName: String (required),
  lastName: String (required),
  email: String (required),
  phoneNumber: String (required, indexed),
  alternatePhone: String,
  address: String,
  city: String,
  state: String,
  zipCode: String,
  
  // Walk-in specific fields
  isWalkIn: Boolean (default: false),
  registrationSource: String (walk-in|online|referral),
  convertedToRegular: Boolean (default: false),
  conversionDate: Date,
  
  // Preferences
  preferredContactMethod: String (phone|email|sms),
  marketingConsent: Boolean,
  reminderPreferences: {
    appointment: Boolean,
    vaccination: Boolean,
    followUp: Boolean
  },
  
  // Emergency Contact
  emergencyContact: {
    name: String,
    relationship: String,
    phoneNumber: String
  },
  
  // Client History
  totalAppointments: Number,
  totalSpent: Number,
  lastVisit: Date,
  registrationDate: Date,
  
  // Multi-clinic relationships
  clinicRelationships: [{
    clinicId: Number (ref: Clinic),
    relationshipType: String (primary|secondary),
    firstVisit: Date,
    lastVisit: Date,
    totalVisits: Number,
    status: String (active|inactive)
  }],
  
  status: Number (0: inactive, 1: active),
  createdAt: Date,
  updatedAt: Date
}
```

## AI Integration Models

### 6. AI Suggestion Model
```javascript
{
  suggestionId: Number (auto-increment, start: 1001),
  clinicId: Number (ref: Clinic, required),
  type: String (appointment|medical|workflow|scheduling),
  context: {
    appointmentId: Number,
    petId: Number,
    clientId: Number,
    staffId: Number
  },
  suggestion: String (required),
  confidence: Number (0-1),
  reasoning: String,
  metadata: Object,
  
  // User interaction
  isPresented: Boolean,
  isAccepted: Boolean,
  userFeedback: String,
  feedbackRating: Number (1-5),
  
  // Performance tracking
  accuracy: Number,
  timeSaved: Number,
  impactScore: Number,
  
  createdAt: Date,
  updatedAt: Date
}
```

### 7. AI Analytics Model
```javascript
{
  analyticsId: Number (auto-increment, start: 1001),
  clinicId: Number (ref: Clinic, required),
  period: String (daily|weekly|monthly),
  date: Date,
  
  metrics: {
    totalSuggestions: Number,
    acceptedSuggestions: Number,
    rejectedSuggestions: Number,
    averageConfidence: Number,
    timeSaved: Number,
    efficiencyGain: Number,
    userSatisfaction: Number
  },
  
  insights: [{
    type: String,
    title: String,
    description: String,
    impact: String (high|medium|low),
    actionable: Boolean,
    recommendations: [String]
  }],
  
  createdAt: Date
}
```

## Indexing Strategy

### Performance Indexes
```javascript
// Critical indexes for performance
Appointment: [
  { clinicId: 1, appointmentDate: 1 },
  { clientPhone: 1 },
  { petName: 1 },
  { staffInCharge: 1, appointmentDate: 1 },
  { status: 1, appointmentDate: 1 }
]

Client: [
  { phoneNumber: 1 },
  { email: 1 },
  { isWalkIn: 1 },
  { "clinicRelationships.clinicId": 1 }
]

Staff: [
  { clinicId: 1, status: 1 },
  { userId: 1 },
  { currentClinicId: 1 }
]

AISuggestion: [
  { clinicId: 1, type: 1 },
  { "context.appointmentId": 1 },
  { createdAt: -1 }
]
```

This database schema provides a robust foundation for the veterinary SaaS system with optimized performance, multi-clinic support, and comprehensive AI integration.
