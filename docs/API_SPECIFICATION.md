# API Specification - Veterinary SaaS System

## Overview
This document defines the complete API specification for the veterinary SaaS system with multi-clinic support, AI integration, and unified appointment management.

## Base Configuration
```
Base URL: /api/v1
Authentication: Bearer JWT Token
Content-Type: application/json
```

## 1. Authentication & Authorization

### POST /auth/sign-up
Register new clinic owner with clinic
```json
{
  "firstName": "string",
  "lastName": "string", 
  "email": "string",
  "password": "string",
  "phoneNumber": "string",
  "roleId": 1001,
  "is_clinic_owner": true,
  "clinic_data": {
    "name": "string",
    "phoneNumber": "string",
    "email": "string",
    "address": "string"
  }
}
```

### POST /auth/sign-in
Login for users and staff
```json
{
  "email": "string",
  "password": "string"
}
```

### POST /auth/switch-clinic
Switch current clinic context
```json
{
  "clinicId": "number"
}
```

## 2. Multi-Clinic Management

### GET /clinics/my-clinics
Get all clinics for current user
```json
Response: {
  "success": true,
  "data": [
    {
      "clinicId": "number",
      "clinicName": "string",
      "role": "owner|manager|staff",
      "permissions": ["string"],
      "isCurrentClinic": "boolean"
    }
  ]
}
```

### POST /clinics/register
Register new clinic (owners only)
```json
{
  "clinicName": "string",
  "phoneNumber": "string",
  "email": "string",
  "address": "string",
  "managerId": "number?"
}
```

### PUT /clinics/:clinicId/assign-staff
Assign staff to clinic
```json
{
  "staffId": "number",
  "roleId": "number",
  "permissions": ["number"]
}
```

### GET /clinics/:clinicId/staff
Get clinic staff with roles and permissions
```json
Response: {
  "success": true,
  "data": [
    {
      "staffId": "number",
      "name": "string",
      "email": "string",
      "role": "string",
      "permissions": ["string"],
      "isActive": "boolean"
    }
  ]
}
```

## 3. Unified Appointment System

### POST /appointments/book
Unified appointment booking
```json
{
  "clientType": "registered|walk-in",
  "clientData": {
    "clientId": "number?",
    "firstName": "string",
    "lastName": "string",
    "phoneNumber": "string",
    "email": "string",
    "isWalkIn": "boolean"
  },
  "petData": {
    "petId": "number?",
    "petName": "string",
    "species": "string",
    "breed": "string",
    "age": "number",
    "weight": "number"
  },
  "appointmentData": {
    "appointmentDate": "string",
    "appointmentTime": "string",
    "categories": ["number"],
    "notes": "string",
    "priority": "low|normal|high|urgent"
  }
}
```

### GET /appointments/conflicts
Check for scheduling conflicts
```json
Query: {
  "date": "string",
  "time": "string",
  "duration": "number",
  "staffId": "number?"
}
```

### POST /appointments/:appointmentId/assign-tasks
Assign tasks to staff
```json
{
  "categoryId": "number",
  "staffId": "number",
  "estimatedDuration": "number",
  "priority": "string"
}
```

### PUT /appointments/:appointmentId/update-progress
Update appointment progress
```json
{
  "categoryId": "number",
  "status": "not_started|in_progress|completed",
  "completedServices": ["number"],
  "notes": "string"
}
```

## 4. AI Integration

### POST /ai/appointment-suggestions
Get AI-powered appointment suggestions
```json
{
  "petId": "number",
  "symptoms": "string",
  "urgency": "string",
  "preferredDate": "string"
}

Response: {
  "suggestions": [
    {
      "categoryId": "number",
      "categoryName": "string",
      "confidence": "number",
      "reasoning": "string",
      "estimatedDuration": "number",
      "recommendedStaff": ["number"]
    }
  ]
}
```

### POST /ai/medical-recommendations
Get AI medical recommendations
```json
{
  "petId": "number",
  "appointmentId": "number",
  "symptoms": "string",
  "history": "string"
}

Response: {
  "recommendations": [
    {
      "type": "medication|procedure|test",
      "name": "string",
      "confidence": "number",
      "reasoning": "string",
      "contraindications": ["string"]
    }
  ]
}
```

### POST /ai/workflow-automation
Trigger AI workflow automation
```json
{
  "appointmentId": "number",
  "trigger": "appointment_created|category_completed|symptoms_updated"
}
```

### GET /ai/clinic-insights
Get AI-powered clinic insights
```json
Response: {
  "insights": [
    {
      "type": "efficiency|revenue|staffing|inventory",
      "title": "string",
      "description": "string",
      "impact": "high|medium|low",
      "actionable": "boolean",
      "recommendations": ["string"]
    }
  ]
}
```

## 5. Staff Management & Permissions

### GET /staff/clinic/:clinicId
Get staff for specific clinic
```json
Response: {
  "staff": [
    {
      "staffId": "number",
      "name": "string",
      "role": "string",
      "specializations": ["string"],
      "availability": "object",
      "currentTasks": "number"
    }
  ]
}
```

### PUT /staff/:staffId/permissions
Update staff permissions
```json
{
  "clinicId": "number",
  "permissions": ["number"],
  "specialPermissions": ["number"],
  "revokedPermissions": ["number"]
}
```

### GET /staff/:staffId/workload
Get staff current workload
```json
Response: {
  "currentAppointments": "number",
  "pendingTasks": "number",
  "todaySchedule": ["object"],
  "availability": "object"
}
```

## 6. Walk-in Client Management

### POST /clients/walk-in/register
Quick walk-in client registration
```json
{
  "firstName": "string",
  "lastName": "string",
  "phoneNumber": "string",
  "email": "string?",
  "petName": "string",
  "petSpecies": "string",
  "petBreed": "string",
  "urgency": "low|normal|high|urgent"
}
```

### PUT /clients/:clientId/convert
Convert walk-in to regular client
```json
{
  "address": "string",
  "emergencyContact": "string",
  "preferences": "object",
  "marketingConsent": "boolean"
}
```

### GET /clients/walk-ins
Get all walk-in clients
```json
Query: {
  "date": "string?",
  "status": "pending|converted|archived"
}
```

## 7. Billing & Documentation

### POST /invoices/generate
Generate invoice from appointment
```json
{
  "appointmentId": "number",
  "discounts": [
    {
      "categoryId": "number",
      "discountPercent": "number",
      "reason": "string"
    }
  ]
}
```

### POST /receipts/generate
Generate receipt after payment
```json
{
  "invoiceId": "number",
  "paymentMethod": "cash|card|insurance",
  "amountPaid": "number",
  "paymentReference": "string"
}
```

### POST /medical-records/create
Create medical record from appointment
```json
{
  "appointmentId": "number",
  "diagnosis": "string",
  "treatment": "string",
  "medications": ["object"],
  "followUpRequired": "boolean",
  "followUpDate": "string?"
}
```

## 8. Real-time Features

### WebSocket Events
```javascript
// Client to Server
'join-clinic' - Join clinic room for updates
'appointment-update' - Real-time appointment updates
'task-assignment' - Task assignment notifications

// Server to Client
'appointment-status-changed' - Appointment status update
'new-task-assigned' - New task assignment
'ai-suggestion-ready' - AI suggestion available
'clinic-notification' - Clinic-wide notification
```

## 9. Error Handling

### Standard Error Response
```json
{
  "success": false,
  "status": "number",
  "message": "string",
  "errors": ["string"],
  "timestamp": "string"
}
```

### Error Codes
- 400: Bad Request
- 401: Unauthorized
- 403: Forbidden (insufficient permissions)
- 404: Resource not found
- 409: Conflict (scheduling conflicts)
- 422: Validation error
- 500: Internal server error

## 10. Rate Limiting

### API Limits
- General APIs: 100 requests/minute
- AI APIs: 20 requests/minute
- Authentication: 10 requests/minute
- File uploads: 5 requests/minute

## 11. Pagination

### Standard Pagination
```json
Query: {
  "page": "number",
  "limit": "number",
  "sortBy": "string",
  "sortOrder": "asc|desc"
}

Response: {
  "data": ["object"],
  "pagination": {
    "currentPage": "number",
    "totalPages": "number",
    "totalItems": "number",
    "hasNext": "boolean",
    "hasPrev": "boolean"
  }
}
```

## 12. Data Validation

### Input Validation Rules
```javascript
// Client data validation
firstName: required, min:2, max:50, alpha
lastName: required, min:2, max:50, alpha
email: required, email format
phoneNumber: required, phone format
petName: required, min:2, max:30
species: required, enum values
breed: required, string
age: required, number, min:0, max:300 months
weight: required, number, min:0.1, max:200 kg
```

### Business Rules
- Walk-in clients must have phone number
- Appointments cannot be scheduled in the past
- Staff can only be assigned to categories they're qualified for
- AI suggestions require minimum confidence threshold
- Cross-clinic operations require proper permissions

This API specification provides a comprehensive foundation for building the veterinary SaaS system with all required features including multi-clinic support, AI integration, and unified appointment management.
