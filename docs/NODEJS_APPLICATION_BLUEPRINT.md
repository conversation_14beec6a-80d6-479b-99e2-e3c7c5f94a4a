# Node.js Application Blueprint

## Table of Contents
1. [Project Overview](#project-overview)
2. [Technology Stack](#technology-stack)
3. [Project Structure](#project-structure)
4. [Authentication Implementation](#authentication-implementation)
5. [Redis Integration](#redis-integration)
6. [Queue System](#queue-system)
7. [API Controllers](#api-controllers)
8. [Database Integration](#database-integration)
9. [Security Implementation](#security-implementation)
10. [Testing Strategy](#testing-strategy)
11. [Deployment Guide](#deployment-guide)

## Project Overview

This blueprint provides a comprehensive guide for building a modern Node.js application equivalent to the reorganized MossBets B2B Back Office system. It includes enterprise-grade security, Redis caching, message queuing, and real-time capabilities.

## Recent Updates (2025-08-14)

### 🔐 Enhanced Authentication Architecture
- **Unified Authentication**: Single middleware for all user types (Admin, Partner, etc.)
- **Role-based Permissions**: Implement auto-creation for role_id 1 and 2
- **Permission Bypass**: Super admin roles automatically bypass all permission checks
- **Dynamic Permission Updates**: Auto-update super admin roles when new permissions are created

### 🏗️ Microservices Architecture
Implement clean separation of concerns with dedicated services:

```javascript
// System Service - Roles & Permissions
app.use('/system/v1', systemRoutes);

// User Service - User Management
app.use('/users/v1', userRoutes);

// Transaction Service - Financial Operations
app.use('/transactions/v1', transactionRoutes);

// Service Management - Services & Settings
app.use('/services/v1', serviceRoutes);

// Partner Service - Core Partner Operations
app.use('/partners/v1', partnerRoutes);

// Betting Service - Betting Operations
app.use('/bets/v1', bettingRoutes);
```

### 🛡️ Security Enhancements
- **JWT-based Authentication**: Unified token system
- **Role-based Access Control**: Granular permission management
- **Auto-permission Creation**: Dynamic permission system for super admins
- **Request Validation**: Comprehensive input validation middleware

### Key Features
- **Express.js/Fastify Framework**: High-performance web framework
- **JWT Authentication**: Secure token-based authentication
- **Redis Integration**: Caching, session management, and queuing
- **Message Queuing**: Bull Queue for background processing
- **Real-time Updates**: WebSocket support for live notifications
- **Database**: Prisma ORM with MySQL
- **Security**: Comprehensive security middleware and validation
- **Monitoring**: Winston logging and health checks

## Technology Stack

```json
{
  "framework": "Express.js / Fastify",
  "authentication": "Passport.js + JWT",
  "database": "Prisma ORM + MySQL",
  "caching": "Redis + ioredis",
  "validation": "Joi / Zod",
  "security": "Helmet.js + express-rate-limit",
  "messaging": "Bull Queue + Redis",
  "monitoring": "Winston + Morgan",
  "testing": "Jest + Supertest",
  "websockets": "Socket.io",
  "containerization": "Docker + Docker Compose"
}
```

### Dependencies
```json
{
  "dependencies": {
    "express": "^4.18.2",
    "jsonwebtoken": "^9.0.2",
    "bcryptjs": "^2.4.3",
    "prisma": "^5.7.0",
    "@prisma/client": "^5.7.0",
    "ioredis": "^5.3.2",
    "bull": "^4.12.0",
    "joi": "^17.11.0",
    "helmet": "^7.1.0",
    "express-rate-limit": "^7.1.5",
    "winston": "^3.11.0",
    "morgan": "^1.10.0",
    "socket.io": "^4.7.4",
    "cors": "^2.8.5",
    "dotenv": "^16.3.1"
  },
  "devDependencies": {
    "jest": "^29.7.0",
    "supertest": "^6.3.3",
    "nodemon": "^3.0.2",
    "@types/node": "^20.10.5"
  }
}
```

## Project Structure

```
src/
├── controllers/
│   ├── AuthController.js
│   ├── DashboardController.js
│   ├── BetsController.js
│   ├── UserController.js
│   ├── PartnerController.js
│   └── TransactionController.js
├── middleware/
│   ├── auth.js
│   ├── validation.js
│   ├── rateLimit.js
│   ├── security.js
│   └── errorHandler.js
├── services/
│   ├── AuthService.js
│   ├── CacheService.js
│   ├── QueueService.js
│   ├── NotificationService.js
│   └── WebSocketService.js
├── models/
│   └── prisma/
│       ├── schema.prisma
│       └── migrations/
├── routes/
│   ├── auth.js
│   ├── dashboard.js
│   ├── partners.js
│   ├── bets.js
│   └── transactions.js
├── utils/
│   ├── helpers.js
│   ├── constants.js
│   └── validators.js
├── config/
│   ├── database.js
│   ├── redis.js
│   ├── queue.js
│   └── websocket.js
├── tests/
│   ├── unit/
│   ├── integration/
│   └── fixtures/
└── app.js
```

## Authentication Implementation

### Authentication Middleware
```javascript
// middleware/auth.js
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const redis = require('../config/redis');
const { AuthService } = require('../services/AuthService');

class AuthMiddleware {
  static async authenticateAndAuthorize(req, res, next) {
    try {
      // Extract authentication headers
      const authData = this.extractAuthHeaders(req.headers);
      
      if (!authData.accessToken || !authData.appKey || !authData.hashKey || !authData.timestamp) {
        return res.status(401).json({
          code: 401,
          statusDescription: 'Authentication failed',
          message: 'Missing required authentication headers'
        });
      }
      
      // Validate timestamp (prevent replay attacks)
      if (!this.validateTimestamp(authData.timestamp)) {
        return res.status(401).json({
          code: 401,
          statusDescription: 'Authentication failed',
          message: 'Request timestamp expired'
        });
      }
      
      // Validate hash integrity
      if (!this.validateHash(req.body, authData)) {
        return res.status(401).json({
          code: 401,
          statusDescription: 'Authentication failed',
          message: 'Invalid request signature'
        });
      }
      
      // Verify JWT token
      const decoded = jwt.verify(authData.accessToken, process.env.JWT_SECRET);
      
      // Check if user session exists in Redis
      const sessionKey = `user:session:${decoded.userId}`;
      const session = await redis.get(sessionKey);
      
      if (!session) {
        return res.status(401).json({
          code: 401,
          statusDescription: 'Authentication failed',
          message: 'Session expired'
        });
      }
      
      // Check user permissions
      const hasPermission = await AuthService.validateUserPermission(
        decoded.userId, 
        req.route.permission
      );
      
      if (!hasPermission) {
        return res.status(403).json({
          code: 403,
          statusDescription: 'Authorization failed',
          message: 'Insufficient permissions'
        });
      }
      
      req.user = decoded;
      req.session = JSON.parse(session);
      next();
    } catch (error) {
      console.error('Authentication error:', error);
      return res.status(401).json({
        code: 401,
        statusDescription: 'Authentication failed',
        message: 'Invalid authentication token'
      });
    }
  }
  
  static extractAuthHeaders(headers) {
    return {
      accessToken: headers['x-access-token'],
      appKey: headers['x-app-key'],
      hashKey: headers['x-hash-key'],
      timestamp: headers['x-timestamp']
    };
  }
  
  static validateTimestamp(timestamp) {
    const now = Math.floor(Date.now() / 1000);
    const requestTime = parseInt(timestamp);
    return Math.abs(now - requestTime) <= 300; // 5 minutes tolerance
  }
  
  static validateHash(data, authData) {
    const payload = JSON.stringify(data) + authData.timestamp;
    const expectedHash = crypto
      .createHmac('sha256', authData.hashKey)
      .update(payload)
      .digest('hex');
    return expectedHash === authData.signature;
  }
}

module.exports = AuthMiddleware;
```

### Authentication Service
```javascript
// services/AuthService.js
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const { PrismaClient } = require('@prisma/client');
const CacheService = require('./CacheService');

const prisma = new PrismaClient();

class AuthService {
  static async login(username, password, otpCode = null) {
    try {
      // Find user with login details
      const user = await prisma.user.findFirst({
        where: { username },
        include: {
          userLogin: true,
          roles: {
            include: {
              permissions: true
            }
          },
          partners: true
        }
      });
      
      if (!user || !user.userLogin) {
        throw new Error('Invalid credentials');
      }
      
      // Verify password
      const isValidPassword = await bcrypt.compare(password, user.userLogin.password);
      if (!isValidPassword) {
        throw new Error('Invalid credentials');
      }
      
      // Check if user is active
      if (user.status !== 'ACTIVE') {
        throw new Error('Account is not active');
      }
      
      // If OTP is required and not provided
      if (user.userLogin.requiresOtp && !otpCode) {
        // Generate and send OTP
        const otp = this.generateOTP();
        await this.sendOTP(user.id, otp);
        
        return {
          requiresOtp: true,
          userId: user.id,
          message: 'OTP sent to your registered contact'
        };
      }
      
      // Verify OTP if provided
      if (otpCode && !await this.verifyOTP(user.id, otpCode)) {
        throw new Error('Invalid OTP');
      }
      
      // Generate JWT token
      const token = this.generateToken(user);
      
      // Store session in Redis
      const sessionData = {
        userId: user.id,
        username: user.username,
        roles: user.roles.map(r => r.name),
        partners: user.partners.map(p => ({ id: p.id, name: p.name })),
        loginTime: new Date().toISOString()
      };
      
      await CacheService.set(`user:session:${user.id}`, sessionData, 3600); // 1 hour
      
      return {
        token,
        user: {
          id: user.id,
          username: user.username,
          roles: user.roles.map(r => r.name),
          partners: user.partners
        }
      };
    } catch (error) {
      throw error;
    }
  }
  
  static generateToken(user) {
    const payload = {
      userId: user.id,
      username: user.username,
      roles: user.roles.map(r => r.name),
      partnerCount: user.partners.length
    };
    
    return jwt.sign(payload, process.env.JWT_SECRET, {
      expiresIn: process.env.JWT_EXPIRATION || '1h',
      issuer: 'mossbets-api'
    });
  }
  
  static async validateUserPermission(userId, requiredPermission) {
    if (!requiredPermission) return true;
    
    // Check cache first
    const cacheKey = `user:permissions:${userId}`;
    let permissions = await CacheService.get(cacheKey);
    
    if (!permissions) {
      // Fetch from database
      const user = await prisma.user.findUnique({
        where: { id: userId },
        include: {
          roles: {
            include: {
              permissions: true
            }
          }
        }
      });
      
      permissions = user.roles.flatMap(role => 
        role.permissions.map(p => p.name)
      );
      
      // Cache for 30 minutes
      await CacheService.set(cacheKey, permissions, 1800);
    }
    
    return permissions.includes(requiredPermission);
  }
  
  static generateOTP() {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }
  
  static async sendOTP(userId, otp) {
    // Store OTP in Redis with 5-minute expiration
    await CacheService.set(`otp:${userId}`, otp, 300);
    
    // Here you would integrate with SMS/Email service
    console.log(`OTP for user ${userId}: ${otp}`);
  }
  
  static async verifyOTP(userId, providedOtp) {
    const storedOtp = await CacheService.get(`otp:${userId}`);
    if (storedOtp === providedOtp) {
      // Remove OTP after successful verification
      await CacheService.invalidate(`otp:${userId}`);
      return true;
    }
    return false;
  }
  
  static async logout(userId) {
    // Remove session from Redis
    await CacheService.invalidate(`user:session:${userId}`);
    await CacheService.invalidate(`user:permissions:${userId}`);
  }
}

module.exports = AuthService;
```

## Redis Integration

### Cache Service
```javascript
// services/CacheService.js
const Redis = require('ioredis');

class CacheService {
  constructor() {
    this.redis = new Redis({
      host: process.env.REDIS_HOST || 'localhost',
      port: process.env.REDIS_PORT || 6379,
      password: process.env.REDIS_PASSWORD,
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3,
      lazyConnect: true
    });

    this.redis.on('error', (err) => {
      console.error('Redis connection error:', err);
    });

    this.redis.on('connect', () => {
      console.log('Redis connected successfully');
    });
  }

  async set(key, value, ttl = 3600) {
    try {
      return await this.redis.setex(key, ttl, JSON.stringify(value));
    } catch (error) {
      console.error('Redis SET error:', error);
      throw error;
    }
  }

  async get(key) {
    try {
      const value = await this.redis.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.error('Redis GET error:', error);
      return null;
    }
  }

  async invalidate(pattern) {
    try {
      const keys = await this.redis.keys(pattern);
      if (keys.length > 0) {
        return await this.redis.del(...keys);
      }
      return 0;
    } catch (error) {
      console.error('Redis INVALIDATE error:', error);
      throw error;
    }
  }

  async exists(key) {
    try {
      return await this.redis.exists(key);
    } catch (error) {
      console.error('Redis EXISTS error:', error);
      return false;
    }
  }

  async increment(key, value = 1) {
    try {
      return await this.redis.incrby(key, value);
    } catch (error) {
      console.error('Redis INCREMENT error:', error);
      throw error;
    }
  }

  async setHash(key, field, value, ttl = 3600) {
    try {
      await this.redis.hset(key, field, JSON.stringify(value));
      await this.redis.expire(key, ttl);
    } catch (error) {
      console.error('Redis HSET error:', error);
      throw error;
    }
  }

  async getHash(key, field) {
    try {
      const value = await this.redis.hget(key, field);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.error('Redis HGET error:', error);
      return null;
    }
  }

  async getAllHash(key) {
    try {
      const hash = await this.redis.hgetall(key);
      const result = {};
      for (const [field, value] of Object.entries(hash)) {
        result[field] = JSON.parse(value);
      }
      return result;
    } catch (error) {
      console.error('Redis HGETALL error:', error);
      return {};
    }
  }
}

module.exports = new CacheService();
```

## Queue System

### Queue Service
```javascript
// services/QueueService.js
const Queue = require('bull');
const redis = require('../config/redis');

class QueueService {
  constructor() {
    this.queues = {
      notifications: new Queue('notifications', { redis }),
      reports: new Queue('reports', { redis }),
      transactions: new Queue('transactions', { redis }),
      betProcessing: new Queue('bet-processing', { redis })
    };

    this.setupProcessors();
    this.setupEventHandlers();
  }

  setupProcessors() {
    // Email notifications
    this.queues.notifications.process('email', 5, require('../processors/emailProcessor'));

    // SMS notifications
    this.queues.notifications.process('sms', 10, require('../processors/smsProcessor'));

    // Report generation
    this.queues.reports.process('generate', 2, require('../processors/reportProcessor'));

    // Transaction processing
    this.queues.transactions.process('deposit', 5, require('../processors/depositProcessor'));
    this.queues.transactions.process('withdrawal', 3, require('../processors/withdrawalProcessor'));

    // Bet processing
    this.queues.betProcessing.process('settle', 10, require('../processors/betSettlementProcessor'));
  }

  setupEventHandlers() {
    Object.values(this.queues).forEach(queue => {
      queue.on('completed', (job) => {
        console.log(`Job ${job.id} completed successfully`);
      });

      queue.on('failed', (job, err) => {
        console.error(`Job ${job.id} failed:`, err);
      });

      queue.on('stalled', (job) => {
        console.warn(`Job ${job.id} stalled`);
      });
    });
  }

  async addJob(queueName, jobType, data, options = {}) {
    try {
      return await this.queues[queueName].add(jobType, data, {
        delay: options.delay || 0,
        attempts: options.attempts || 3,
        backoff: 'exponential',
        removeOnComplete: options.removeOnComplete || 10,
        removeOnFail: options.removeOnFail || 5,
        ...options
      });
    } catch (error) {
      console.error(`Error adding job to ${queueName}:`, error);
      throw error;
    }
  }

  async getQueueStats(queueName) {
    const queue = this.queues[queueName];
    if (!queue) {
      throw new Error(`Queue ${queueName} not found`);
    }

    const [waiting, active, completed, failed, delayed] = await Promise.all([
      queue.getWaiting(),
      queue.getActive(),
      queue.getCompleted(),
      queue.getFailed(),
      queue.getDelayed()
    ]);

    return {
      waiting: waiting.length,
      active: active.length,
      completed: completed.length,
      failed: failed.length,
      delayed: delayed.length
    };
  }

  async pauseQueue(queueName) {
    return await this.queues[queueName].pause();
  }

  async resumeQueue(queueName) {
    return await this.queues[queueName].resume();
  }
}

module.exports = new QueueService();
```
```
