import { config } from "dotenv";

// Load environment variables from the appropriate .env file
config({ path: `.env.${process.env.NODE_ENV || 'development'}.local` });

/**
 * Required environment variables
 * Add new required variables to this array
 */
const requiredEnvVars = [
    'PORT',
    'DB_URI',
    'JWT_SECRET',
    'JWT_EXPIRES_IN'
];

/**
 * Validate that all required environment variables are defined
 */
const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);
if (missingEnvVars.length > 0) {
    throw new Error(`Missing required environment variables: ${missingEnvVars.join(', ')}`);
}

/**
 * Environment variables with default values
 */
const NODE_ENV = process.env.NODE_ENV || 'development';
const PORT = parseInt(process.env.PORT, 10) || 3000;
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '1h';

/**
 * Extract environment variables
 */
const DB_URI = process.env.DB_URI;
const JWT_SECRET = process.env.JWT_SECRET;
const ARCJET_KEY = process.env.ARCJET_KEY;
const ARCJET_ENV = process.env.ARCJET_ENV;
const QSTASH_URL = process.env.QSTASH_URL;
const QSTASH_TOKEN = process.env.QSTASH_TOKEN;

// Email Configuration (optional)
const EMAIL_CONFIG = {
    host: process.env.EMAIL_HOST || 'smtp.gmail.com',
    port: parseInt(process.env.EMAIL_PORT, 10) || 587,
    secure: process.env.EMAIL_SECURE === 'true',
    user: process.env.EMAIL_USER,
    password: process.env.EMAIL_PASSWORD,
    from: process.env.EMAIL_FROM || '<EMAIL>'
};

// SMS Configuration (optional)
const SMS_CONFIG = {
    accountSid: process.env.TWILIO_ACCOUNT_SID,
    authToken: process.env.TWILIO_AUTH_TOKEN,
    phoneNumber: process.env.TWILIO_PHONE_NUMBER
};

// AI Configuration
const AI_API_KEY = process.env.OPENAI_API_KEY || process.env.AI_API_KEY;
const AI_MODEL = process.env.AI_MODEL || 'gpt-4';
const AI_ENABLED = process.env.AI_ENABLED !== 'false'; // Default to true

// AI Rate Limiting
const AI_RATE_LIMITS = {
    appointmentSuggestions: parseInt(process.env.AI_RATE_LIMIT_APPOINTMENTS, 10) || 20,
    medicalRecommendations: parseInt(process.env.AI_RATE_LIMIT_MEDICAL, 10) || 15,
    workflowAutomation: parseInt(process.env.AI_RATE_LIMIT_WORKFLOW, 10) || 10,
    clinicInsights: parseInt(process.env.AI_RATE_LIMIT_INSIGHTS, 10) || 5
};

// Redis Configuration
const REDIS_CONFIG = {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT, 10) || 6379,
    password: process.env.REDIS_PASSWORD || '',
    db: parseInt(process.env.REDIS_DB, 10) || 0,
    retryDelayOnFailover: 100,
    maxRetriesPerRequest: 3,
    lazyConnect: true
};

/**
 * Export all environment variables
 */
export {
    // Server configuration
    PORT,
    NODE_ENV,

    // Database configuration
    DB_URI,

    // Authentication configuration
    JWT_SECRET,
    JWT_EXPIRES_IN,

    // Security configuration (optional)
    ARCJET_KEY,
    ARCJET_ENV,

    // Queue configuration (optional)
    QSTASH_URL,
    QSTASH_TOKEN,

    // Notification configuration (optional)
    EMAIL_CONFIG,
    SMS_CONFIG,

    // AI configuration
    AI_API_KEY,
    AI_MODEL,
    AI_ENABLED,
    AI_RATE_LIMITS,

    // Redis configuration
    REDIS_CONFIG,
};
