import OpenAI from 'openai';
import { AI_API_KEY, AI_MODEL, AI_ENABLED } from './env.js';

// Initialize OpenAI client only if API key is available
let openai = null;
const apiKey = AI_API_KEY || process.env.OPENAI_API_KEY;

if (apiKey && AI_ENABLED) {
    try {
        openai = new OpenAI({
            apiKey: apiKey,
        });
        console.log('✅ OpenAI client initialized successfully');
    } catch (error) {
        console.warn('⚠️ Failed to initialize OpenAI client:', error.message);
        openai = null;
    }
} else {
    console.warn('⚠️ OpenAI API key not provided or AI disabled. AI features will be unavailable.');
}

export { openai };

// AI Configuration
export const AI_CONFIG = {
    model: AI_MODEL || 'gpt-4',
    temperature: 0.3,
    maxTokens: 1000,
    
    // Veterinary-specific prompts
    prompts: {
        appointmentSuggestion: {
            system: "You are an expert veterinary AI assistant. Provide accurate, helpful appointment category suggestions based on symptoms and medical history. Always prioritize patient safety and recommend urgent care when necessary.",
            temperature: 0.3,
            maxTokens: 1000
        },
        
        medicalRecommendation: {
            system: "You are an expert veterinary medical AI. Provide evidence-based medical recommendations while emphasizing the need for professional veterinary judgment. Include differential diagnoses, diagnostic tests, and treatment options.",
            temperature: 0.2,
            maxTokens: 1500
        },
        
        workflowAutomation: {
            system: "You are a veterinary workflow optimization AI. Analyze appointment data and suggest efficient task sequences, staff assignments, and process improvements.",
            temperature: 0.4,
            maxTokens: 800
        },
        
        clinicInsights: {
            system: "You are a veterinary business intelligence AI. Analyze clinic data to provide actionable insights for improving efficiency, revenue, and patient care quality.",
            temperature: 0.3,
            maxTokens: 1200
        }
    },
    
    // Rate limiting
    rateLimits: {
        appointmentSuggestions: 20, // per minute
        medicalRecommendations: 15, // per minute
        workflowAutomation: 10, // per minute
        clinicInsights: 5 // per minute
    },
    
    // Confidence thresholds
    confidenceThresholds: {
        appointmentSuggestion: 0.6,
        medicalRecommendation: 0.7,
        urgentCare: 0.8
    }
};

// AI Response validation
export const validateAIResponse = (response, type) => {
    try {
        const parsed = typeof response === 'string' ? JSON.parse(response) : response;
        
        switch (type) {
            case 'appointment_suggestion':
                return validateAppointmentSuggestion(parsed);
            case 'medical_recommendation':
                return validateMedicalRecommendation(parsed);
            case 'workflow_automation':
                return validateWorkflowAutomation(parsed);
            case 'clinic_insights':
                return validateClinicInsights(parsed);
            default:
                return { isValid: false, error: 'Unknown response type' };
        }
    } catch (error) {
        return { isValid: false, error: 'Invalid JSON response' };
    }
};

const validateAppointmentSuggestion = (response) => {
    if (!response.suggestions || !Array.isArray(response.suggestions)) {
        return { isValid: false, error: 'Missing or invalid suggestions array' };
    }
    
    for (const suggestion of response.suggestions) {
        if (!suggestion.categoryName || typeof suggestion.confidence !== 'number') {
            return { isValid: false, error: 'Invalid suggestion format' };
        }
        
        if (suggestion.confidence < 0 || suggestion.confidence > 1) {
            return { isValid: false, error: 'Confidence score must be between 0 and 1' };
        }
    }
    
    return { isValid: true };
};

const validateMedicalRecommendation = (response) => {
    if (!response.differentialDiagnoses && !response.recommendations) {
        return { isValid: false, error: 'Missing medical recommendations' };
    }
    
    return { isValid: true };
};

const validateWorkflowAutomation = (response) => {
    if (!response.tasks && !response.recommendations) {
        return { isValid: false, error: 'Missing workflow automation data' };
    }
    
    return { isValid: true };
};

const validateClinicInsights = (response) => {
    if (!response.insights && !response.recommendations) {
        return { isValid: false, error: 'Missing clinic insights data' };
    }
    
    return { isValid: true };
};

// AI Safety filters
export const applySafetyFilters = (prompt, type) => {
    // Remove any potentially harmful content
    let filteredPrompt = prompt;
    
    // Add safety disclaimers for medical content
    if (type === 'medical_recommendation') {
        filteredPrompt += "\n\nIMPORTANT: These are AI-generated suggestions for veterinary professionals. Always use clinical judgment and follow established protocols.";
    }
    
    return filteredPrompt;
};

// AI Usage tracking
export const trackAIUsage = async (clinicId, type, tokens, cost) => {
    try {
        // Track AI usage for billing and analytics
        // This would integrate with your analytics system
        console.log(`AI Usage - Clinic: ${clinicId}, Type: ${type}, Tokens: ${tokens}, Cost: ${cost}`);
    } catch (error) {
        console.error('Error tracking AI usage:', error);
    }
};

// Emergency detection patterns
export const EMERGENCY_PATTERNS = [
    'difficulty breathing',
    'not breathing',
    'unconscious',
    'seizure',
    'severe bleeding',
    'hit by car',
    'poisoning',
    'bloat',
    'unable to urinate',
    'severe pain',
    'collapse',
    'emergency'
];

export const detectEmergency = (symptoms) => {
    const lowerSymptoms = symptoms.toLowerCase();
    return EMERGENCY_PATTERNS.some(pattern => lowerSymptoms.includes(pattern));
};

// Species-specific AI configurations
export const SPECIES_CONFIGS = {
    dog: {
        commonConditions: ['allergies', 'arthritis', 'dental disease', 'obesity'],
        emergencySymptoms: ['bloat', 'difficulty breathing', 'seizures'],
        ageFactors: {
            puppy: 'Consider vaccination schedule and growth monitoring',
            adult: 'Focus on preventive care and early disease detection',
            senior: 'Emphasize geriatric care and chronic disease management'
        }
    },
    cat: {
        commonConditions: ['urinary issues', 'dental disease', 'kidney disease'],
        emergencySymptoms: ['urinary blockage', 'difficulty breathing', 'hiding'],
        ageFactors: {
            kitten: 'Consider vaccination schedule and spay/neuter timing',
            adult: 'Focus on preventive care and dental health',
            senior: 'Emphasize kidney function and hyperthyroidism screening'
        }
    },
    bird: {
        commonConditions: ['respiratory infections', 'feather plucking', 'egg binding'],
        emergencySymptoms: ['difficulty breathing', 'sitting on bottom of cage', 'bleeding'],
        specialConsiderations: 'Birds hide illness well - subtle changes may indicate serious problems'
    },
    rabbit: {
        commonConditions: ['dental problems', 'GI stasis', 'respiratory infections'],
        emergencySymptoms: ['not eating', 'not producing feces', 'difficulty breathing'],
        specialConsiderations: 'Rabbits are prey animals and may hide symptoms until severely ill'
    }
};

export default {
    openai,
    AI_CONFIG,
    validateAIResponse,
    applySafetyFilters,
    trackAIUsage,
    detectEmergency,
    EMERGENCY_PATTERNS,
    SPECIES_CONFIGS
};
