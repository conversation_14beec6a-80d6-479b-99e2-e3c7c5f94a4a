import Redis from 'ioredis';
import { REDIS_CONFIG, NODE_ENV } from './env.js';

class RedisConnection {
  constructor() {
    this.client = null;
    this.isConnected = false;
  }

  async connect() {
    try {
      this.client = new Redis(REDIS_CONFIG);

      this.client.on('connect', () => {
        console.log('🔴 Redis connected successfully');
        this.isConnected = true;
      });

      this.client.on('error', (err) => {
        console.error('❌ Redis connection error:', err);
        this.isConnected = false;
      });

      this.client.on('close', () => {
        console.log('🔴 Redis connection closed');
        this.isConnected = false;
      });

      this.client.on('reconnecting', () => {
        console.log('🔄 Redis reconnecting...');
      });

      // Test the connection
      await this.client.ping();
      console.log('🔴 Redis ping successful');

      return this.client;
    } catch (error) {
      console.error('❌ Failed to connect to Redis:', error);
      if (NODE_ENV === 'production') {
        throw error;
      }
      // In development, continue without Redis
      console.warn('⚠️  Continuing without Redis in development mode');
      return null;
    }
  }

  getClient() {
    return this.client;
  }

  isHealthy() {
    return this.isConnected && this.client;
  }

  async disconnect() {
    if (this.client) {
      await this.client.quit();
      this.client = null;
      this.isConnected = false;
    }
  }
}

// Create singleton instance
const redisConnection = new RedisConnection();

export default redisConnection;
export { redisConnection };
