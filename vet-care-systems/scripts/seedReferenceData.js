import mongoose from 'mongoose';
import { DB_URI } from '../config/env.js';

/**
 * Reference Data Seeding Script
 * 
 * Seeds the database with essential reference data:
 * - Species (with auto-increment IDs starting from 1001)
 * - Breeds (with auto-increment IDs starting from 1001)
 * - Appointment Categories (with auto-increment IDs starting from 1001)
 * - Services for each category
 */

// Import models
import '../models/species.model.js';
import '../models/breed.model.js';
import '../models/appointmentCategory.model.js';

const Species = mongoose.model('Species');
const Breed = mongoose.model('Breed');
const AppointmentCategory = mongoose.model('AppointmentCategory');

// Reference data
const speciesData = [
    {
        speciesName: "Dog",
        description: "Domestic dog - loyal companion animal",
        status: 1
    },
    {
        speciesName: "Cat", 
        description: "Domestic cat - independent feline companion",
        status: 1
    },
    {
        speciesName: "Bird",
        description: "Various bird species kept as pets",
        status: 1
    },
    {
        speciesName: "Rabbit",
        description: "Domestic rabbit - small mammal pet",
        status: 1
    },
    {
        speciesName: "Hamster",
        description: "Small rodent pet",
        status: 1
    },
    {
        speciesName: "Guinea Pig",
        description: "Small social rodent pet",
        status: 1
    },
    {
        speciesName: "Fish",
        description: "Various aquatic pets",
        status: 1
    },
    {
        speciesName: "Reptile",
        description: "Various reptilian pets",
        status: 1
    }
];

const appointmentCategoriesData = [
    {
        name: "General Checkup",
        description: "Routine health examination and wellness check",
        icon: "stethoscope",
        color: "#3B82F6",
        displayOrder: 1,
        isActive: true
    },
    {
        name: "Vaccination",
        description: "Preventive vaccination services",
        icon: "syringe",
        color: "#10B981",
        displayOrder: 2,
        isActive: true
    },
    {
        name: "Dental Care",
        description: "Dental cleaning and oral health services",
        icon: "tooth",
        color: "#F59E0B",
        displayOrder: 3,
        isActive: true
    },
    {
        name: "Surgery",
        description: "Surgical procedures and operations",
        icon: "scalpel",
        color: "#EF4444",
        displayOrder: 4,
        isActive: true
    },
    {
        name: "Emergency",
        description: "Emergency and urgent care services",
        icon: "emergency",
        color: "#DC2626",
        displayOrder: 5,
        isActive: true
    },
    {
        name: "Grooming",
        description: "Pet grooming and hygiene services",
        icon: "scissors",
        color: "#8B5CF6",
        displayOrder: 6,
        isActive: true
    },
    {
        name: "Laboratory",
        description: "Diagnostic tests and laboratory services",
        icon: "flask",
        color: "#06B6D4",
        displayOrder: 7,
        isActive: true
    },
    {
        name: "Imaging",
        description: "X-ray, ultrasound and imaging services",
        icon: "camera",
        color: "#84CC16",
        displayOrder: 8,
        isActive: true
    }
];

const seedReferenceData = async () => {
    try {
        console.log('🌱 Starting reference data seeding...');

        // Connect to MongoDB
        await mongoose.connect(DB_URI);
        console.log('✅ Connected to MongoDB');

        // Verify models are loaded
        console.log('📋 Available models:', mongoose.modelNames());
        
        // Check existing data first
        const existingSpecies = await Species.countDocuments();
        const existingBreeds = await Breed.countDocuments();
        const existingCategories = await AppointmentCategory.countDocuments();

        console.log(`📊 Current data counts:`);
        console.log(`   Species: ${existingSpecies}`);
        console.log(`   Breeds: ${existingBreeds}`);
        console.log(`   Categories: ${existingCategories}`);

        if (existingSpecies > 0 || existingBreeds > 0 || existingCategories > 0) {
            console.log('🧹 Clearing existing reference data...');
            await Species.deleteMany({});
            await Breed.deleteMany({});
            await AppointmentCategory.deleteMany({});
        } else {
            console.log('📝 No existing data found, proceeding with seeding...');
        }
        
        // Seed Species
        console.log('🐾 Seeding species data...');
        const createdSpecies = await Species.insertMany(speciesData);
        console.log(`✅ Created ${createdSpecies.length} species`);
        
        // Seed Appointment Categories
        console.log('📅 Seeding appointment categories...');
        const createdCategories = await AppointmentCategory.insertMany(appointmentCategoriesData);
        console.log(`✅ Created ${createdCategories.length} appointment categories`);
        
        // Seed Breeds (basic breeds for dogs and cats)
        console.log('🐕 Seeding breed data...');
        const dogSpecies = createdSpecies.find(s => s.speciesName === 'Dog');
        const catSpecies = createdSpecies.find(s => s.speciesName === 'Cat');
        
        const breedData = [
            // Dog breeds
            { speciesId: dogSpecies.speciesId, breedName: "Labrador Retriever", sizeCategory: "large", lifespan: 12 },
            { speciesId: dogSpecies.speciesId, breedName: "Golden Retriever", sizeCategory: "large", lifespan: 12 },
            { speciesId: dogSpecies.speciesId, breedName: "German Shepherd", sizeCategory: "large", lifespan: 11 },
            { speciesId: dogSpecies.speciesId, breedName: "Bulldog", sizeCategory: "medium", lifespan: 10 },
            { speciesId: dogSpecies.speciesId, breedName: "Poodle", sizeCategory: "medium", lifespan: 14 },
            { speciesId: dogSpecies.speciesId, breedName: "Beagle", sizeCategory: "medium", lifespan: 13 },
            { speciesId: dogSpecies.speciesId, breedName: "Chihuahua", sizeCategory: "small", lifespan: 15 },
            { speciesId: dogSpecies.speciesId, breedName: "Yorkshire Terrier", sizeCategory: "small", lifespan: 14 },
            
            // Cat breeds
            { speciesId: catSpecies.speciesId, breedName: "Persian", sizeCategory: "medium", lifespan: 15 },
            { speciesId: catSpecies.speciesId, breedName: "Siamese", sizeCategory: "medium", lifespan: 16 },
            { speciesId: catSpecies.speciesId, breedName: "Maine Coon", sizeCategory: "large", lifespan: 14 },
            { speciesId: catSpecies.speciesId, breedName: "British Shorthair", sizeCategory: "medium", lifespan: 15 },
            { speciesId: catSpecies.speciesId, breedName: "Ragdoll", sizeCategory: "large", lifespan: 15 },
            { speciesId: catSpecies.speciesId, breedName: "Domestic Shorthair", sizeCategory: "medium", lifespan: 15 }
        ];
        
        const createdBreeds = await Breed.insertMany(breedData);
        console.log(`✅ Created ${createdBreeds.length} breeds`);
        
        console.log('🎉 Reference data seeding completed successfully!');
        console.log('\n📊 Summary:');
        console.log(`   Species: ${createdSpecies.length}`);
        console.log(`   Breeds: ${createdBreeds.length}`);
        console.log(`   Appointment Categories: ${createdCategories.length}`);
        
        // Display ID ranges
        console.log('\n🔢 Auto-increment ID ranges:');
        console.log(`   Species IDs: ${createdSpecies[0]?.speciesId} - ${createdSpecies[createdSpecies.length-1]?.speciesId}`);
        console.log(`   Breed IDs: ${createdBreeds[0]?.breedId} - ${createdBreeds[createdBreeds.length-1]?.breedId}`);
        console.log(`   Category IDs: ${createdCategories[0]?.appointmentCategoryId} - ${createdCategories[createdCategories.length-1]?.appointmentCategoryId}`);
        
    } catch (error) {
        console.error('❌ Error seeding reference data:', error);
        throw error;
    } finally {
        await mongoose.disconnect();
        console.log('📴 Disconnected from MongoDB');
    }
};

// Run the seeding if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    seedReferenceData()
        .then(() => {
            console.log('🎉 Reference data seeding completed');
            process.exit(0);
        })
        .catch((error) => {
            console.error('💥 Reference data seeding failed:', error);
            process.exit(1);
        });
}

export default seedReferenceData;
