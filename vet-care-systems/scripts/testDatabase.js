import mongoose from 'mongoose';
import { DB_URI } from '../config/env.js';

// Import models
import '../models/species.model.js';
import '../models/breed.model.js';
import '../models/appointmentCategory.model.js';

const Species = mongoose.model('Species');
const Breed = mongoose.model('Breed');
const AppointmentCategory = mongoose.model('AppointmentCategory');

/**
 * Test Database Script
 * 
 * Tests the database to verify:
 * 1. Auto-increment IDs are working
 * 2. Data was seeded correctly
 * 3. No _id fields in responses
 */

const testDatabase = async () => {
    try {
        console.log('🧪 Testing database...');
        
        // Connect to MongoDB
        await mongoose.connect(DB_URI);
        console.log('✅ Connected to MongoDB');
        
        // Test Species
        console.log('\n🐾 Testing Species:');
        const species = await Species.find({}).select('speciesId speciesName description status').lean();
        console.log(`Found ${species.length} species:`);
        species.forEach(s => {
            console.log(`  - ID: ${s.speciesId}, Name: ${s.speciesName}, Status: ${s.status}`);
        });
        
        // Test Breeds
        console.log('\n🐕 Testing Breeds:');
        const breeds = await Breed.find({}).select('breedId breedName speciesId sizeCategory').lean();
        console.log(`Found ${breeds.length} breeds:`);
        breeds.slice(0, 5).forEach(b => {
            console.log(`  - ID: ${b.breedId}, Name: ${b.breedName}, Species: ${b.speciesId}, Size: ${b.sizeCategory}`);
        });
        if (breeds.length > 5) {
            console.log(`  ... and ${breeds.length - 5} more`);
        }
        
        // Test Appointment Categories
        console.log('\n📅 Testing Appointment Categories:');
        const categories = await AppointmentCategory.find({}).select('appointmentCategoryId name description isActive').lean();
        console.log(`Found ${categories.length} categories:`);
        categories.forEach(c => {
            console.log(`  - ID: ${c.appointmentCategoryId}, Name: ${c.name}, Active: ${c.isActive}`);
        });
        
        // Test ID ranges
        console.log('\n🔢 ID Ranges:');
        if (species.length > 0) {
            const speciesIds = species.map(s => s.speciesId).sort((a, b) => a - b);
            console.log(`  Species: ${speciesIds[0]} - ${speciesIds[speciesIds.length - 1]}`);
        }
        
        if (breeds.length > 0) {
            const breedIds = breeds.map(b => b.breedId).sort((a, b) => a - b);
            console.log(`  Breeds: ${breedIds[0]} - ${breedIds[breedIds.length - 1]}`);
        }
        
        if (categories.length > 0) {
            const categoryIds = categories.map(c => c.appointmentCategoryId).sort((a, b) => a - b);
            console.log(`  Categories: ${categoryIds[0]} - ${categoryIds[categoryIds.length - 1]}`);
        }
        
        // Verify no _id fields in lean queries
        console.log('\n🔍 Checking for _id fields:');
        const hasSpeciesId = species.some(s => s._id !== undefined);
        const hasBreedId = breeds.some(b => b._id !== undefined);
        const hasCategoryId = categories.some(c => c._id !== undefined);
        
        console.log(`  Species has _id: ${hasSpeciesId}`);
        console.log(`  Breeds has _id: ${hasBreedId}`);
        console.log(`  Categories has _id: ${hasCategoryId}`);
        
        if (!hasSpeciesId && !hasBreedId && !hasCategoryId) {
            console.log('✅ No _id fields found in lean queries - Good!');
        } else {
            console.log('⚠️ Some _id fields found - this is expected with lean() but should be excluded in API responses');
        }
        
        console.log('\n🎉 Database test completed successfully!');
        
    } catch (error) {
        console.error('❌ Error testing database:', error);
        throw error;
    } finally {
        await mongoose.disconnect();
        console.log('📴 Disconnected from MongoDB');
    }
};

// Run the test if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    testDatabase()
        .then(() => {
            console.log('🎉 Database test completed');
            process.exit(0);
        })
        .catch((error) => {
            console.error('💥 Database test failed:', error);
            process.exit(1);
        });
}

export default testDatabase;
