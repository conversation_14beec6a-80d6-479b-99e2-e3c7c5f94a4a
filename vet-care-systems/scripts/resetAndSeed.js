import resetDatabase from './resetDatabase.js';
import seedReferenceData from './seedReferenceData.js';

/**
 * Master Reset and Seed Script
 * 
 * This script performs a complete database reset and seeding:
 * 1. Drops the entire database
 * 2. Seeds reference data with auto-increment IDs
 * 3. Creates default admin user
 */

const resetAndSeed = async () => {
    try {
        console.log('🚀 Starting complete database reset and seeding...\n');
        
        // Step 1: Reset database
        console.log('📋 Step 1: Resetting database...');
        await resetDatabase();
        console.log('✅ Database reset completed\n');
        
        // Step 2: Seed reference data
        console.log('📋 Step 2: Seeding reference data...');
        await seedReferenceData();
        console.log('✅ Reference data seeding completed\n');
        
        console.log('🎉 Complete database reset and seeding finished successfully!');
        console.log('\n📝 Next steps:');
        console.log('   1. Start the server: npm start');
        console.log('   2. Create default admin user via API or script');
        console.log('   3. Test auto-increment functionality');
        console.log('   4. Verify API responses return minimal data');
        
    } catch (error) {
        console.error('💥 Reset and seed process failed:', error);
        throw error;
    }
};

// Run the process if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    resetAndSeed()
        .then(() => {
            console.log('🎉 Reset and seed process completed');
            process.exit(0);
        })
        .catch((error) => {
            console.error('💥 Reset and seed process failed:', error);
            process.exit(1);
        });
}

export default resetAndSeed;
