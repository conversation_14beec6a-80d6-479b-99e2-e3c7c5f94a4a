import mongoose from 'mongoose';
import { DB_URI } from '../config/env.js';

// Import models
import '../models/species.model.js';
import '../models/breed.model.js';
import '../models/appointmentCategory.model.js';

const Species = mongoose.model('Species');
const Breed = mongoose.model('Breed');
const AppointmentCategory = mongoose.model('AppointmentCategory');

const seedData = async () => {
    console.log('🌱 Starting simple seed...');
    
    await mongoose.connect(DB_URI);
    console.log('✅ Connected to MongoDB');
    
    // Clear existing data
    await Species.deleteMany({});
    await Breed.deleteMany({});
    await AppointmentCategory.deleteMany({});
    console.log('🧹 Cleared existing data');
    
    // Create species
    console.log('🐾 Creating species...');
    const speciesData = [
        { speciesName: "Dog", description: "Domestic dog - loyal companion animal", status: 1 },
        { speciesName: "Cat", description: "Domestic cat - independent feline companion", status: 1 },
        { speciesName: "Bird", description: "Various bird species kept as pets", status: 1 },
        { speciesName: "Rabbit", description: "Domestic rabbit - small mammal pet", status: 1 }
    ];
    
    const createdSpecies = [];
    for (const data of speciesData) {
        const species = await Species.create(data);
        createdSpecies.push(species);
        console.log(`  Created: ${species.speciesId} - ${species.speciesName}`);
    }
    
    // Create appointment categories
    console.log('📅 Creating appointment categories...');
    const categoryData = [
        { name: "General Checkup", description: "Routine health examination", isActive: true, createdBy: 1000 },
        { name: "Vaccination", description: "Preventive vaccination services", isActive: true, createdBy: 1000 },
        { name: "Dental Care", description: "Dental cleaning and oral health", isActive: true, createdBy: 1000 },
        { name: "Surgery", description: "Surgical procedures", isActive: true, createdBy: 1000 }
    ];
    
    const createdCategories = [];
    for (const data of categoryData) {
        const category = await AppointmentCategory.create(data);
        createdCategories.push(category);
        console.log(`  Created: ${category.appointmentCategoryId} - ${category.name}`);
    }
    
    // Create breeds
    console.log('🐕 Creating breeds...');
    const dogSpecies = createdSpecies.find(s => s.speciesName === 'Dog');
    const catSpecies = createdSpecies.find(s => s.speciesName === 'Cat');
    
    const breedData = [
        { speciesId: dogSpecies.speciesId, breedName: "Labrador Retriever", sizeCategory: "large" },
        { speciesId: dogSpecies.speciesId, breedName: "Golden Retriever", sizeCategory: "large" },
        { speciesId: dogSpecies.speciesId, breedName: "Bulldog", sizeCategory: "medium" },
        { speciesId: catSpecies.speciesId, breedName: "Persian", sizeCategory: "medium" },
        { speciesId: catSpecies.speciesId, breedName: "Siamese", sizeCategory: "medium" }
    ];
    
    const createdBreeds = [];
    for (const data of breedData) {
        const breed = await Breed.create(data);
        createdBreeds.push(breed);
        console.log(`  Created: ${breed.breedId} - ${breed.breedName} (Species: ${breed.speciesId})`);
    }
    
    console.log('🎉 Seeding completed!');
    console.log(`📊 Summary: ${createdSpecies.length} species, ${createdBreeds.length} breeds, ${createdCategories.length} categories`);
    
    await mongoose.disconnect();
    console.log('📴 Disconnected');
};

seedData().catch(console.error);
