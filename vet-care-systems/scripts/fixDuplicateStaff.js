import mongoose from 'mongoose';
import { DB_URI } from '../config/env.js';
import '../models/index.js';

const Appointment = mongoose.model('Appointment');

async function fixDuplicateStaffAssignments() {
  try {
    await mongoose.connect(DB_URI);
    console.log('Connected to MongoDB');

    // Get all appointments
    const appointments = await Appointment.find({}).lean();
    
    console.log(`\nChecking ${appointments.length} appointments for duplicate staff assignments...\n`);
    
    let fixedCount = 0;
    
    for (const appointment of appointments) {
      let needsUpdate = false;
      const updatedCategories = appointment.appointmentCategories?.map(category => {
        const hasAssigned = category.staffAssigned && category.staffAssignedName;
        const hasPerformed = category.performedBy && category.performedByName;
        const isDuplicate = hasAssigned && hasPerformed && 
                           category.staffAssigned === category.performedBy &&
                           category.staffAssignedName === category.performedByName;
        
        console.log(`Appointment ${appointment.appointmentId} (${appointment.petName}):`);
        console.log(`  Category: ${category.categoryName}`);
        console.log(`  Status: ${category.categoryStatus}`);
        
        if (hasAssigned) {
          console.log(`  Assigned: ${category.staffAssigned} (${category.staffAssignedName})`);
        }
        if (hasPerformed) {
          console.log(`  Performed: ${category.performedBy} (${category.performedByName})`);
        }
        
        // Fix logic: Only keep performedBy for completed categories
        if (isDuplicate) {
          console.log(`  ⚠️  DUPLICATE DETECTED!`);
          
          if (category.categoryStatus === 'completed') {
            // For completed categories, keep performedBy and remove staffAssigned
            console.log(`  ✅ Fixing: Removing staffAssigned, keeping performedBy`);
            needsUpdate = true;
            return {
              ...category,
              staffAssigned: undefined,
              staffAssignedName: undefined
            };
          } else {
            // For non-completed categories, keep staffAssigned and remove performedBy
            console.log(`  ✅ Fixing: Removing performedBy, keeping staffAssigned`);
            needsUpdate = true;
            return {
              ...category,
              performedBy: undefined,
              performedByName: undefined
            };
          }
        }
        
        console.log(`  ✓ No duplicates found`);
        return category;
      });
      
      if (needsUpdate) {
        await Appointment.findOneAndUpdate(
          { appointmentId: appointment.appointmentId },
          { appointmentCategories: updatedCategories },
          { new: true }
        );
        fixedCount++;
        console.log(`  📝 Updated appointment ${appointment.appointmentId}\n`);
      } else {
        console.log(`  ✓ No changes needed\n`);
      }
    }
    
    console.log(`\n🎉 Fixed ${fixedCount} appointments with duplicate staff assignments`);
    
    // Verify the fix
    console.log('\n--- Verification ---');
    const verifyAppointments = await Appointment.find({}).lean();
    
    let duplicatesFound = 0;
    verifyAppointments.forEach(apt => {
      apt.appointmentCategories?.forEach(cat => {
        const hasAssigned = cat.staffAssigned && cat.staffAssignedName;
        const hasPerformed = cat.performedBy && cat.performedByName;
        const isDuplicate = hasAssigned && hasPerformed && 
                           cat.staffAssigned === cat.performedBy &&
                           cat.staffAssignedName === cat.performedByName;
        
        if (isDuplicate) {
          duplicatesFound++;
          console.log(`❌ Still duplicate in appointment ${apt.appointmentId}`);
        }
      });
    });
    
    if (duplicatesFound === 0) {
      console.log('✅ All duplicates have been fixed!');
    } else {
      console.log(`❌ ${duplicatesFound} duplicates still remain`);
    }

    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  } catch (error) {
    console.error('Error fixing duplicate staff assignments:', error);
    await mongoose.disconnect();
  }
}

fixDuplicateStaffAssignments();
