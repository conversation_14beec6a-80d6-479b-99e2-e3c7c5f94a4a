import mongoose from 'mongoose';
import { DB_URI } from '../config/env.js';
import '../models/index.js';

const Appointment = mongoose.model('Appointment');

async function verifyStaffFix() {
  try {
    await mongoose.connect(DB_URI);
    console.log('Connected to MongoDB');
    
    const appointments = await Appointment.find({}).lean();
    
    console.log('\n=== VERIFICATION: Staff Assignment Status ===\n');
    
    appointments.forEach(apt => {
      console.log(`📋 Appointment ${apt.appointmentId} (${apt.petName}) - Status: ${apt.status}`);
      
      apt.appointmentCategories?.forEach((cat, index) => {
        console.log(`  📂 Category: ${cat.categoryName} (Status: ${cat.categoryStatus})`);
        
        if (cat.staffAssigned && cat.staffAssignedName) {
          console.log(`    👤 Assigned: ${cat.staffAssigned} (${cat.staffAssignedName})`);
        }
        
        if (cat.performedBy && cat.performedByName) {
          console.log(`    ✅ Performed: ${cat.performedBy} (${cat.performedByName})`);
        }
        
        if (!cat.staffAssigned && !cat.performedBy) {
          console.log(`    ⚠️  No staff assigned or performed`);
        }
        
        console.log('');
      });
    });
    
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  } catch (error) {
    console.error('Error:', error);
    await mongoose.disconnect();
  }
}

verifyStaffFix();
