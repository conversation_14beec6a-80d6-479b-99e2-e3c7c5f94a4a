import fetch from 'node-fetch';

async function testHealthRecordsEndpoint() {
  try {
    console.log('Testing health records endpoint...\n');
    
    // Test the new endpoint for appointment 3010
    const appointmentId = 3010;
    const url = `http://localhost:5500/api/health-records/appointment/${appointmentId}`;
    
    console.log(`Testing: GET ${url}`);
    
    // Note: In a real scenario, you would need to include authentication headers
    // For testing purposes, we'll see what happens without auth
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        // 'Authorization': 'Bearer your-token-here' // Would be needed in real scenario
      }
    });
    
    console.log(`Status: ${response.status} ${response.statusText}`);
    
    const data = await response.text();
    console.log('Response:', data);
    
    if (response.status === 401) {
      console.log('\n✅ Endpoint exists but requires authentication (expected)');
      console.log('The route /api/health-records/appointment/:appointmentId is working!');
    } else if (response.status === 200) {
      console.log('\n✅ Endpoint working and returned data');
    } else {
      console.log('\n❌ Unexpected response');
    }
    
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.log('❌ Server is not running on port 5500');
    } else {
      console.log('❌ Error:', error.message);
    }
  }
}

testHealthRecordsEndpoint();
