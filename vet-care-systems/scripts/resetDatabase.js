import mongoose from 'mongoose';
import { DB_URI } from '../config/env.js';

/**
 * Database Reset Script
 * 
 * This script completely drops the database and recreates all collections
 * with proper auto-increment IDs starting from 1000+
 */

const resetDatabase = async () => {
    try {
        console.log('🔄 Starting database reset...');
        
        // Connect to MongoDB
        await mongoose.connect(DB_URI);
        console.log('✅ Connected to MongoDB');
        
        // Get database name
        const dbName = mongoose.connection.db.databaseName;
        console.log(`📋 Database: ${dbName}`);
        
        // Drop entire database
        await mongoose.connection.db.dropDatabase();
        console.log('🗑️ Database dropped successfully');
        
        // Disconnect and reconnect to ensure clean state
        await mongoose.disconnect();
        await mongoose.connect(DB_URI);
        console.log('🔄 Reconnected to clean database');
        
        console.log('✅ Database reset completed successfully');
        console.log('📝 Next steps:');
        console.log('   1. Run seed scripts to populate reference data');
        console.log('   2. Create default admin user');
        console.log('   3. Test auto-increment functionality');
        
    } catch (error) {
        console.error('❌ Error resetting database:', error);
        throw error;
    } finally {
        await mongoose.disconnect();
        console.log('📴 Disconnected from MongoDB');
    }
};

// Run the reset if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    resetDatabase()
        .then(() => {
            console.log('🎉 Database reset completed');
            process.exit(0);
        })
        .catch((error) => {
            console.error('💥 Database reset failed:', error);
            process.exit(1);
        });
}

export default resetDatabase;
