import mongoose from 'mongoose';
import { DB_URI } from '../config/env.js';
import '../models/index.js'; // Import all models

const Appointment = mongoose.model('Appointment');

async function createSampleAppointments() {
  try {
    await mongoose.connect(DB_URI);
    console.log('Connected to MongoDB');

    // Clear any existing appointments first
    await Appointment.deleteMany({});
    console.log('Cleared existing appointments');

    const sampleAppointments = [
      {
        appointmentId: 1001,
        clinicId: 1001,
        clinicName: "Happy Paws Veterinary Clinic",
        clientId: 1001,
        clientName: "<PERSON>",
        clientPhone: "+254712345678",
        clientEmail: "<EMAIL>",
        petId: 1001,
        petName: "Buddy",
        petSpecies: "Dog",
        petBreed: "Golden Retriever",
        petAge: 24,
        petWeight: 25.5,
        petGender: "male",
        appointmentDate: new Date('2024-01-15T10:00:00Z'),
        appointmentTime: "10:00",
        status: "scheduled",
        completionStatus: "not_started",
        staffInCharge: 1001,
        staffInChargeName: "<PERSON>. <PERSON>",
        staffInChargeJobTitle: "Veterinarian",
        appointmentType: "checkup",
        priority: "normal",
        appointmentCategories: [
          {
            appointmentCategoryId: 1001,
            categoryName: "General Checkup",
            estimatedDuration: 30,
            priority: "normal",
            requiresEquipment: false,
            requiresQualification: true,
            categoryServices: [
              {
                categoryServiceId: 1001,
                categoryServiceName: "Physical Examination",
                price: 500,
                currency: "KES",
                status: "pending",
                isCompleted: false
              }
            ],
            categoryStatus: "not_started",
            isCompleted: false,
            staffAssigned: 1001,
            staffAssignedName: "Dr. Sarah Johnson"
          }
        ],
        createdBy: 1001,
        updatedBy: 1001
      },
      {
        appointmentId: 1002,
        clinicId: 1001,
        clinicName: "Happy Paws Veterinary Clinic",
        clientId: 1002,
        clientName: "Mary Johnson",
        clientPhone: "+254723456789",
        clientEmail: "<EMAIL>",
        petId: 1002,
        petName: "Whiskers",
        petSpecies: "Cat",
        petBreed: "Persian",
        petAge: 36,
        petWeight: 4.2,
        petGender: "female",
        appointmentDate: new Date('2024-01-16T14:30:00Z'),
        appointmentTime: "14:30",
        status: "in_progress",
        completionStatus: "in_progress",
        staffInCharge: 1002,
        staffInChargeName: "Dr. Michael Brown",
        staffInChargeJobTitle: "Veterinarian",
        appointmentType: "grooming",
        priority: "normal",
        appointmentCategories: [
          {
            appointmentCategoryId: 1003,
            categoryName: "Grooming",
            estimatedDuration: 60,
            priority: "normal",
            requiresEquipment: true,
            requiresQualification: false,
            categoryServices: [
              {
                categoryServiceId: 1087,
                categoryServiceName: "Nails",
                price: 120,
                currency: "KES",
                status: "completed",
                notes: "Nails completed successfully. Patient responded well to treatment.",
                isCompleted: true,
                completedAt: new Date('2024-01-16T15:00:00Z')
              },
              {
                categoryServiceId: 1088,
                categoryServiceName: "Bath & Dry",
                price: 300,
                currency: "KES",
                status: "in_progress",
                isCompleted: false
              }
            ],
            categoryStatus: "in_progress",
            isCompleted: false,
            staffAssigned: 1003,
            staffAssignedName: "Jane Wilson"
          }
        ],
        createdBy: 1002,
        updatedBy: 1002
      },
      {
        appointmentId: 1003,
        clinicId: 1001,
        clientId: 1003,
        petId: 1003,
        petName: "Max",
        petSpecies: "Dog",
        petBreed: "German Shepherd",
        clientName: "Robert Davis",
        clientPhone: "+254734567890",
        appointmentDate: new Date('2024-01-17T09:00:00Z'),
        appointmentTime: "09:00",
        status: "completed",
        completionStatus: "completed",
        staffInCharge: 1001,
        staffInChargeName: "Dr. Sarah Johnson",
        appointmentType: "vaccination",
        priority: "high",
        appointmentCategories: [
          {
            appointmentCategoryId: 1002,
            categoryName: "Vaccination",
            estimatedDuration: 20,
            priority: "high",
            requiresEquipment: false,
            requiresQualification: true,
            categoryServices: [
              {
                categoryServiceId: 1045,
                categoryServiceName: "Rabies Vaccine",
                price: 800,
                currency: "KES",
                status: "completed",
                notes: "Rabies vaccination administered successfully. No adverse reactions observed.",
                isCompleted: true,
                completedAt: new Date('2024-01-17T09:15:00Z')
              }
            ],
            categoryStatus: "completed",
            isCompleted: true,
            completedAt: new Date('2024-01-17T09:20:00Z'),
            staffAssigned: 1001,
            staffAssignedName: "Dr. Sarah Johnson",
            performedBy: 1001,
            performedByName: "Dr. Sarah Johnson"
          }
        ],
        generalNotes: "Vaccination completed successfully. Next vaccination due in 12 months.",
        recommendations: "Keep pet calm for 24 hours. Monitor for any unusual reactions.",
        createdBy: 1001,
        updatedBy: 1001
      },
      {
        appointmentId: 1004,
        clinicId: 1002,
        clientId: 1004,
        petId: 1004,
        petName: "Luna",
        petSpecies: "Cat",
        petBreed: "Siamese",
        clientName: "Emily Wilson",
        clientPhone: "+254745678901",
        appointmentDate: new Date('2024-01-18T11:00:00Z'),
        appointmentTime: "11:00",
        status: "scheduled",
        completionStatus: "pending",
        staffInCharge: 1004,
        staffInChargeName: "Dr. James Miller",
        appointmentType: "dental",
        priority: "normal",
        appointmentCategories: [
          {
            appointmentCategoryId: 1004,
            categoryName: "Dental Care",
            estimatedDuration: 45,
            priority: "normal",
            requiresEquipment: true,
            requiresQualification: true,
            categoryServices: [
              {
                categoryServiceId: 1120,
                categoryServiceName: "Dental Cleaning",
                price: 1200,
                currency: "KES",
                status: "scheduled",
                isCompleted: false
              }
            ],
            categoryStatus: "scheduled",
            isCompleted: false,
            staffAssigned: 1004,
            staffAssignedName: "Dr. James Miller"
          }
        ],
        createdBy: 1004,
        updatedBy: 1004
      },
      {
        appointmentId: 1005,
        clinicId: 1001,
        clientId: 1005,
        petId: 1005,
        petName: "Charlie",
        petSpecies: "Dog",
        petBreed: "Labrador",
        clientName: "David Brown",
        clientPhone: "+254756789012",
        appointmentDate: new Date('2024-01-19T16:00:00Z'),
        appointmentTime: "16:00",
        status: "completed",
        completionStatus: "completed",
        staffInCharge: 1002,
        staffInChargeName: "Dr. Michael Brown",
        appointmentType: "surgery",
        priority: "high",
        appointmentCategories: [
          {
            appointmentCategoryId: 1005,
            categoryName: "Surgery",
            estimatedDuration: 120,
            priority: "high",
            requiresEquipment: true,
            requiresQualification: true,
            categoryServices: [
              {
                categoryServiceId: 1150,
                categoryServiceName: "Spay/Neuter",
                price: 3500,
                currency: "KES",
                status: "completed",
                notes: "Surgery completed successfully. Patient recovered well from anesthesia.",
                isCompleted: true,
                completedAt: new Date('2024-01-19T18:00:00Z')
              }
            ],
            categoryStatus: "completed",
            isCompleted: true,
            completedAt: new Date('2024-01-19T18:00:00Z'),
            staffAssigned: 1002,
            staffAssignedName: "Dr. Michael Brown",
            performedBy: 1002,
            performedByName: "Dr. Michael Brown"
          }
        ],
        generalNotes: "Spay surgery completed successfully. Patient is recovering well.",
        recommendations: "Keep pet quiet for 7-10 days. Return for suture removal in 10 days.",
        createdBy: 1002,
        updatedBy: 1002
      },
      {
        appointmentId: 1006,
        clinicId: 1002,
        clientId: 1006,
        petId: 1006,
        petName: "Bella",
        petSpecies: "Dog",
        petBreed: "Poodle",
        clientName: "Lisa Anderson",
        clientPhone: "+254767890123",
        appointmentDate: new Date('2024-01-20T13:30:00Z'),
        appointmentTime: "13:30",
        status: "in_progress",
        completionStatus: "in_progress",
        staffInCharge: 1005,
        staffInChargeName: "Dr. Emma Davis",
        appointmentType: "emergency",
        priority: "urgent",
        appointmentCategories: [
          {
            appointmentCategoryId: 1006,
            categoryName: "Emergency Care",
            estimatedDuration: 90,
            priority: "urgent",
            requiresEquipment: true,
            requiresQualification: true,
            categoryServices: [
              {
                categoryServiceId: 1200,
                categoryServiceName: "Emergency Examination",
                price: 1500,
                currency: "KES",
                status: "completed",
                notes: "Emergency examination completed. Diagnosed with gastric torsion.",
                isCompleted: true,
                completedAt: new Date('2024-01-20T14:00:00Z')
              },
              {
                categoryServiceId: 1201,
                categoryServiceName: "Emergency Surgery",
                price: 5000,
                currency: "KES",
                status: "in_progress",
                isCompleted: false
              }
            ],
            categoryStatus: "in_progress",
            isCompleted: false,
            staffAssigned: 1005,
            staffAssignedName: "Dr. Emma Davis"
          }
        ],
        createdBy: 1005,
        updatedBy: 1005
      },
      {
        appointmentId: 1007,
        clinicId: 1001,
        clientId: 1007,
        petId: 1007,
        petName: "Milo",
        petSpecies: "Cat",
        petBreed: "Maine Coon",
        clientName: "Thomas Wilson",
        clientPhone: "+254778901234",
        appointmentDate: new Date('2024-01-21T10:30:00Z'),
        appointmentTime: "10:30",
        status: "scheduled",
        completionStatus: "pending",
        staffInCharge: 1003,
        staffInChargeName: "Jane Wilson",
        appointmentType: "grooming",
        priority: "normal",
        appointmentCategories: [
          {
            appointmentCategoryId: 1003,
            categoryName: "Grooming",
            estimatedDuration: 75,
            priority: "normal",
            requiresEquipment: true,
            requiresQualification: false,
            categoryServices: [
              {
                categoryServiceId: 1087,
                categoryServiceName: "Nails",
                price: 120,
                currency: "KES",
                status: "scheduled",
                isCompleted: false
              },
              {
                categoryServiceId: 1088,
                categoryServiceName: "Bath & Dry",
                price: 300,
                currency: "KES",
                status: "scheduled",
                isCompleted: false
              },
              {
                categoryServiceId: 1089,
                categoryServiceName: "Fur Trimming",
                price: 400,
                currency: "KES",
                status: "scheduled",
                isCompleted: false
              }
            ],
            categoryStatus: "scheduled",
            isCompleted: false,
            staffAssigned: 1003,
            staffAssignedName: "Jane Wilson"
          }
        ],
        createdBy: 1003,
        updatedBy: 1003
      },
      {
        appointmentId: 1008,
        clinicId: 1002,
        clientId: 1008,
        petId: 1008,
        petName: "Rocky",
        petSpecies: "Dog",
        petBreed: "Bulldog",
        clientName: "Jennifer Taylor",
        clientPhone: "+254789012345",
        appointmentDate: new Date('2024-01-22T15:00:00Z'),
        appointmentTime: "15:00",
        status: "completed",
        completionStatus: "completed",
        staffInCharge: 1004,
        staffInChargeName: "Dr. James Miller",
        appointmentType: "checkup",
        priority: "normal",
        appointmentCategories: [
          {
            appointmentCategoryId: 1001,
            categoryName: "General Checkup",
            estimatedDuration: 30,
            priority: "normal",
            requiresEquipment: false,
            requiresQualification: true,
            categoryServices: [
              {
                categoryServiceId: 1001,
                categoryServiceName: "Physical Examination",
                price: 500,
                currency: "KES",
                status: "completed",
                notes: "Complete physical examination performed. All vital signs normal.",
                isCompleted: true,
                completedAt: new Date('2024-01-22T15:25:00Z')
              }
            ],
            categoryStatus: "completed",
            isCompleted: true,
            completedAt: new Date('2024-01-22T15:30:00Z'),
            staffAssigned: 1004,
            staffAssignedName: "Dr. James Miller",
            performedBy: 1004,
            performedByName: "Dr. James Miller"
          }
        ],
        generalNotes: "Routine checkup completed. Pet is in excellent health.",
        recommendations: "Continue current diet and exercise routine. Next checkup in 6 months.",
        createdBy: 1004,
        updatedBy: 1004
      }
    ];

    // Insert the sample appointments
    const result = await Appointment.insertMany(sampleAppointments);
    console.log(`Created ${result.length} sample appointments`);

    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  } catch (error) {
    console.error('Error creating sample appointments:', error);
    await mongoose.disconnect();
  }
}

createSampleAppointments();
