import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import { JWT_SECRET, NODE_ENV } from '../config/env.js';
import { sendResponse } from '../utils/responseHandler.js';
import authService from '../services/auth.service.js';
import cacheService from '../services/cache.service.js';

/**
 * Enhanced Authentication Middleware with comprehensive security features
 */
class EnhancedAuthMiddleware {
  /**
   * Main authentication and authorization middleware
   * Supports multiple authentication methods and security checks
   */
  static async authenticateAndAuthorize(req, res, next) {
    try {
      // Extract authentication data
      const authData = this.extractAuthHeaders(req.headers);
      
      if (!authData.accessToken) {
        return sendResponse(res, 401, false, 'Authentication required');
      }

      // Validate timestamp (prevent replay attacks)
      if (authData.timestamp && !this.validateTimestamp(authData.timestamp)) {
        return sendResponse(res, 401, false, 'Request timestamp expired');
      }

      // Validate hash integrity if provided
      if (authData.hashKey && !this.validateHash(req.body, authData)) {
        return sendResponse(res, 401, false, 'Invalid request signature');
      }

      // Verify JWT token
      const decoded = jwt.verify(authData.accessToken, JWT_SECRET);
      
      // Check if session exists and is valid
      const session = await authService.validateSession(decoded.userId);
      if (!session) {
        return sendResponse(res, 401, false, 'Session expired or invalid');
      }

      // Rate limiting check
      const rateLimitResult = await this.checkRateLimit(req, decoded.userId);
      if (!rateLimitResult.allowed) {
        return sendResponse(res, 429, false, 'Rate limit exceeded', {
          retryAfter: rateLimitResult.resetTime
        });
      }

      // Attach user data to request
      req.user = {
        ...decoded,
        session,
        rateLimitRemaining: rateLimitResult.remaining
      };

      // Check permissions if route requires specific permissions
      if (req.route && req.route.permission) {
        const hasPermission = await authService.validateUserPermission(
          decoded.userId,
          req.route.permission,
          decoded.userType === 'staff'
        );

        if (!hasPermission) {
          return sendResponse(res, 403, false, 'Insufficient permissions');
        }
      }

      // Log successful authentication
      console.log(`🔐 Authenticated: ${decoded.email} (${decoded.userType})`);
      
      next();
    } catch (error) {
      console.error('Authentication error:', error);
      
      if (error.name === 'JsonWebTokenError') {
        return sendResponse(res, 401, false, 'Invalid token');
      } else if (error.name === 'TokenExpiredError') {
        return sendResponse(res, 401, false, 'Token expired');
      } else {
        return sendResponse(res, 500, false, 'Authentication error');
      }
    }
  }

  /**
   * Extract authentication headers
   */
  static extractAuthHeaders(headers) {
    return {
      accessToken: headers['authorization']?.split(' ')[1] || headers['x-access-token'],
      appKey: headers['x-app-key'],
      hashKey: headers['x-hash-key'],
      timestamp: headers['x-timestamp'],
      signature: headers['x-signature']
    };
  }

  /**
   * Validate timestamp to prevent replay attacks
   */
  static validateTimestamp(timestamp) {
    const now = Math.floor(Date.now() / 1000);
    const requestTime = parseInt(timestamp);
    const tolerance = NODE_ENV === 'development' ? 600 : 300; // 10 min dev, 5 min prod
    return Math.abs(now - requestTime) <= tolerance;
  }

  /**
   * Validate request hash for integrity
   */
  static validateHash(data, authData) {
    if (!authData.hashKey || !authData.signature || !authData.timestamp) {
      return false;
    }

    const payload = JSON.stringify(data) + authData.timestamp;
    const expectedHash = crypto
      .createHmac('sha256', authData.hashKey)
      .update(payload)
      .digest('hex');
    
    return crypto.timingSafeEqual(
      Buffer.from(expectedHash, 'hex'),
      Buffer.from(authData.signature, 'hex')
    );
  }

  /**
   * Rate limiting check
   */
  static async checkRateLimit(req, userId) {
    const endpoint = `${req.method}:${req.route?.path || req.path}`;
    const rateLimitKey = `rate_limit:${userId}:${endpoint}`;
    
    // Different limits for different endpoints
    const limits = {
      'POST:/api/auth/login': { limit: 5, window: 300 }, // 5 attempts per 5 minutes
      'POST:/api/appointments': { limit: 20, window: 3600 }, // 20 per hour
      'GET:/api/clients': { limit: 100, window: 3600 }, // 100 per hour
      'default': { limit: 60, window: 3600 } // 60 per hour default
    };

    const config = limits[endpoint] || limits.default;
    return await cacheService.checkRateLimit(rateLimitKey, config.limit, config.window);
  }

  /**
   * Permission-based middleware factory
   */
  static requirePermission(permission) {
    return async (req, res, next) => {
      try {
        if (!req.user) {
          return sendResponse(res, 401, false, 'Authentication required');
        }

        const hasPermission = await authService.validateUserPermission(
          req.user.userId,
          permission,
          req.user.userType === 'staff'
        );

        if (!hasPermission) {
          return sendResponse(res, 403, false, `Permission required: ${permission}`);
        }

        next();
      } catch (error) {
        console.error('Permission check error:', error);
        return sendResponse(res, 500, false, 'Permission check failed');
      }
    };
  }

  /**
   * Role-based middleware factory
   */
  static requireRole(roles) {
    const roleArray = Array.isArray(roles) ? roles : [roles];
    
    return async (req, res, next) => {
      try {
        if (!req.user) {
          return sendResponse(res, 401, false, 'Authentication required');
        }

        if (!roleArray.includes(req.user.roleName)) {
          return sendResponse(res, 403, false, `Role required: ${roleArray.join(' or ')}`);
        }

        next();
      } catch (error) {
        console.error('Role check error:', error);
        return sendResponse(res, 500, false, 'Role check failed');
      }
    };
  }

  /**
   * Clinic access middleware
   */
  static requireClinicAccess(clinicIdParam = 'clinicId') {
    return async (req, res, next) => {
      try {
        if (!req.user) {
          return sendResponse(res, 401, false, 'Authentication required');
        }

        const requestedClinicId = req.params[clinicIdParam] || req.body[clinicIdParam] || req.query[clinicIdParam];
        
        if (!requestedClinicId) {
          return sendResponse(res, 400, false, 'Clinic ID required');
        }

        // Super admin has access to all clinics
        if (req.user.roleName === 'super_admin') {
          return next();
        }

        // Staff must have access to the requested clinic
        if (req.user.userType === 'staff') {
          const hasAccess = req.user.clinicId === parseInt(requestedClinicId) ||
                           req.user.additionalClinics?.includes(parseInt(requestedClinicId));
          
          if (!hasAccess) {
            return sendResponse(res, 403, false, 'Access denied to this clinic');
          }
        }

        next();
      } catch (error) {
        console.error('Clinic access check error:', error);
        return sendResponse(res, 500, false, 'Clinic access check failed');
      }
    };
  }

  /**
   * Clinic owner middleware
   */
  static requireClinicOwner(clinicIdParam = 'clinicId') {
    return async (req, res, next) => {
      try {
        if (!req.user) {
          return sendResponse(res, 401, false, 'Authentication required');
        }

        const requestedClinicId = req.params[clinicIdParam] || req.body[clinicIdParam] || req.query[clinicIdParam];
        
        // Super admin has owner access to all clinics
        if (req.user.roleName === 'super_admin') {
          return next();
        }

        // Check if user is clinic owner
        if (!req.user.isClinicOwner || req.user.clinicId !== parseInt(requestedClinicId)) {
          return sendResponse(res, 403, false, 'Clinic owner access required');
        }

        next();
      } catch (error) {
        console.error('Clinic owner check error:', error);
        return sendResponse(res, 500, false, 'Clinic owner check failed');
      }
    };
  }

  /**
   * Optional authentication middleware (doesn't fail if no token)
   */
  static async optionalAuth(req, res, next) {
    try {
      const authData = this.extractAuthHeaders(req.headers);
      
      if (authData.accessToken) {
        const decoded = jwt.verify(authData.accessToken, JWT_SECRET);
        const session = await authService.validateSession(decoded.userId);
        
        if (session) {
          req.user = { ...decoded, session };
        }
      }
      
      next();
    } catch (error) {
      // Silently continue without authentication
      next();
    }
  }

  /**
   * API key authentication for external integrations
   */
  static async authenticateApiKey(req, res, next) {
    try {
      const apiKey = req.headers['x-api-key'];
      
      if (!apiKey) {
        return sendResponse(res, 401, false, 'API key required');
      }

      // Validate API key (implement your API key validation logic)
      const isValidApiKey = await this.validateApiKey(apiKey);
      
      if (!isValidApiKey) {
        return sendResponse(res, 401, false, 'Invalid API key');
      }

      req.apiKey = apiKey;
      next();
    } catch (error) {
      console.error('API key authentication error:', error);
      return sendResponse(res, 500, false, 'API key authentication failed');
    }
  }

  /**
   * Validate API key (placeholder implementation)
   */
  static async validateApiKey(apiKey) {
    // Implement your API key validation logic here
    // This could check against a database, cache, or external service
    return apiKey === process.env.MASTER_API_KEY;
  }
}

export default EnhancedAuthMiddleware;
export {
  EnhancedAuthMiddleware
};
