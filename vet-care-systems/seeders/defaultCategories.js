import AppointmentCategory from '../models/appointmentCategory.model.js';

const defaultCategories = [
    {
        name: "Vaccination",
        description: "Preventive vaccination services for pets",
        icon: "syringe",
        color: "#10B981", // Green
        displayOrder: 1,
        estimatedDuration: 15,
        defaultStaffRoles: ["veterinarian", "vet_tech"],
        requiresQualification: true,
        createdBy: 1001
    },
    {
        name: "Consultation",
        description: "General health consultation and examination",
        icon: "stethoscope",
        color: "#3B82F6", // Blue
        displayOrder: 2,
        estimatedDuration: 30,
        defaultStaffRoles: ["veterinarian"],
        requiresQualification: true,
        createdBy: 1001
    },
    {
        name: "Grooming",
        description: "Pet grooming and hygiene services",
        icon: "scissors",
        color: "#F59E0B", // Amber
        displayOrder: 3,
        estimatedDuration: 60,
        defaultStaffRoles: ["groomer", "assistant"],
        requiresEquipment: true,
        createdBy: 1001
    },
    {
        name: "Day Care",
        description: "Pet day care and supervision services",
        icon: "home",
        color: "#8B5CF6", // Purple
        displayOrder: 4,
        estimatedDuration: 480, // 8 hours
        defaultStaffRoles: ["assistant", "vet_tech"],
        createdBy: 1001
    },
    {
        name: "Dog Walking",
        description: "Professional dog walking and exercise services",
        icon: "walking",
        color: "#06B6D4", // Cyan
        displayOrder: 5,
        estimatedDuration: 30,
        defaultStaffRoles: ["assistant"],
        createdBy: 1001
    },
    {
        name: "Cat Walking",
        description: "Supervised outdoor time and exercise for cats",
        icon: "cat",
        color: "#EC4899", // Pink
        displayOrder: 6,
        estimatedDuration: 20,
        defaultStaffRoles: ["assistant"],
        createdBy: 1001
    },
    {
        name: "Imaging",
        description: "X-ray, ultrasound and diagnostic imaging services",
        icon: "camera",
        color: "#7C3AED", // Violet
        displayOrder: 7,
        estimatedDuration: 30,
        defaultStaffRoles: ["veterinarian", "vet_tech"],
        requiresEquipment: true,
        requiresQualification: true,
        createdBy: 1001
    },
    {
        name: "Surgery",
        description: "Surgical procedures and operations",
        icon: "scalpel",
        color: "#DC2626", // Red
        displayOrder: 8,
        estimatedDuration: 120,
        defaultStaffRoles: ["veterinarian"],
        requiresEquipment: true,
        requiresQualification: true,
        createdBy: 1001
    },
    {
        name: "Laboratory",
        description: "Laboratory tests and diagnostic services",
        icon: "flask",
        color: "#059669", // Emerald
        displayOrder: 9,
        estimatedDuration: 45,
        defaultStaffRoles: ["lab_tech", "vet_tech"],
        requiresEquipment: true,
        createdBy: 1001
    },
    {
        name: "Dental",
        description: "Dental care and oral health services",
        icon: "tooth",
        color: "#0891B2", // Sky
        displayOrder: 10,
        estimatedDuration: 45,
        defaultStaffRoles: ["veterinarian", "vet_tech"],
        requiresEquipment: true,
        requiresQualification: true,
        createdBy: 1001
    },
    {
        name: "Emergency",
        description: "Emergency and urgent care services",
        icon: "ambulance",
        color: "#EF4444", // Red
        displayOrder: 11,
        estimatedDuration: 60,
        defaultStaffRoles: ["veterinarian", "vet_tech"],
        requiresQualification: true,
        createdBy: 1001
    },
    {
        name: "Boarding",
        description: "Pet boarding and hospitalization services",
        icon: "building",
        color: "#7C3AED", // Violet
        displayOrder: 12,
        estimatedDuration: 1440, // 24 hours
        defaultStaffRoles: ["assistant", "vet_tech"],
        createdBy: 1001
    },
    {
        name: "Wellness Check",
        description: "Routine wellness and health check-ups",
        icon: "heart",
        color: "#F97316", // Orange
        displayOrder: 13,
        estimatedDuration: 45,
        defaultStaffRoles: ["veterinarian", "vet_tech"],
        requiresQualification: true,
        createdBy: 1001
    },
    {
        name: "Pharmacy",
        description: "Medication dispensing and pharmaceutical services",
        icon: "pills",
        color: "#84CC16", // Lime
        displayOrder: 14,
        estimatedDuration: 15,
        defaultStaffRoles: ["veterinarian", "vet_tech"],
        requiresQualification: true,
        createdBy: 1001
    },
    {
        name: "Behavioral Training",
        description: "Pet behavior modification and training services",
        icon: "brain",
        color: "#A855F7", // Purple
        displayOrder: 15,
        estimatedDuration: 60,
        defaultStaffRoles: ["veterinarian", "assistant"],
        createdBy: 1001
    },
    {
        name: "Nutrition Counseling",
        description: "Pet nutrition and dietary consultation",
        icon: "apple",
        color: "#22C55E", // Green
        displayOrder: 16,
        estimatedDuration: 30,
        defaultStaffRoles: ["veterinarian", "vet_tech"],
        requiresQualification: true,
        createdBy: 1001
    },
    {
        name: "Microchipping",
        description: "Pet identification microchip implantation",
        icon: "microchip",
        color: "#6366F1", // Indigo
        displayOrder: 17,
        estimatedDuration: 15,
        defaultStaffRoles: ["veterinarian", "vet_tech"],
        requiresQualification: true,
        createdBy: 1001
    },
    {
        name: "Euthanasia",
        description: "Humane end-of-life services",
        icon: "heart-broken",
        color: "#6B7280", // Gray
        displayOrder: 18,
        estimatedDuration: 60,
        defaultStaffRoles: ["veterinarian"],
        requiresQualification: true,
        createdBy: 1001
    },
    {
        name: "Physiotherapy",
        description: "Pet rehabilitation and physiotherapy services",
        icon: "dumbbell",
        color: "#14B8A6", // Teal
        displayOrder: 19,
        estimatedDuration: 45,
        defaultStaffRoles: ["veterinarian", "vet_tech"],
        requiresEquipment: true,
        requiresQualification: true,
        createdBy: 1001
    },
    {
        name: "Follow-up",
        description: "Post-treatment follow-up appointments",
        icon: "calendar-check",
        color: "#F59E0B", // Amber
        displayOrder: 20,
        estimatedDuration: 20,
        defaultStaffRoles: ["veterinarian", "vet_tech"],
        createdBy: 1001
    }
];

export const seedDefaultCategories = async () => {
    try {
        console.log('Seeding default appointment categories...');

        // Check if categories already exist
        const existingCategories = await AppointmentCategory.countDocuments();
        if (existingCategories > 0) {
            console.log('Appointment categories already exist, skipping seeding');
            return;
        }

        // Create categories
        const createdCategories = await AppointmentCategory.insertMany(defaultCategories);
        console.log(`✅ Successfully created ${createdCategories.length} default appointment categories`);

        // Log created categories
        createdCategories.forEach(category => {
            console.log(`   📋 ${category.name} (ID: ${category.appointmentCategoryId})`);
        });

    } catch (error) {
        console.error('❌ Error seeding default categories:', error.message);

        // If it's a duplicate key error, that's okay
        if (error.code === 11000) {
            console.log('Categories already exist (duplicate key), skipping seeding');
        } else {
            throw error;
        }
    }
};
