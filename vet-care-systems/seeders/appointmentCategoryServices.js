import AppointmentCategory from '../models/appointmentCategory.model.js';
import CategoryService from '../models/categoryService.model.js';

// Comprehensive services for each appointment category
const servicesByCategory = {
    "Vaccination": [
        { name: 'Rabies Vaccination', code: 'VAC001', price: 1500, duration: 20, description: 'Core rabies vaccine for dogs and cats' },
        { name: 'DHPP Vaccination', code: 'VAC002', price: 1800, duration: 20, description: 'Distemper, Hepatitis, Parvovirus, Parainfluenza vaccine for dogs' },
        { name: 'FVRCP Vaccination', code: 'VAC003', price: 1700, duration: 20, description: 'Feline Viral Rhinotracheitis, Calicivirus, Panleukopenia vaccine' },
        { name: 'Bordetella Vaccination', code: 'VAC004', price: 1200, duration: 15, description: 'Kennel cough vaccine' },
        { name: 'Lyme Disease Vaccination', code: 'VAC005', price: 2000, duration: 20, description: 'Lyme disease prevention vaccine for dogs' }
    ],
    "Consultation": [
        { name: 'General Consultation', code: 'CONS001', price: 2000, duration: 45, description: 'Comprehensive health examination and consultation' },
        { name: 'Senior Pet Consultation', code: 'CONS002', price: 2500, duration: 60, description: 'Specialized consultation for senior pets' },
        { name: 'Puppy/Kitten First Visit', code: 'CONS003', price: 1800, duration: 45, description: 'Initial health check for young pets' },
        { name: 'Pre-Surgery Consultation', code: 'CONS004', price: 2200, duration: 30, description: 'Pre-operative assessment and planning' },
        { name: 'Second Opinion Consultation', code: 'CONS005', price: 2800, duration: 60, description: 'Independent medical opinion consultation' }
    ],
    "Grooming": [
        { name: 'Full Grooming Service', code: 'GROOM001', price: 2500, duration: 90, description: 'Complete grooming including bath, cut, and nail trim' },
        { name: 'Bath and Brush', code: 'GROOM002', price: 1500, duration: 45, description: 'Basic bath and brushing service' },
        { name: 'Nail Trimming', code: 'GROOM003', price: 500, duration: 15, description: 'Professional nail trimming' },
        { name: 'Ear Cleaning', code: 'GROOM004', price: 600, duration: 20, description: 'Thorough ear cleaning and inspection' },
        { name: 'Dental Hygiene', code: 'GROOM005', price: 800, duration: 30, description: 'Teeth cleaning and oral hygiene' }
    ],
    "Day Care": [
        { name: 'Full Day Care', code: 'DAYCARE001', price: 3000, duration: 480, description: 'Full day supervision and care' },
        { name: 'Half Day Care', code: 'DAYCARE002', price: 1800, duration: 240, description: 'Half day supervision and care' },
        { name: 'Socialization Session', code: 'DAYCARE003', price: 1200, duration: 120, description: 'Supervised socialization with other pets' },
        { name: 'Exercise and Play', code: 'DAYCARE004', price: 800, duration: 60, description: 'Supervised exercise and play time' }
    ],
    "Dog Walking": [
        { name: 'Standard Walk', code: 'WALK001', price: 800, duration: 30, description: 'Standard 30-minute walk' },
        { name: 'Extended Walk', code: 'WALK002', price: 1200, duration: 60, description: 'Extended 60-minute walk' },
        { name: 'Group Walk', code: 'WALK003', price: 600, duration: 30, description: 'Group walking session' },
        { name: 'Training Walk', code: 'WALK004', price: 1000, duration: 45, description: 'Walk with basic training exercises' }
    ],
    "Cat Walking": [
        { name: 'Supervised Outdoor Time', code: 'CATWALK001', price: 600, duration: 20, description: 'Supervised outdoor exploration for cats' },
        { name: 'Leash Training', code: 'CATWALK002', price: 800, duration: 30, description: 'Leash training for cats' },
        { name: 'Garden Exploration', code: 'CATWALK003', price: 700, duration: 25, description: 'Supervised garden exploration' }
    ],
    "Imaging": [
        { name: 'X-Ray Examination', code: 'IMG001', price: 3500, duration: 30, description: 'Digital X-ray imaging' },
        { name: 'Ultrasound Scan', code: 'IMG002', price: 4000, duration: 45, description: 'Ultrasound examination' },
        { name: 'CT Scan', code: 'IMG003', price: 8000, duration: 60, description: 'Computed tomography scan' },
        { name: 'MRI Scan', code: 'IMG004', price: 12000, duration: 90, description: 'Magnetic resonance imaging' }
    ],
    "Surgery": [
        { name: 'Spay/Neuter Surgery', code: 'SURG001', price: 8000, duration: 120, description: 'Spaying or neutering procedure' },
        { name: 'Soft Tissue Surgery', code: 'SURG002', price: 12000, duration: 180, description: 'General soft tissue surgical procedures' },
        { name: 'Orthopedic Surgery', code: 'SURG003', price: 20000, duration: 240, description: 'Bone and joint surgical procedures' },
        { name: 'Emergency Surgery', code: 'SURG004', price: 15000, duration: 180, description: 'Emergency surgical intervention' }
    ],
    "Laboratory": [
        { name: 'Complete Blood Count', code: 'LAB001', price: 2000, duration: 30, description: 'Full blood analysis' },
        { name: 'Blood Chemistry Panel', code: 'LAB002', price: 2500, duration: 45, description: 'Comprehensive blood chemistry analysis' },
        { name: 'Urinalysis', code: 'LAB003', price: 1500, duration: 20, description: 'Complete urine analysis' },
        { name: 'Fecal Examination', code: 'LAB004', price: 1200, duration: 15, description: 'Parasitology and fecal analysis' },
        { name: 'Thyroid Function Test', code: 'LAB005', price: 3000, duration: 30, description: 'Thyroid hormone level testing' }
    ],
    "Dental": [
        { name: 'Dental Cleaning', code: 'DENT001', price: 4000, duration: 60, description: 'Professional dental cleaning under anesthesia' },
        { name: 'Tooth Extraction', code: 'DENT002', price: 2500, duration: 45, description: 'Surgical tooth extraction' },
        { name: 'Dental X-Ray', code: 'DENT003', price: 2000, duration: 30, description: 'Dental radiography' },
        { name: 'Oral Surgery', code: 'DENT004', price: 6000, duration: 90, description: 'Advanced oral surgical procedures' }
    ],
    "Emergency": [
        { name: 'Emergency Consultation', code: 'EMRG001', price: 5000, duration: 60, description: 'Urgent medical consultation' },
        { name: 'Trauma Assessment', code: 'EMRG002', price: 6000, duration: 90, description: 'Emergency trauma evaluation' },
        { name: 'Poison Control Treatment', code: 'EMRG003', price: 7000, duration: 120, description: 'Emergency poison treatment' },
        { name: 'Critical Care Stabilization', code: 'EMRG004', price: 8000, duration: 180, description: 'Emergency stabilization care' }
    ],
    "Boarding": [
        { name: 'Standard Boarding', code: 'BOARD001', price: 3000, duration: 1440, description: 'Standard overnight boarding' },
        { name: 'Luxury Boarding', code: 'BOARD002', price: 4500, duration: 1440, description: 'Premium boarding with extra amenities' },
        { name: 'Medical Boarding', code: 'BOARD003', price: 5000, duration: 1440, description: 'Boarding with medical supervision' },
        { name: 'Medication Administration', code: 'BOARD004', price: 500, duration: 10, description: 'Medication given during boarding' }
    ],
    "Wellness Check": [
        { name: 'Annual Wellness Exam', code: 'WELL001', price: 2200, duration: 45, description: 'Comprehensive annual health examination' },
        { name: 'Senior Pet Wellness', code: 'WELL002', price: 2800, duration: 60, description: 'Specialized wellness check for senior pets' },
        { name: 'Puppy/Kitten Wellness', code: 'WELL003', price: 2000, duration: 45, description: 'Wellness check for young pets' },
        { name: 'Pre-Travel Health Check', code: 'WELL004', price: 1800, duration: 30, description: 'Health certification for travel' }
    ],
    "Pharmacy": [
        { name: 'Prescription Medication', code: 'PHARM001', price: 0, duration: 15, description: 'Prescription medication dispensing' },
        { name: 'Over-the-Counter Medication', code: 'PHARM002', price: 0, duration: 10, description: 'OTC medication and supplements' },
        { name: 'Medication Consultation', code: 'PHARM003', price: 800, duration: 20, description: 'Medication counseling and review' },
        { name: 'Compounded Medication', code: 'PHARM004', price: 0, duration: 30, description: 'Custom compounded medications' }
    ],
    "Behavioral Training": [
        { name: 'Basic Obedience Training', code: 'BEHAV001', price: 2000, duration: 60, description: 'Basic obedience and behavior training' },
        { name: 'Aggression Management', code: 'BEHAV002', price: 3000, duration: 90, description: 'Behavioral modification for aggression' },
        { name: 'Anxiety Treatment', code: 'BEHAV003', price: 2500, duration: 60, description: 'Anxiety and stress management' },
        { name: 'Socialization Training', code: 'BEHAV004', price: 1800, duration: 45, description: 'Social skills development' }
    ],
    "Nutrition Counseling": [
        { name: 'Dietary Assessment', code: 'NUT001', price: 1500, duration: 30, description: 'Comprehensive dietary evaluation' },
        { name: 'Weight Management Plan', code: 'NUT002', price: 2000, duration: 45, description: 'Weight loss or gain program' },
        { name: 'Special Diet Consultation', code: 'NUT003', price: 1800, duration: 30, description: 'Specialized dietary requirements' },
        { name: 'Nutritional Supplement Advice', code: 'NUT004', price: 1200, duration: 20, description: 'Supplement recommendations' }
    ],
    "Microchipping": [
        { name: 'Microchip Implantation', code: 'MICRO001', price: 1500, duration: 15, description: 'Pet identification microchip insertion' },
        { name: 'Microchip Registration', code: 'MICRO002', price: 500, duration: 10, description: 'Microchip database registration' },
        { name: 'Microchip Scanning', code: 'MICRO003', price: 300, duration: 5, description: 'Microchip detection and reading' }
    ],
    "Euthanasia": [
        { name: 'Euthanasia Service', code: 'EUTH001', price: 5000, duration: 60, description: 'Humane end-of-life service' },
        { name: 'Cremation Service', code: 'EUTH002', price: 3000, duration: 0, description: 'Pet cremation arrangements' },
        { name: 'Memorial Service', code: 'EUTH003', price: 1000, duration: 30, description: 'Memorial and remembrance service' }
    ],
    "Physiotherapy": [
        { name: 'Physical Rehabilitation', code: 'PHYSIO001', price: 2500, duration: 45, description: 'Physical therapy and rehabilitation' },
        { name: 'Hydrotherapy', code: 'PHYSIO002', price: 3000, duration: 60, description: 'Water-based therapy' },
        { name: 'Massage Therapy', code: 'PHYSIO003', price: 1800, duration: 30, description: 'Therapeutic massage' },
        { name: 'Exercise Therapy', code: 'PHYSIO004', price: 2000, duration: 45, description: 'Structured exercise program' }
    ],
    "Follow-up": [
        { name: 'Post-Surgery Follow-up', code: 'FOLLOW001', price: 1500, duration: 20, description: 'Post-operative examination' },
        { name: 'Treatment Progress Check', code: 'FOLLOW002', price: 1200, duration: 20, description: 'Treatment progress evaluation' },
        { name: 'Medication Review', code: 'FOLLOW003', price: 1000, duration: 15, description: 'Medication effectiveness review' },
        { name: 'General Follow-up', code: 'FOLLOW004', price: 1300, duration: 20, description: 'General follow-up consultation' }
    ]
};

export const seedAppointmentCategoryServices = async () => {
    try {
        console.log('Seeding appointment category services...');

        // Check if services already exist
        const existingServices = await CategoryService.countDocuments();
        if (existingServices > 0) {
            console.log('Category services already exist, skipping seeding');
            return;
        }

        // Get all appointment categories
        const appointmentCategories = await AppointmentCategory.find({}).lean();
        console.log(`Found ${appointmentCategories.length} appointment categories`);

        let totalServicesCreated = 0;

        // Create services for each category
        for (const [categoryName, services] of Object.entries(servicesByCategory)) {
            console.log(`Creating services for category: ${categoryName}`);

            // Find the matching appointment category
            const matchingCategory = appointmentCategories.find(cat => cat.name === categoryName);

            if (!matchingCategory) {
                console.log(`No matching category found for: ${categoryName}`);
                console.log(`Available categories: ${appointmentCategories.map(c => c.name).join(', ')}`);
                continue;
            }

            console.log(`Found matching category: ${matchingCategory.name} (ID: ${matchingCategory.appointmentCategoryId})`);

            // Create services for this category
            for (let i = 0; i < services.length; i++) {
                const serviceData = services[i];
                try {
                    const categoryService = await CategoryService.create({
                        categoryServiceName: serviceData.name,
                        categoryServiceCode: serviceData.code,
                        description: serviceData.description,
                        appointmentCategoryId: matchingCategory.appointmentCategoryId,
                        defaultPrice: serviceData.price,
                        currency: 'KES',
                        estimatedDuration: serviceData.duration,
                        isActive: true,
                        isCustom: false,
                        createdBy: 1001 // Default admin
                    });

                    totalServicesCreated++;
                    console.log(`Created service: ${categoryService.categoryServiceName} (ID: ${categoryService.categoryServiceId})`);
                } catch (serviceError) {
                    if (serviceError.code === 11000) {
                        console.log(`Service ${serviceData.name} already exists, skipping...`);
                    } else {
                        console.error(`Error creating service ${serviceData.name}:`, serviceError.message);
                    }
                }
            }
        }

        console.log(`Successfully created ${totalServicesCreated} category services`);
    } catch (error) {
        console.error('Error seeding appointment category services:', error);
    }
};

export default seedAppointmentCategoryServices;
