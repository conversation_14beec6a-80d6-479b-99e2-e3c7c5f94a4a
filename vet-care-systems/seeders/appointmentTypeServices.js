import Service from '../models/service.model.js';
import AppointmentType from '../models/appointmentType.model.js';
import AppointmentTypeService from '../models/appointmentTypeService.model.js';

// Comprehensive services for each appointment type category
const servicesByCategory = {
    vaccination: [
        { name: 'Rabies Vaccination', price: 45, duration: 20, description: 'Core rabies vaccine for dogs and cats' },
        { name: 'DHPP Vaccination', price: 50, duration: 20, description: 'Distemper, Hepatitis, Parvovirus, Parainfluenza vaccine for dogs' },
        { name: 'FVRCP Vaccination', price: 48, duration: 20, description: 'Feline Viral Rhinotracheitis, Calicivirus, Panleukopenia vaccine' },
        { name: 'Bordetella Vaccination', price: 35, duration: 15, description: 'Kennel cough vaccine' },
        { name: 'Lyme Disease Vaccination', price: 55, duration: 20, description: 'Lyme disease prevention vaccine for dogs' }
    ],
    consultation: [
        { name: 'General Consultation', price: 75, duration: 45, description: 'Comprehensive health examination and consultation' },
        { name: 'Senior Pet Consultation', price: 85, duration: 60, description: 'Specialized consultation for senior pets' },
        { name: 'Puppy/Kitten First Visit', price: 65, duration: 45, description: 'Initial health check for young pets' },
        { name: 'Pre-Surgery Consultation', price: 70, duration: 30, description: 'Pre-operative assessment and planning' },
        { name: 'Second Opinion Consultation', price: 90, duration: 60, description: 'Independent medical opinion consultation' }
    ],
    laboratory: [
        { name: 'Complete Blood Count (CBC)', price: 80, duration: 30, description: 'Comprehensive blood cell analysis' },
        { name: 'Blood Chemistry Panel', price: 120, duration: 45, description: 'Organ function and metabolic analysis' },
        { name: 'Urinalysis', price: 45, duration: 20, description: 'Urine analysis for kidney and bladder health' },
        { name: 'Fecal Examination', price: 35, duration: 15, description: 'Parasite and digestive health screening' },
        { name: 'Thyroid Function Test', price: 95, duration: 30, description: 'Thyroid hormone level assessment' }
    ],
    surgery: [
        { name: 'Spay Surgery (Female)', price: 350, duration: 120, description: 'Ovariohysterectomy procedure' },
        { name: 'Neuter Surgery (Male)', price: 280, duration: 90, description: 'Castration procedure' },
        { name: 'Tumor Removal', price: 450, duration: 150, description: 'Surgical removal of tumors' },
        { name: 'Dental Surgery', price: 320, duration: 120, description: 'Oral surgery and tooth extraction' },
        { name: 'Soft Tissue Surgery', price: 400, duration: 180, description: 'General soft tissue surgical procedures' }
    ],
    boarding: [
        { name: 'Pet Boarding', price: 50, duration: 1440, description: 'Daily boarding care' },
        { name: 'Medication Administration During Boarding', price: 15, duration: 10, description: 'Medication given during boarding' },
        { name: 'Exercise and Play Time', price: 20, duration: 60, description: 'Supervised exercise and play' },
        { name: 'Special Diet Management', price: 10, duration: 15, description: 'Special dietary requirements management' },
        { name: 'Daily Health Monitoring', price: 25, duration: 30, description: 'Daily health checks during boarding' }
    ],
    grooming: [
        { name: 'Basic Grooming', price: 60, duration: 90, description: 'Bath, brush, and basic grooming' },
        { name: 'Full Grooming with Haircut', price: 85, duration: 120, description: 'Complete grooming with styling' },
        { name: 'Nail Trimming', price: 20, duration: 15, description: 'Nail clipping and filing' },
        { name: 'Ear Cleaning', price: 25, duration: 20, description: 'Professional ear cleaning' },
        { name: 'Flea Bath Treatment', price: 45, duration: 60, description: 'Specialized flea treatment bath' }
    ],
    dental: [
        { name: 'Dental Cleaning', price: 180, duration: 90, description: 'Professional dental cleaning under anesthesia' },
        { name: 'Tooth Extraction', price: 120, duration: 60, description: 'Surgical tooth removal' },
        { name: 'Dental X-rays', price: 95, duration: 30, description: 'Dental radiographic examination' },
        { name: 'Periodontal Treatment', price: 150, duration: 75, description: 'Treatment for gum disease' },
        { name: 'Dental Polishing', price: 65, duration: 30, description: 'Tooth polishing and fluoride treatment' }
    ],
    wellness: [
        { name: 'Annual Wellness Exam', price: 85, duration: 45, description: 'Comprehensive annual health check' },
        { name: 'Puppy/Kitten Wellness Package', price: 120, duration: 60, description: 'Complete wellness package for young pets' },
        { name: 'Senior Pet Wellness Exam', price: 110, duration: 60, description: 'Specialized wellness exam for senior pets' },
        { name: 'Weight Management Consultation', price: 70, duration: 45, description: 'Weight and nutrition management' },
        { name: 'Preventive Care Planning', price: 60, duration: 30, description: 'Preventive healthcare planning' }
    ],
    emergency: [
        { name: 'Emergency Consultation', price: 150, duration: 60, description: 'Urgent medical consultation' },
        { name: 'Trauma Assessment', price: 200, duration: 90, description: 'Emergency trauma evaluation' },
        { name: 'Poison Control Treatment', price: 180, duration: 120, description: 'Emergency poison treatment' },
        { name: 'Emergency Surgery', price: 600, duration: 240, description: 'Emergency surgical intervention' },
        { name: 'Critical Care Stabilization', price: 250, duration: 180, description: 'Emergency stabilization care' }
    ],
    follow_up: [
        { name: 'Follow-up Consultation', price: 50, duration: 30, description: 'Post-treatment follow-up visit' },
        { name: 'Post-Surgery Check', price: 45, duration: 30, description: 'Post-operative examination' },
        { name: 'Medication Review', price: 40, duration: 20, description: 'Medication effectiveness review' },
        { name: 'Treatment Progress Assessment', price: 55, duration: 30, description: 'Treatment progress evaluation' },
        { name: 'Recheck Examination', price: 60, duration: 30, description: 'Follow-up health examination' }
    ]
};

export const seedAppointmentTypeServices = async () => {
    try {
        console.log('Seeding appointment type services...');

        // Check if services already exist
        const existingServices = await Service.countDocuments();
        if (existingServices > 0) {
            console.log('Services already exist, skipping seeding');
            return;
        }

        // Get all appointment types
        const appointmentTypes = await AppointmentType.find({}).lean();
        console.log(`Found ${appointmentTypes.length} appointment types`);

        let totalServicesCreated = 0;
        let totalLinksCreated = 0;

        // Create services for each category and link them to appointment types
        for (const [category, services] of Object.entries(servicesByCategory)) {
            console.log(`Creating services for category: ${category}`);
            
            // Find appointment types that match this category
            const matchingTypes = appointmentTypes.filter(type => {
                const typeName = type.name.toLowerCase();
                return (
                    (category === 'vaccination' && typeName.includes('vaccination')) ||
                    (category === 'consultation' && (typeName.includes('consultation') || typeName.includes('behavioral') || typeName.includes('nutrition'))) ||
                    (category === 'laboratory' && typeName.includes('laboratory')) ||
                    (category === 'surgery' && typeName.includes('surgery')) ||
                    (category === 'boarding' && (typeName.includes('boarding') || typeName.includes('hospitalization'))) ||
                    (category === 'grooming' && typeName.includes('grooming')) ||
                    (category === 'dental' && typeName.includes('dental')) ||
                    (category === 'wellness' && (typeName.includes('wellness') || typeName.includes('check'))) ||
                    (category === 'emergency' && typeName.includes('emergency')) ||
                    (category === 'follow_up' && (typeName.includes('follow') || typeName.includes('microchip') || typeName.includes('deworming')))
                );
            });

            console.log(`Found ${matchingTypes.length} matching appointment types for ${category}`);

            // Create services for this category
            for (let i = 0; i < services.length; i++) {
                const serviceData = services[i];
                try {
                    const service = await Service.create({
                        serviceName: serviceData.name,
                        description: serviceData.description,
                        category: category,
                        defaultPrice: serviceData.price,
                        currency: 'KES',
                        estimatedDuration: serviceData.duration,
                        isActive: true,
                        isCustom: false,
                        createdBy: 1001 // Default admin
                    });

                    totalServicesCreated++;
                    console.log(`Created service: ${service.serviceName} (ID: ${service.serviceId})`);

                    // Link this service to matching appointment types
                    for (const appointmentType of matchingTypes) {
                        try {
                            await AppointmentTypeService.create({
                                appointmentTypeId: appointmentType.appointmentTypeId,
                                serviceId: service.serviceId,
                                isDefault: true,
                                displayOrder: i + 1,
                                isRequired: i === 0, // First service is required
                                createdBy: 1001 // Default admin
                            });
                            totalLinksCreated++;
                        } catch (linkError) {
                            if (linkError.code !== 11000) { // Ignore duplicate key errors
                                console.error(`Error linking service ${service.serviceId} to appointment type ${appointmentType.appointmentTypeId}:`, linkError.message);
                            }
                        }
                    }
                } catch (serviceError) {
                    if (serviceError.code === 11000) {
                        console.log(`Service ${serviceData.name} already exists, skipping...`);
                    } else {
                        console.error(`Error creating service ${serviceData.name}:`, serviceError.message);
                    }
                }
            }
        }

        console.log(`Successfully created ${totalServicesCreated} services and ${totalLinksCreated} appointment type-service links`);
    } catch (error) {
        console.error('Error seeding appointment type services:', error);
    }
};

export default seedAppointmentTypeServices;
