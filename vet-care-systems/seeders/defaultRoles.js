import Role from '../models/role.model.js';
import Permission from '../models/permission.model.js';

const defaultRoles = [
    {
        roleName: 'super_admin',
        category: 'system',
        description: 'System super administrator with all permissions',
        permissions: [1001] // all_access permission
    },
    {
        roleName: 'admin',
        category: 'system',
        description: 'System administrator',
        permissions: [1002, 1003, 1004, 1005, 1006, 1007, 1008] // All except all_access
    },
    {
        roleName: 'support',
        category: 'system',
        description: 'System support staff',
        permissions: [1009] // support permission
    },
    {
        roleName: 'clinic_owner',
        category: 'clinic',
        description: 'Clinic owner',
        permissions: [1002, 1003, 1004, 1005, 1006, 1007, 1008] // All except all_access
    },
    {
        roleName: 'clinic_admin',
        category: 'clinic',
        description: 'Clinic administrator',
        permissions: [1002, 1003, 1004, 1005, 1006, 1007, 1008] // All except all_access
    },
    {
        roleName: 'veterinarian',
        category: 'clinic',
        description: 'Licensed veterinarian',
        permissions: [1004, 1005, 1006] // patient, medical, appointment
    },
    {
        roleName: 'veterinarian_assistant',
        category: 'clinic',
        description: 'Veterinarian assistant',
        permissions: [1004, 1005] // patient, medical
    },
    {
        roleName: 'veterinary_technician',
        category: 'clinic',
        description: 'Veterinary medical technician',
        permissions: [1004, 1005] // patient, medical
    },
    {
        roleName: 'receptionist',
        category: 'clinic',
        description: 'Front desk receptionist',
        permissions: [1004, 1006] // patient, appointment
    },
    {
        roleName: 'grooming_staff',
        category: 'clinic',
        description: 'Pet grooming specialist',
        permissions: [1004, 1006], // patient, appointment
        allowedAppointmentTypes: [1005] // Only Grooming appointment type
    }
];

export const seedDefaultRoles = async (force = false) => {
    try {
        console.log('Seeding default roles...');

        // Check if roles already exist
        const existingRoles = await Role.countDocuments();
        if (existingRoles > 0 && !force) {
            console.log('Roles already exist, skipping seeding');
            return;
        }

        // If forcing, clear existing roles first
        if (force && existingRoles > 0) {
            await Role.deleteMany({});
            console.log('Cleared existing roles');
        }

        // Create default roles
        for (const roleData of defaultRoles) {
            const existingRole = await Role.findOne({ roleName: roleData.roleName });
            if (!existingRole) {
                await Role.create(roleData);
                console.log(`Created role: ${roleData.roleName}`);
            }
        }

        console.log('Default roles seeded successfully');
    } catch (error) {
        console.error('Error seeding default roles:', error);
    }
};

export default seedDefaultRoles;
