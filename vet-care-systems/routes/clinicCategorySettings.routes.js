import express from 'express';
import {
    getClinicCategories,
    updateClinicCategoryStatus,
    updateClinicCategorySettings,
    getAvailableCategories,
    bulkUpdateClinicCategories
} from '../controllers/clinicCategorySettings.controller.js';
import { verifyToken } from '../middleware/auth.middleware.js';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(verifyToken);

// Get all categories for a clinic (enabled and disabled)
router.get('/clinics/:clinicId/categories', getClinicCategories);

// Get all available categories that a clinic can enable
router.get('/clinics/:clinicId/available-categories', getAvailableCategories);

// Enable/disable a specific category for a clinic
router.put('/clinics/:clinicId/categories/:appointmentCategoryId/status', updateClinicCategoryStatus);

// Update clinic-specific settings for a category
router.put('/clinics/:clinicId/categories/:appointmentCategoryId/settings', updateClinicCategorySettings);

// Bulk update clinic category selections
router.put('/clinics/:clinicId/categories/bulk-update', bulkUpdateClinicCategories);

export default router;
