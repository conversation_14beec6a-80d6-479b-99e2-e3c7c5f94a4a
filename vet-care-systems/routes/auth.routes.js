import {Router} from "express";
import {signUp, signIn,} from "../controllers/auth.controller.js";
import { verifyToken } from "../middleware/auth.middleware.js";
import { getUserAvailableClinics } from "../middlewares/clinicContext.middleware.js";

const authRouter = Router();

// Test route to verify auth routes are working
authRouter.get('/test', (req, res) => {
    res.json({ message: 'Auth routes are working!' });
});

authRouter.post('/sign-up', signUp);
authRouter.post('/sign-in', signIn);
// authRouter.post('/sign-out', signOut);

// Protected routes
authRouter.get('/available-clinics', verifyToken, getUserAvailableClinics);

export default authRouter;