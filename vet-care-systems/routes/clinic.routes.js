import express from "express";
import {
    createClinic,
    getAllClinics,
    getClinicById,
    updateClinic,
    updateClinicProfile,
    deleteClinic,
    switchClinic,
    registerClinicOwner,
    assignClinicOwner,
    assignClinicManager,
    assignStaffToClinic,
    removeStaffFromClinic,
    // New multi-clinic functions
    getMyClinicsList,
    registerNewClinic,
    switchCurrentClinic,
    getClinicStaff,
    updateClinicSettings
} from "../controllers/clinic.controller.js";
import { verifyToken } from "../middleware/auth.middleware.js";

const router = express.Router();

// Protected routes
router.use(verifyToken);

// Multi-Clinic Management Routes
// Get all clinics for current user
router.get("/my-clinics", getMyClinicsList);

// Register new clinic (for clinic owners)
router.post("/register", registerNewClinic);

// Switch current clinic context
router.post("/switch-clinic", switchCurrentClinic);

// Get clinic staff with roles and permissions
router.get("/:clinicId/staff", getClinicStaff);

// Assign staff to clinic
router.post("/:clinicId/assign-staff", assignStaffToClinic);

// Update clinic settings
router.put("/:clinicId/settings", updateClinicSettings);

// Admin endpoint to register clinic owner with default password
router.post("/register-owner", registerClinicOwner);

// Create a new clinic
router.post("/create", createClinic);

// Get all clinics
router.get("/", getAllClinics);

// Get a single clinic by clinic_id
router.get("/:clinicId", getClinicById);

// Update a clinic
router.put("/update/:clinicId", updateClinic);

// Update clinic profile (comprehensive)
router.put("/:clinicId/profile", updateClinicProfile);

// Delete a clinic
router.delete("/delete/:clinicId", deleteClinic);

// Switch current clinic (legacy endpoint - use new one)
router.post("/switch", switchCurrentClinic);

// Admin endpoints for clinic-staff management
// Assign/attach a clinic to an existing owner
router.put("/:clinicId/assign-owner", assignClinicOwner);

// Clinic owner endpoint to assign a clinic manager
router.put("/:clinicId/assign-manager", assignClinicManager);

// Assign/attach staff members to a clinic
router.put("/:clinicId/assign-staff", assignStaffToClinic);

// Remove staff from a clinic (or move to another clinic)
router.put("/:clinicId/remove-staff", removeStaffFromClinic);

export default router;