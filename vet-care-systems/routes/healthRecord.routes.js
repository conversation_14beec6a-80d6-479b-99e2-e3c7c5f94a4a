import { Router } from "express";
import { verifyToken } from "../middleware/auth.middleware.js";
import {
    createHealthRecord,
    getHealthRecordsByPet,
    getHealthRecordsByAppointment,
    getHealthRecordById,
    updateHealthRecord,
    deleteHealthRecord,
    getPetMedicalHistory,
    applyDiscount,
    generateReceipt,
    getReceiptByHealthRecord
} from '../controllers/healthRecord.controller.js';

const healthRecordRouter = Router();

// Apply authorization middleware to all routes
healthRecordRouter.use(verifyToken);

// Health record routes
healthRecordRouter.post('/', createHealthRecord);
healthRecordRouter.get('/pet/:petId', getHealthRecordsByPet);
healthRecordRouter.get('/pet/:petId/medical-history', getPetMedicalHistory);
healthRecordRouter.get('/appointment/:appointmentId', getHealthRecordsByAppointment);
healthRecordRouter.get('/:recordId', getHealthRecordById);
healthRecordRouter.put('/:recordId', updateHealthRecord);
healthRecordRouter.delete('/:recordId', deleteHealthRecord);

// Discount routes
healthRecordRouter.post('/:recordId/discount', applyDiscount);

// Receipt routes
healthRecordRouter.post('/:recordId/receipt', generateReceipt);
healthRecordRouter.get('/:recordId/receipt', getReceiptByHealthRecord);

export default healthRecordRouter;