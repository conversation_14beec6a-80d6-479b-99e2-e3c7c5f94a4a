/**
 * Appointment Categories Routes - Appointment Categories Management
 */

import express from 'express';
import {
    createAppointmentCategory,
    getAllAppointmentCategories,
    getAppointmentCategoryById,
    updateAppointmentCategory,
    deleteAppointmentCategory,
    getAppointmentCategoriesWithStats
} from '../controllers/appointmentCategory.controller.js';
import { verifyToken } from '../middleware/auth.middleware.js';

const router = express.Router();

// Apply authentication to all routes
router.use(verifyToken);

// Create appointment category
router.post('/', createAppointmentCategory);

// Get all appointment categories
router.get('/', getAllAppointmentCategories);

// Get appointment categories with stats for dashboard
router.get('/stats', getAppointmentCategoriesWithStats);

// Get single appointment category by ID
router.get('/:appointmentCategoryId', getAppointmentCategoryById);

// Update appointment category
router.put('/:appointmentCategoryId', updateAppointmentCategory);

// Delete appointment category
router.delete('/:appointmentCategoryId', deleteAppointmentCategory);

export default router;
