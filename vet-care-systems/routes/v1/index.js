import express from 'express';

// Import v1 route modules
import authRoutes from './auth.routes.js';
import userRoutes from './user.routes.js';
import staffRoutes from './staff.routes.js';
import clinicRoutes from './clinic.routes.js';
import appointmentRoutes from './appointment.routes.js';
import clientRoutes from './client.routes.js';
import petRoutes from './pet.routes.js';
import healthRoutes from './health.routes.js';
import billingRoutes from './billing.routes.js';
import inventoryRoutes from './inventory.routes.js';
import reportRoutes from './report.routes.js';
import aiRoutes from './ai.routes.js';
import adminRoutes from './admin.routes.js';

const router = express.Router();

// API Documentation route
router.get('/', (req, res) => {
  res.json({
    version: '1.0.0',
    title: 'VetCare SaaS API v1',
    description: 'Comprehensive veterinary practice management system API',
    documentation: '/api/v1/docs',
    endpoints: {
      authentication: '/api/v1/auth',
      users: '/api/v1/users',
      staff: '/api/v1/staff',
      clinics: '/api/v1/clinics',
      appointments: '/api/v1/appointments',
      clients: '/api/v1/clients',
      pets: '/api/v1/pets',
      health: '/api/v1/health',
      billing: '/api/v1/billing',
      inventory: '/api/v1/inventory',
      reports: '/api/v1/reports',
      ai: '/api/v1/ai',
      admin: '/api/v1/admin'
    },
    features: [
      'Multi-clinic support',
      'Role-based permissions',
      'Real-time notifications',
      'AI-powered suggestions',
      'Comprehensive reporting',
      'Inventory management',
      'Billing and invoicing'
    ]
  });
});

// Mount service routes with proper organization
router.use('/auth', authRoutes);
router.use('/users', userRoutes);
router.use('/staff', staffRoutes);
router.use('/clinics', clinicRoutes);
router.use('/appointments', appointmentRoutes);
router.use('/clients', clientRoutes);
router.use('/pets', petRoutes);
router.use('/health', healthRoutes);
router.use('/billing', billingRoutes);
router.use('/inventory', inventoryRoutes);
router.use('/reports', reportRoutes);
router.use('/ai', aiRoutes);
router.use('/admin', adminRoutes);

export default router;
