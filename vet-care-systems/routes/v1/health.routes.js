import express from 'express';
import healthService from '../../services/health.service.js';
import { sendResponse } from '../../utils/responseHandler.js';
import { asyncHandler } from '../../middleware/error-handler.middleware.js';

const router = express.Router();

/**
 * @route GET /api/v1/health
 * @desc Comprehensive health check
 * @access Public
 */
router.get('/', asyncHandler(async (req, res) => {
  const health = await healthService.getHealthStatus();
  
  const statusCode = health.status === 'healthy' ? 200 : 503;
  
  res.status(statusCode).json({
    success: health.status === 'healthy',
    data: health
  });
}));

/**
 * @route GET /api/v1/health/quick
 * @desc Quick health check for load balancers
 * @access Public
 */
router.get('/quick', asyncHandler(async (req, res) => {
  const health = await healthService.getQuickHealth();
  
  const statusCode = health.status === 'healthy' ? 200 : 503;
  
  res.status(statusCode).json(health);
}));

/**
 * @route GET /api/v1/health/ready
 * @desc Readiness probe for Kubernetes
 * @access Public
 */
router.get('/ready', asyncHandler(async (req, res) => {
  const readiness = await healthService.isReady();
  
  const statusCode = readiness.ready ? 200 : 503;
  
  res.status(statusCode).json(readiness);
}));

/**
 * @route GET /api/v1/health/live
 * @desc Liveness probe for Kubernetes
 * @access Public
 */
router.get('/live', (req, res) => {
  const liveness = healthService.isAlive();
  
  res.status(200).json(liveness);
});

/**
 * @route GET /api/v1/health/metrics
 * @desc System metrics
 * @access Public
 */
router.get('/metrics', asyncHandler(async (req, res) => {
  const metrics = await healthService.getMetrics();
  
  sendResponse(res, 200, true, 'System metrics retrieved', metrics);
}));

/**
 * @route GET /api/v1/health/database
 * @desc Database health check
 * @access Public
 */
router.get('/database', asyncHandler(async (req, res) => {
  const dbHealth = await healthService.checkDatabase();
  
  const statusCode = dbHealth.status === 'healthy' ? 200 : 503;
  
  res.status(statusCode).json({
    success: dbHealth.status === 'healthy',
    data: dbHealth
  });
}));

/**
 * @route GET /api/v1/health/redis
 * @desc Redis health check
 * @access Public
 */
router.get('/redis', asyncHandler(async (req, res) => {
  const redisHealth = await healthService.checkRedis();
  
  const statusCode = redisHealth.status === 'healthy' ? 200 : 503;
  
  res.status(statusCode).json({
    success: redisHealth.status === 'healthy',
    data: redisHealth
  });
}));

/**
 * @route GET /api/v1/health/queue
 * @desc Queue health check
 * @access Public
 */
router.get('/queue', asyncHandler(async (req, res) => {
  const queueHealth = await healthService.checkQueue();
  
  const statusCode = queueHealth.status === 'healthy' ? 200 : 503;
  
  res.status(statusCode).json({
    success: queueHealth.status === 'healthy',
    data: queueHealth
  });
}));

export default router;
