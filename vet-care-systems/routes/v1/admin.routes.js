import express from 'express';
import { sendResponse } from '../../utils/responseHandler.js';
import EnhancedAuthMiddleware from '../../middleware/enhanced-auth.middleware.js';

const router = express.Router();

router.get('/', 
  EnhancedAuthMiddleware.authenticateAndAuthorize,
  EnhancedAuthMiddleware.requireRole(['super_admin', 'admin']),
  async (req, res) => {
    sendResponse(res, 200, true, 'Admin endpoint - v1');
  }
);

export default router;
