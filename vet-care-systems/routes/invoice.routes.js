import express from 'express';
import {
    generateInvoiceFromAppointment,
    getInvoiceById,
    getInvoiceByAppointment,
    applyDiscountToInvoice,
    updateInvoiceCharges,
    applyCategoryDiscount,
    updateInvoiceStatus
} from '../controllers/invoice.controller.js';
import { verifyToken } from '../middleware/auth.middleware.js';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(verifyToken);

// Generate invoice from appointment
router.post('/generate/:appointmentId', generateInvoiceFromAppointment);

// Get invoice by ID
router.get('/:invoiceId', getInvoiceById);

// Get invoice by appointment ID
router.get('/appointment/:appointmentId', getInvoiceByAppointment);

// Apply discount to invoice
router.post('/:invoiceId/discount', applyDiscountToInvoice);

// Update invoice charges (categories, services, discounts)
router.put('/:invoiceId/charges', updateInvoiceCharges);

// Apply discount to specific category or service
router.post('/:invoiceId/category-discount', applyCategoryDiscount);

// Update invoice status
router.patch('/:invoiceId/status', updateInvoiceStatus);

export default router;
