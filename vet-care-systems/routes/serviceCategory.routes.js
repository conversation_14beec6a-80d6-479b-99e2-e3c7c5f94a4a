/**
 * Service Category Routes
 */

import express from 'express';
import {
    createServiceCategory,
    getAllServiceCategories,
    getServiceCategoryById,
    updateServiceCategory,
    deleteServiceCategory,
    getServicesByCategory,
    reorderServiceCategories,
    getCategoriesWithStats
} from '../controllers/serviceCategory.controller.js';

const router = express.Router();

// Create service category
router.post('/', createServiceCategory);

// Get all service categories
router.get('/', getAllServiceCategories);

// Get categories with stats for dashboard
router.get('/stats', getCategoriesWithStats);

// Reorder categories
router.put('/reorder', reorderServiceCategories);

// Get services by category
router.get('/:serviceCategoryId/services', getServicesByCategory);

// Get single service category by ID
router.get('/:serviceCategoryId', getServiceCategoryById);

// Update service category
router.put('/:serviceCategoryId', updateServiceCategory);

// Delete service category
router.delete('/:serviceCategoryId', deleteServiceCategory);

export default router;
