import mongoose from 'mongoose';
import { seedDefaultRoles } from './seeders/defaultRoles.js';
import { DB_URI } from './config/env.js';

// Simple MongoDB connection for testing
const connectToDatabase = async () => {
    try {
        console.log('Connecting to MongoDB...');
        await mongoose.connect(DB_URI);
        console.log('✅ MongoDB Connected');
    } catch (error) {
        console.error('❌ MongoDB connection failed:', error.message);
        process.exit(1);
    }
};

// Test role seeding
const testRoles = async () => {
    try {
        await connectToDatabase();
        
        console.log('\n🔄 Testing role seeding...');
        
        // Force re-seed roles
        await seedDefaultRoles(true);
        
        // Check what roles exist
        const Role = mongoose.model('Role');
        const roles = await Role.find({}).select('roleId roleName category description').lean();
        
        console.log('\n📊 Roles in database:');
        console.log(`Total roles: ${roles.length}`);
        
        roles.forEach(role => {
            console.log(`- ${role.roleName} (ID: ${role.roleId}, Category: ${role.category})`);
        });
        
        // Check if clinic_owner role exists
        const clinicOwnerRole = await Role.findOne({ roleName: 'clinic_owner' });
        if (clinicOwnerRole) {
            console.log('\n✅ clinic_owner role found!');
            console.log(`Role ID: ${clinicOwnerRole.roleId}`);
            console.log(`Description: ${clinicOwnerRole.description}`);
        } else {
            console.log('\n❌ clinic_owner role NOT found!');
        }
        
        console.log('\n🎉 Role testing completed!');
        
    } catch (error) {
        console.error('❌ Error testing roles:', error);
    } finally {
        await mongoose.connection.close();
        process.exit(0);
    }
};

// Run the test
testRoles();
