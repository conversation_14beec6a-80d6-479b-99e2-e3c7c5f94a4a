import Breed from '../models/breed.model.js';
import Species from "../models/species.model.js";
import { sendResponse, paginateResults } from '../utils/responseHandler.js';

export const registerBreed = async (req, res) => {
    try {
        const {
            speciesId,
            breedName,
            commonColor,
            origin,
            sizeCategory,
            lifespan,
            temperament,
        } = req.body;

        if (!speciesId) {
            return sendResponse(res, 400, false, "Species ID is required");
        }

        if (!breedName) {
            return sendResponse(res, 400, false, "Breed name is required");
        }

        const species = await Species.findOne({ speciesId: parseInt(speciesId) }).lean();
        if (!species) {
            return sendResponse(res, 404, false, "Species not found");
        }

        const existingBreed = await Breed.findOne({ breedName }).lean();
        if (existingBreed) {
            return sendResponse(res, 409, false, "Breed already exists");
        }

        const breed = await Breed.create({
            speciesId: species.speciesId,
            breedName,
            commonColor,
            origin,
            sizeCategory,
            lifespan,
            temperament,
        });

        // Return only essential fields
        const responseData = {
            breedId: breed.breedId,
            speciesId: breed.speciesId,
            breedName: breed.breedName,
            commonColour: breed.commonColour,
            origin: breed.origin,
            sizeCategory: breed.sizeCategory,
            lifespan: breed.lifespan,
            temperament: breed.temperament,
            createdAt: breed.createdAt
        };

        return sendResponse(res, 201, true, "Breed registered successfully", responseData);
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

export const getAllBreeds = async (req, res) => {
    try {
        const {
            page = 1,
            limit = 10,
            sortBy = "breedName",
            sortOrder = "asc",
            search,
            speciesId
        } = req.query;
        console.log("Query parameters:", req.query);

        const skip = (parseInt(page) - 1) * parseInt(limit);
        const sortOptions = { [sortBy]: sortOrder === "desc" ? -1 : 1 };

        let query = {};
        if (speciesId) {
            // Validate that the species exists first
            const species = await Species.findOne({ speciesId: parseInt(speciesId) }).lean();
            if (!species) {
                return sendResponse(res, 404, false, "Species not found");
            }
            query.speciesId = parseInt(speciesId);
        }

        if (search) {
            query.$or = [
                { breedName: { $regex: search, $options: "i" } },
                { origin: { $regex: search, $options: "i" } },
                { sizeCategory: { $regex: search, $options: "i" } },
                { temperament: { $regex: search, $options: "i" } }
            ];
        }

        // Get breeds and manually populate species data
        const [breeds, totalCount, allSpecies] = await Promise.all([
            Breed.find(query)
                .select('breedId speciesId breedName commonColour origin sizeCategory lifespan temperament createdAt -_id')
                .sort(sortOptions)
                .skip(skip)
                .limit(parseInt(limit))
                .lean(),
            Breed.countDocuments(query),
            Species.find({}).select('speciesId speciesName -_id').lean()
        ]);

        // Create species lookup map
        const speciesMap = allSpecies.reduce((map, species) => {
            map[species.speciesId] = species;
            return map;
        }, {});

        // Manually populate species data
        const populatedBreeds = breeds.map(breed => ({
            ...breed,
            speciesId: speciesMap[breed.speciesId] || { speciesId: breed.speciesId, speciesName: 'Unknown' }
        }));

        console.log(`Found ${populatedBreeds.length} breeds out of ${totalCount} total`);

        return sendResponse(
            res,
            200,
            true,
            "Breeds fetched successfully",
            paginateResults(populatedBreeds, totalCount, page, limit)
        );
    } catch (error) {
        console.error("Error in getAllBreeds:", error);
        return sendResponse(res, 500, false, error.message);
    }
};

export const getBreedById = async (req, res) => {
    console.log(`Fetching breed with ID: ${req.params.breedId}`);
    try {
        const [breed, species] = await Promise.all([
            Breed.findOne({ breedId: parseInt(req.params.breedId) }).select('-_id').lean(),
            Species.find({}).select('speciesId speciesName -_id').lean()
        ]);

        if (!breed) {
            return sendResponse(res, 404, false, "Breed not found");
        }

        // Manually populate species data
        const speciesData = species.find(s => s.speciesId === breed.speciesId);
        const populatedBreed = {
            ...breed,
            speciesId: speciesData || { speciesId: breed.speciesId, speciesName: 'Unknown' }
        };

        return sendResponse(res, 200, true, "Breed found successfully", populatedBreed);
    } catch (error) {
        console.error("Error in getBreedById:", error);
        return sendResponse(res, 500, false, error.message);
    }
};

export const updateBreed = async (req, res) => {
    try {
        const [breed, species] = await Promise.all([
            Breed.findOneAndUpdate(
                { breedId: parseInt(req.params.breedId) },
                { $set: req.body },
                { new: true, runValidators: true }
            ).lean(),
            Species.find({}).select('speciesId speciesName').lean()
        ]);

        if (!breed) {
            return sendResponse(res, 404, false, "Breed not found");
        }

        // Manually populate species data
        const speciesData = species.find(s => s.speciesId === breed.speciesId);
        const populatedBreed = {
            ...breed,
            speciesId: speciesData || { speciesId: breed.speciesId, speciesName: 'Unknown' }
        };

        return sendResponse(res, 200, true, "Breed updated successfully", populatedBreed);
    } catch (error) {
        console.error("Error in updateBreed:", error);
        return sendResponse(res, 400, false, error.message);
    }
};

export const deleteBreed = async (req, res) => {
    try {
        const breed = await Breed.findOneAndDelete({ breedId: parseInt(req.params.breedId) });

        if (!breed) {
            return sendResponse(res, 404, false, "Breed not found");
        }

        return sendResponse(res, 200, true, "Breed deleted successfully");
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};
