import Role from "../models/role.model.js";
import Permission from "../models/permission.model.js";
import { sendResponse } from '../utils/responseHandler.js';

export const createRole = async (req, res) => {
    try {
        const { roleName, description, permissions } = req.body;

        const existingRole = await Role.findOne({ roleName }).lean();
        if (existingRole) {
            return sendResponse(res, 409, false, "Role already exists");
        }

        const role = await Role.create({
            roleName,
            description,
            permissions,
            status: 1
        });

        return sendResponse(res, 201, true, "Role created successfully", role);
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

export const getAllRoles = async (req, res) => {
    try {
        const { 
            page = 1, 
            limit = 10, 
            sortBy = "roleName", 
            sortOrder = "asc",
            search,
            status 
        } = req.query;

        const skip = (parseInt(page) - 1) * parseInt(limit);
        const sortOptions = { [sortBy]: sortOrder === "desc" ? -1 : 1 };

        let query = {};
        if (status !== undefined) query.status = parseInt(status);
        if (search) {
            query.$or = [
                { roleId: { $regex: search, $options: "i" } },
                { roleName: { $regex: search, $options: "i" } },
                { description: { $regex: search, $options: "i" } }
            ];
        }

        const [roles, totalCount] = await Promise.all([
            Role.find(query)
                .select('-__v -_id')
                .sort(sortOptions)
                .skip(skip)
                .limit(parseInt(limit))
                .lean(),
            Role.countDocuments(query)
        ]);

        return sendResponse(
            res, 
            200, 
            true, 
            "Roles retrieved successfully",
            {
                data:roles,
                pagination: { 
                    total: totalCount,
                    page: parseInt(page),
                    limit: parseInt(limit),
                    pages: Math.ceil(totalCount / limit)
                }
            }
        );
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

export const getRoleById = async (req, res) => {
    try {
        const role = await Role.findOne({ roleId: parseInt(req.params.roleId) }).lean();
        
        if (!role) {
            return sendResponse(res, 404, false, "Role not found");
        }

        return sendResponse(res, 200, true, "Role found successfully", role);
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

export const updateRole = async (req, res) => {
    try {
        const { name, description, permissions, status } = req.body;
        
        if (name) {
            const existingRole = await Role.findOne({ 
                roleName: name, 
                roleId: { $ne: parseInt(req.params.roleId) } 
            }).lean();
            
            if (existingRole) {
                return sendResponse(res, 409, false, "Role name already exists");
            }
        }

        const role = await Role.findOneAndUpdate(
            { roleId: parseInt(req.params.roleId) },
            { $set: { roleName: name, description, permissions, status } },
            { new: true, runValidators: true }
        ).lean();

        if (!role) {
            return sendResponse(res, 404, false, "Role not found");
        }

        return sendResponse(res, 200, true, "Role updated successfully", role);
    } catch (error) {
        return sendResponse(res, 400, false, error.message);
    }
};

export const deleteRole = async (req, res) => {
    try {
        const role = await Role.findOneAndUpdate(
            { roleId: parseInt(req.params.roleId) },
            { status: 0 },
            { new: true }
        ).lean();

        if (!role) {
            return sendResponse(res, 404, false, "Role not found");
        }

        return sendResponse(res, 200, true, "Role deleted successfully");
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

export const addPermissionToRole = async (req, res) => {
    try {
        const { roleId } = req.params;
        const { permissionIds } = req.body;

        const role = await Role.findOne({ roleId: parseInt(roleId) });
        if (!role) {
            return sendResponse(res, 404, false, "Role not found");
        }

        const permissions = await Permission.find({ 
            permissionId: { $in: permissionIds },
            status: 1 
        });

        if (permissions.length !== permissionIds.length) {
            return sendResponse(res, 400, false, "Some permissions are invalid or inactive");
        }

        role.permissions = [...new Set([...role.permissions, ...permissionIds])];
        await role.save();

        return sendResponse(res, 200, true, "Permissions added successfully", role);
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

export const removePermissionFromRole = async (req, res) => {
    try {
        const { roleId, permissionId } = req.params;

        const role = await Role.findOne({ roleId: parseInt(roleId) });
        if (!role) {
            return sendResponse(res, 404, false, "Role not found");
        }

        role.permissions = role.permissions.filter(p => p !== parseInt(permissionId));
        await role.save();

        return sendResponse(res, 200, true, "Permission removed successfully", role);
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

export const getRolePermissions = async (req, res) => {
    try {
        const { roleId } = req.params;

        const role = await Role.findOne({ roleId: parseInt(roleId) }).lean();
        if (!role) {
            return sendResponse(res, 404, false, "Role not found");
        }

        const permissions = await Permission.find({ 
            permissionId: { $in: role.permissions },
            status: 1 
        }).lean();

        return sendResponse(res, 200, true, "Role permissions retrieved successfully", permissions);
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};