import Inventory from '../models/inventory.model.js';
import { sendResponse, paginateResults } from '../utils/responseHandler.js';
import mongoose from 'mongoose';

/**
 * Create a new inventory item
 */
export const createInventoryItem = async (req, res) => {
    try {
        const inventoryItem = await Inventory.create(req.body);
        
        // Add initial transaction if initial quantity is provided
        if (req.body.quantity > 0) {
            inventoryItem.transactions.push({
                type: 'purchase',
                quantity: req.body.quantity,
                date: new Date(),
                performedBy: req.user._id,
                notes: 'Initial inventory entry'
            });
            await inventoryItem.save();
        }
        
        return sendResponse(res, 201, true, "Inventory item created successfully", inventoryItem);
    } catch (error) {
        return sendResponse(res, 400, false, error.message);
    }
};

/**
 * Get all inventory items with pagination and filtering
 */
export const getAllInventoryItems = async (req, res) => {
    try {
        const { 
            page = 1, 
            limit = 10, 
            sortBy = "name", 
            sortOrder = "asc",
            search,
            category,
            isMedication,
            status,
            clinicId,
            lowStock,
            expired
        } = req.query;

        // Build query
        let query = {};
        
        // Add clinic filter
        if (clinicId) {
            query.clinicId = clinicId;
        }
        
        // Add search filter
        if (search) {
            query.$or = [
                { inventoryItemId: { $regex: search, $options: "i" } },
                { name: { $regex: search, $options: "i" } },
                { description: { $regex: search, $options: "i" } },
                { itemCode: { $regex: search, $options: "i" } },
                { activeIngredient: { $regex: search, $options: "i" } }
            ];
        }
        
        // Add category filter
        if (category) {
            query.category = category;
        }
        
        // Add medication filter
        if (isMedication !== undefined) {
            query.isMedication = isMedication === 'true';
        }
        
        // Add status filter
        if (status) {
            query.status = status;
        }
        
        // Add low stock filter
        if (lowStock === 'true') {
            query.$expr = { $lte: ["$quantity", "$reorderLevel"] };
        }
        
        // Add expired filter
        if (expired === 'true') {
            query.expiryDate = { $lt: new Date() };
        }

        // Set up pagination and sorting
        const options = {
            page: parseInt(page),
            limit: parseInt(limit),
            sort: { [sortBy]: sortOrder === "desc" ? -1 : 1 }
        };

        // Execute query with pagination
        const paginatedResults = paginateResults(Inventory, query, options);
        
        return sendResponse(res, 200, true, "Inventory items retrieved successfully", paginatedResults);
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Get a specific inventory item by ID
 */
export const getInventoryItemById = async (req, res) => {
    try {
        const { itemId } = req.params;
        
        const inventoryItem = await Inventory.findOne({ inventoryItemId: parseInt(itemId) }).select('-_id');
        
        if (!inventoryItem) {
            return sendResponse(res, 404, false, "Inventory item not found");
        }
        
        return sendResponse(res, 200, true, "Inventory item retrieved successfully", inventoryItem);
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Update an inventory item
 */
export const updateInventoryItem = async (req, res) => {
    try {
        const { itemId } = req.params;
        
        // Check if item exists
        const existingItem = await Inventory.findOne({ inventoryItemId: parseInt(itemId) });
        if (!existingItem) {
            return sendResponse(res, 404, false, "Inventory item not found");
        }
        
        // Update the item
        const updatedItem = await Inventory.findOneAndUpdate(
            { inventoryItemId: parseInt(itemId) },
            req.body,
            { new: true, runValidators: true }
        );
        
        return sendResponse(res, 200, true, "Inventory item updated successfully", updatedItem);
    } catch (error) {
        return sendResponse(res, 400, false, error.message);
    }
};

/**
 * Delete an inventory item
 */
export const deleteInventoryItem = async (req, res) => {
    try {
        const { itemId } = req.params;
        
        // Check if item exists
        const existingItem = await Inventory.findOne({ inventoryItemId: parseInt(itemId) });
        if (!existingItem) {
            return sendResponse(res, 404, false, "Inventory item not found");
        }
        
        // Delete the item
        await Inventory.findOneAndDelete({ inventoryItemId: parseInt(itemId) });
        
        return sendResponse(res, 200, true, "Inventory item deleted successfully");
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Adjust inventory quantity
 */
export const adjustInventory = async (req, res) => {
    const session = await mongoose.startSession();
    session.startTransaction();
    
    try {
        const { itemId } = req.params;
        const { 
            adjustmentType, 
            quantity, 
            notes,
            relatedRecordType,
            relatedRecordId
        } = req.body;
        
        if (!adjustmentType || !quantity) {
            await session.abortTransaction();
            await session.endSession();
            return sendResponse(res, 400, false, "Adjustment type and quantity are required");
        }
        
        // Find the inventory item
        const inventoryItem = await Inventory.findOne({ inventoryItemId: parseInt(itemId) }).session(session);
        if (!inventoryItem) {
            await session.abortTransaction();
            await session.endSession();
            return sendResponse(res, 404, false, "Inventory item not found");
        }
        
        // Calculate new quantity based on adjustment type
        let newQuantity = inventoryItem.quantity;
        
        switch (adjustmentType) {
            case 'purchase':
            case 'transfer':
            case 'adjustment':
                newQuantity += parseFloat(quantity);
                break;
            case 'sale':
            case 'expired':
            case 'damaged':
                newQuantity -= parseFloat(quantity);
                if (newQuantity < 0) {
                    await session.abortTransaction();
                    await session.endSession();
                    return sendResponse(res, 400, false, "Insufficient inventory quantity");
                }
                break;
            default:
                await session.abortTransaction();
                await session.endSession();
                return sendResponse(res, 400, false, "Invalid adjustment type");
        }
        
        // Add transaction record
        const transaction = {
            type: adjustmentType,
            quantity: parseFloat(quantity),
            date: new Date(),
            performedBy: req.user._id,
            notes: notes || '',
            relatedRecord: {
                recordType: relatedRecordType,
                recordId: relatedRecordId
            }
        };
        
        // Update inventory
        const updatedItem = await Inventory.findOneAndUpdate(
            { inventoryItemId: parseInt(itemId) },
            { 
                $set: { 
                    quantity: newQuantity,
                    lastUsedDate: new Date()
                },
                $push: { 
                    transactions: transaction 
                }
            },
            { new: true, runValidators: true, session }
        );
        
        await session.commitTransaction();
        await session.endSession();
        
        return sendResponse(res, 200, true, "Inventory adjusted successfully", updatedItem);
    } catch (error) {
        await session.abortTransaction();
        await session.endSession();
        return sendResponse(res, 500, false, error.message);
    }
};
