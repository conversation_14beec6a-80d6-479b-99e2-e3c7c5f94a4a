import Client from '../models/client.model.js';
import { sendResponse } from '../utils/responseHandler.js';

// Generate unique account ID
const generateAccountId = async () => {
    const lastClient = await Client.findOne().sort({ createdAt: -1 });
    const accountNumber = lastClient ? parseInt(lastClient.accountId.slice(2)) + 1 : 1000;
    return `CL${accountNumber}`;
};

export const createClient = async (req, res) => {
    try {
        const { email, phoneNumber } = req.body;

        // Check if client already exists
        const existingClient = await Client.findOne({
            $or: [{ email }, { phoneNumber }]
        });

        if (existingClient) {
            return sendResponse(res, 409, false, "Client with this email or phone number already exists");
        }

        // Generate account ID
        const accountId = await generateAccountId();

        // Set default password if not provided
        const clientData = {
            ...req.body,
            password: req.body.password || "pass123", // Default password
            accountId,
            status: 1 // Use status instead of clientStatus to match the schema
        };

        // Create the client
        const client = await Client.create(clientData);

        return sendResponse(res, 201, true, "Client created successfully", client);
    } catch (error) {
        console.error("Error creating client:", error);
        return sendResponse(res, 500, false, error.message);
    }
};

export const getAllClients = async (req, res) => {
    try {
        const {
            page = 1,
            limit = 10,
            sortBy = "createdAt",
            sortOrder = "desc",
            name,  // Changed from search to name
            status,
            id,
            phoneNumber,
            isWalkIn,
            clientId
        } = req.query;
        console.log("Name search query:", name);
        let accountId = id;
        if (accountId) accountId = accountId.toUpperCase();

        const skip = (parseInt(page) - 1) * parseInt(limit);
        const sortOptions = { [sortBy]: sortOrder === "desc" ? -1 : 1 };

        let query = {};
        if (status !== undefined) query.status = parseInt(status);
        if (accountId) query.accountId = accountId;
        if (phoneNumber) query.phoneNumber = phoneNumber;
        if (isWalkIn !== undefined) query.isWalkIn = isWalkIn === 'true';
        if (clientId) query.clientId = parseInt(clientId);

        if (name && name.trim() !== '') {
            const searchRegex = new RegExp(name, 'i');
            query.$or = [
                { firstName: searchRegex },
                { lastName: searchRegex },
                { email: searchRegex },
                { phoneNumber: searchRegex },
                { address: searchRegex },
                // Add ability to search by full name (first + last)
                { $expr: { $regexMatch: {
                    input: { $concat: ["$firstName", " ", "$lastName"] },
                    regex: name,
                    options: "i"
                }}}
            ];
            console.log("Search query built:", JSON.stringify(query));
        }

        // Debug the final query
        console.log("Final MongoDB query:", JSON.stringify(query));

        const [clients, totalCount] = await Promise.all([
            Client.find(query)
                .select('clientId accountId firstName lastName email phoneNumber address status createdAt updatedAt clinicId -_id')
                .sort(sortOptions)
                .skip(skip)
                .limit(parseInt(limit))
                .lean(),
            Client.countDocuments(query)
        ]);

        console.log(`Found ${clients.length} clients out of ${totalCount} total`);

        // Return only essential client data
        const essentialClients = clients.map(client => ({
            clientId: client.clientId,
            accountId: client.accountId,
            firstName: client.firstName,
            lastName: client.lastName,
            email: client.email,
            phoneNumber: client.phoneNumber,
            address: client.address,
            status: client.status,
            clinicId: client.clinicId,
            createdAt: client.createdAt,
            updatedAt: client.updatedAt
        }));

        return sendResponse(res, 200, true, "Clients retrieved successfully", {
            data: essentialClients,
            pagination: {
                totalCount: totalCount,
                page: parseInt(page),
                limit: parseInt(limit),
                offset: (parseInt(page) - 1) * parseInt(limit),
                totalPages: Math.ceil(totalCount / limit)
            }
        });
    } catch (error) {
        console.error("Error in getAllClients:", error);
        return sendResponse(res, 500, false, error.message);
    }
};

export const getClientById = async (req, res) => {
    try {
        const client = await Client.findOne({ accountId: req.params.accountId })
            .populate('clinicId', 'clinicName address clinicId -_id')
            .select('-_id')
            .lean();

        if (!client) {
            return sendResponse(res, 404, false, "Client not found");
        }

        return sendResponse(res, 200, true, "Client retrieved successfully", client);
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

export const updateClient = async (req, res) => {
    try {
        const { email, phoneNumber } = req.body;

        if (email || phoneNumber) {
            const existingClient = await Client.findOne({
                $or: [
                    email ? { email } : null,
                    phoneNumber ? { phoneNumber } : null
                ].filter(Boolean),
                accountId: { $ne: req.params.accountId }
            });

            if (existingClient) {
                return sendResponse(res, 409, false, "Email or phone number already in use");
            }
        }

        const client = await Client.findOneAndUpdate(
            { accountId: req.params.accountId },
            { $set: req.body },
            { new: true, runValidators: true }
        ).lean();

        if (!client) {
            return sendResponse(res, 404, false, "Client not found");
        }

        return sendResponse(res, 200, true, "Client updated successfully", client);
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

export const deleteClient = async (req, res) => {
    try {
        const client = await Client.findOneAndUpdate(
            { accountId: req.params.accountId },
            { status: 0 },
            { new: true }
        ).lean();

        if (!client) {
            return sendResponse(res, 404, false, "Client not found");
        }

        return sendResponse(res, 200, true, "Client deactivated successfully");
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};
