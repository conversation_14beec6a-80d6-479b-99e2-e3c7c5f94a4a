import Species from '../models/species.model.js';
import { sendResponse, paginateResults } from '../utils/responseHandler.js';

export const createSpecies = async (req, res) => {
    try {
        const { speciesName, description, imageUrl, status } = req.body;

        if (!speciesName) {
            return sendResponse(res, 400, false, "Species name is required");
        }

        const existingSpecies = await Species.findOne({ speciesName }).lean();
        if (existingSpecies) {
            return sendResponse(res, 409, false, "Species already exists");
        }

        // Handle image upload if present
        let finalImageUrl = imageUrl;
        if (req.file) {
            // If using multer or similar middleware for file uploads
            // Store the file path or URL
            finalImageUrl = `/uploads/species/${req.file.filename}`;
        }

        const species = await Species.create({
            speciesName,
            description,
            imageUrl: finalImageUrl,
            status: status || 1
        });

        // Return only essential fields
        const responseData = {
            speciesId: species.speciesId,
            speciesName: species.speciesName,
            description: species.description,
            status: species.status,
            createdAt: species.createdAt
        };

        return sendResponse(res, 201, true, "Species created successfully", responseData);
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

export const getAllSpecies = async (req, res) => {
    try {
        const {
            page = 1,
            limit = 10,
            sortBy = "speciesName",
            sortOrder = "asc",
            search
        } = req.query;

        console.log("Query parameters:", req.query);

        const skip = (parseInt(page) - 1) * parseInt(limit);
        const sortOptions = { [sortBy]: sortOrder === "desc" ? -1 : 1 };

        let query = {};
        if (search) {
            query = {
                $or: [
                    { speciesId: { $regex: search, $options: "i" } },
                    { speciesName: { $regex: search, $options: "i" } },
                    { description: { $regex: search, $options: "i" } }
                ]
            };
        }

        const [species, totalCount] = await Promise.all([
            Species.find(query)
                .select('speciesId speciesName description status createdAt -_id')
                .sort(sortOptions)
                .skip(skip)
                .limit(parseInt(limit))
                .lean(),
            Species.countDocuments(query)
        ]);

        console.log(`Found ${JSON.stringify(species)} species out of ${totalCount} total`);

        return sendResponse(
            res,
            200,
            true,
            "Species retrieved successfully",
            paginateResults(species, totalCount, page, limit)
        );
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};


export const updateSpecies = async (req, res) => {
    try {
        const { name, description, imageUrl, status } = req.body;

        // Check if species exists
        const existingSpecies = await Species.findOne({ speciesId: parseInt(req.params.speciesId) });
        if (!existingSpecies) {
            return sendResponse(res, 404, false, "Species not found");
        }

        // If updating name, check if it conflicts with another species
        if (name && name !== existingSpecies.speciesName) {
            const nameConflict = await Species.findOne({
                speciesName: name,
                speciesId: { $ne: parseInt(req.params.speciesId) }
            });

            if (nameConflict) {
                return sendResponse(res, 409, false, "Species name already exists");
            }
        }

        // Handle image upload if present
        let finalImageUrl = imageUrl;
        if (req.file) {
            // If using multer or similar middleware for file uploads
            finalImageUrl = `/uploads/species/${req.file.filename}`;
        }

        // Prepare update data
        const updateData = {
            ...(name && { speciesName: name }),
            ...(description !== undefined && { description }),
            ...(finalImageUrl && { imageUrl: finalImageUrl }),
            ...(status !== undefined && { status })
        };

        // Update the species
        const species = await Species.findOneAndUpdate(
            { speciesId: parseInt(req.params.speciesId) },
            { $set: updateData },
            { new: true, runValidators: true }
        );

        // Return only essential fields
        const responseData = {
            speciesId: species.speciesId,
            speciesName: species.speciesName,
            description: species.description,
            status: species.status,
            createdAt: species.createdAt,
            updatedAt: species.updatedAt
        };

        return sendResponse(res, 200, true, "Species updated successfully", responseData);
    } catch (error) {
        return sendResponse(res, 400, false, error.message);
    }
};

export const deleteSpecies = async (req, res) => {
    try {
        const species = await Species.findOneAndDelete({ speciesId: parseInt(req.params.speciesId) });

        if (!species) {
            return sendResponse(res, 404, false, "Species not found");
        }

        return sendResponse(res, 200, true, "Species deleted successfully");
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};
