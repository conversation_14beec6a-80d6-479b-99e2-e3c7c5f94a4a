import AppointmentCategory from '../models/appointmentCategory.model.js';
import ClinicCategorySettings from '../models/clinicCategorySettings.model.js';
import { sendResponse, paginateResults } from '../utils/responseHandler.js';

/**
 * Create a new appointment category
 */
const createAppointmentCategory = async (req, res) => {
    try {
        const categoryData = {
            ...req.body,
            createdBy: req.user?.staffId || req.user?.userId || 1001,
            clinicId: req.body.clinicId || req.user?.clinicId || null
        };

        const category = await AppointmentCategory.create(categoryData);
        return sendResponse(res, 201, true, "Appointment category created successfully", category);
    } catch (error) {
        console.error("Appointment category creation error:", error);
        return sendResponse(res, 400, false, `Appointment category creation failed: ${error.message}`);
    }
};

/**
 * Get all appointment categories
 */
const getAllAppointmentCategories = async (req, res) => {
    try {
        const {
            page = 1,
            limit = 100,
            sortBy = "displayOrder",
            sortOrder = "asc",
            isActive,
            clinicId,
            search
        } = req.query;

        const skip = (parseInt(page) - 1) * parseInt(limit);
        const sortOptions = { [sortBy]: sortOrder === "desc" ? -1 : 1 };

        let query = {};

        // Filter by active status
        if (isActive !== undefined) query.isActive = isActive === 'true';

        // If clinicId is provided, get clinic-specific enabled categories
        if (clinicId) {
            const parsedClinicId = parseInt(clinicId);
            if (isNaN(parsedClinicId)) {
                return sendResponse(res, 400, false, "Invalid clinic ID provided");
            }

            // Get clinic's enabled categories with their settings
            let clinicQuery = { clinicId: parsedClinicId, isEnabled: true };

            const clinicCategories = await ClinicCategorySettings.find(clinicQuery)
                .populate('categoryData')
                .sort({ displayOrder: 1, 'categoryData.name': 1 })
                .skip(skip)
                .limit(parseInt(limit));

            const totalCount = await ClinicCategorySettings.countDocuments(clinicQuery);

            // Transform data to include effective values
            const transformedCategories = clinicCategories.map(setting => {
                const effectiveValues = setting.getEffectiveValues();
                return {
                    appointmentCategoryId: setting.appointmentCategoryId,
                    clinicCategorySettingsId: setting.clinicCategorySettingsId,
                    ...effectiveValues,
                    isActive: setting.isEnabled,
                    displayOrder: setting.displayOrder,
                    createdAt: setting.createdAt,
                    updatedAt: setting.updatedAt
                };
            });

            const pagination = paginateResults(totalCount, parseInt(page), parseInt(limit));

            return sendResponse(res, 200, true, "Clinic appointment categories retrieved successfully", {
                categories: transformedCategories,
                pagination,
                totalCount
            });
        }

        // Search functionality
        if (search) {
            query.$or = [
                { name: { $regex: search, $options: 'i' } },
                { description: { $regex: search, $options: 'i' } }
            ];
        }

        const [categories, totalCount] = await Promise.all([
            AppointmentCategory.find(query)
                .sort(sortOptions)
                .skip(skip)
                .limit(parseInt(limit))
                .lean(),
            AppointmentCategory.countDocuments(query)
        ]);

        const pagination = paginateResults(totalCount, parseInt(page), parseInt(limit));

        return sendResponse(res, 200, true, "Appointment categories retrieved successfully", {
            categories,
            pagination,
            totalCount
        });
    } catch (error) {
        console.error("Get appointment categories error:", error);
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Get single appointment category by ID
 */
const getAppointmentCategoryById = async (req, res) => {
    try {
        const { appointmentCategoryId } = req.params;

        // Validate appointmentCategoryId parameter
        const parsedCategoryId = parseInt(appointmentCategoryId);
        if (isNaN(parsedCategoryId)) {
            return sendResponse(res, 400, false, "Invalid appointment category ID provided");
        }

        const category = await AppointmentCategory.findOne({
            appointmentCategoryId: parsedCategoryId
        }).lean();

        if (!category) {
            return sendResponse(res, 404, false, "Appointment category not found");
        }

        return sendResponse(res, 200, true, "Appointment category retrieved successfully", category);
    } catch (error) {
        console.error("Get appointment category error:", error);
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Update appointment category
 */
const updateAppointmentCategory = async (req, res) => {
    try {
        const { appointmentCategoryId } = req.params;

        // Validate appointmentCategoryId parameter
        const parsedCategoryId = parseInt(appointmentCategoryId);
        if (isNaN(parsedCategoryId)) {
            return sendResponse(res, 400, false, "Invalid appointment category ID provided");
        }

        const updateData = {
            ...req.body,
            modifiedBy: req.user?.staffId || req.user?.userId
        };

        const category = await AppointmentCategory.findOneAndUpdate(
            { appointmentCategoryId: parsedCategoryId },
            updateData,
            { new: true, runValidators: true }
        ).lean();

        if (!category) {
            return sendResponse(res, 404, false, "Appointment category not found");
        }

        return sendResponse(res, 200, true, "Appointment category updated successfully", category);
    } catch (error) {
        console.error("Update appointment category error:", error);
        return sendResponse(res, 400, false, `Appointment category update failed: ${error.message}`);
    }
};

/**
 * Delete appointment category
 */
const deleteAppointmentCategory = async (req, res) => {
    try {
        const { appointmentCategoryId } = req.params;

        // Validate appointmentCategoryId parameter
        const parsedCategoryId = parseInt(appointmentCategoryId);
        if (isNaN(parsedCategoryId)) {
            return sendResponse(res, 400, false, "Invalid appointment category ID provided");
        }

        const category = await AppointmentCategory.findOneAndDelete({
            appointmentCategoryId: parsedCategoryId
        });

        if (!category) {
            return sendResponse(res, 404, false, "Appointment category not found");
        }

        return sendResponse(res, 200, true, "Appointment category deleted successfully");
    } catch (error) {
        console.error("Delete appointment category error:", error);
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Get appointment categories with statistics
 */
const getAppointmentCategoriesWithStats = async (req, res) => {
    try {
        const { clinicId } = req.query;

        let query = { isActive: true };
        if (clinicId) {
            query.$or = [
                { clinicId: null }, // Global categories
                { clinicId: parseInt(clinicId) }
            ];
        }

        const categories = await AppointmentCategory.find(query)
            .sort({ displayOrder: 1 })
            .lean();

        // Add basic stats and ensure charge/discount fields are included
        const categoriesWithStats = categories.map(category => ({
            ...category,
            defaultCharge: category.defaultCharge || 0,
            currency: category.currency || 'KES',
            defaultDiscountPercentage: category.defaultDiscountPercentage || 0,
            stats: {
                totalAppointments: 0, // You can add actual appointment counting here
                activeServices: 0,    // You can add actual service counting here
                avgDuration: category.estimatedDuration || 30
            }
        }));

        return sendResponse(res, 200, true, "Appointment categories with stats retrieved successfully", categoriesWithStats);
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

export {
    createAppointmentCategory,
    getAllAppointmentCategories,
    getAppointmentCategoryById,
    updateAppointmentCategory,
    deleteAppointmentCategory,
    getAppointmentCategoriesWithStats
};
