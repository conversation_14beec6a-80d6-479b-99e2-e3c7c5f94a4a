import CategoryService from '../models/categoryService.model.js';
import { sendResponse } from '../utils/responseHandler.js';

/**
 * Get all services with filtering
 */
export const getAllServices = async (req, res) => {
    try {
        const {
            appointmentCategoryId,
            search,
            isActive = true,
            isCustom,
            clinicId,
            page = 1,
            limit = 100
        } = req.query;

        let query = {};

        // Filter by active status
        if (isActive !== undefined) {
            query.isActive = isActive === 'true';
        }

        // Filter by custom services
        if (isCustom !== undefined) {
            query.isCustom = isCustom === 'true';
        }

        // Filter by appointment category
        if (appointmentCategoryId) {
            query.appointmentCategoryId = parseInt(appointmentCategoryId);
        }

        // Filter by appointment type IDs (new approach)
        if (req.query.appointmentTypeIds) {
            const appointmentTypeIds = Array.isArray(req.query.appointmentTypeIds)
                ? req.query.appointmentTypeIds.map(id => parseInt(id))
                : [parseInt(req.query.appointmentTypeIds)];
            query.appointmentTypeId = { $in: appointmentTypeIds };
        }

        // Note: appointmentId filtering removed - services are now linked through appointment.serviceCategories

        // Filter by clinic (include global services)
        const userClinicId = clinicId || req.user?.clinicId;
        if (userClinicId) {
            query.$or = [
                { clinicId: null }, // Global services
                { clinicId: parseInt(userClinicId) } // Clinic-specific services
            ];
        }

        let servicesQuery;

        // Text search if provided
        if (search) {
            servicesQuery = CategoryService.searchServices(search, appointmentCategoryId, userClinicId);
        } else {
            servicesQuery = CategoryService.find(query);
        }

        const services = await servicesQuery
            .sort({ appointmentCategoryId: 1, categoryServiceName: 1 })
            .limit(parseInt(limit))
            .skip((parseInt(page) - 1) * parseInt(limit))
            .lean();

        // Get total count
        const total = await CategoryService.countDocuments(query);

        // Add created by staff data
        const populatedServices = await Promise.all(services.map(async (service) => {
            if (service.createdBy) {
                const staffData = await CategoryService.model('Staff').findOne({
                    staffId: service.createdBy
                }).lean();
                if (staffData) {
                    service.createdByData = staffData;
                    service.createdByName = `${staffData.firstName} ${staffData.lastName}`;
                }
            }
            return service;
        }));

        return sendResponse(res, 200, true, "Services retrieved successfully", {
            services: populatedServices,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                pages: Math.ceil(total / parseInt(limit))
            }
        });
    } catch (error) {
        console.error("Get services error:", error);
        return sendResponse(res, 500, false, `Failed to retrieve services: ${error.message}`);
    }
};

/**
 * Get services by appointment category
 */
export const getServicesByCategory = async (req, res) => {
    try {
        const { category } = req.params;
        const { clinicId } = req.query;

        const userClinicId = clinicId || req.user?.clinicId;

        // If category is a number, treat it as appointmentCategoryId
        let query;
        if (!isNaN(category)) {
            query = { appointmentCategoryId: parseInt(category), isActive: true };
        } else {
            // For backward compatibility, search by category name
            // First find the appointment category by name
            const appointmentCategory = await CategoryService.model('AppointmentCategory').findOne({
                name: { $regex: new RegExp(category, 'i') }
            }).lean();

            if (!appointmentCategory) {
                return sendResponse(res, 404, false, `Category '${category}' not found`);
            }

            query = { appointmentCategoryId: appointmentCategory.appointmentCategoryId, isActive: true };
        }

        if (userClinicId) {
            query.$or = [
                { ...query, clinicId: null }, // Global services
                { ...query, clinicId: parseInt(userClinicId) } // Clinic-specific services
            ];
            delete query.appointmentCategoryId;
            delete query.isActive;
        }

        const services = await CategoryService.find(query)
            .sort({ categoryServiceName: 1 })
            .lean();

        return sendResponse(res, 200, true, `Services retrieved successfully`, services);
    } catch (error) {
        console.error("Get services by category error:", error);
        return sendResponse(res, 500, false, `Failed to retrieve services: ${error.message}`);
    }
};

/**
 * Get single service by ID
 */
export const getServiceById = async (req, res) => {
    try {
        const { serviceId } = req.params;

        const service = await CategoryService.findOne({ categoryServiceId: parseInt(serviceId) }).lean();

        if (!service) {
            return sendResponse(res, 404, false, "Service not found");
        }

        // Add created by staff data
        if (service.createdBy) {
            const staffData = await CategoryService.model('Staff').findOne({
                staffId: service.createdBy
            }).lean();
            if (staffData) {
                service.createdByData = staffData;
                service.createdByName = `${staffData.firstName} ${staffData.lastName}`;
            }
        }

        return sendResponse(res, 200, true, "Service retrieved successfully", service);
    } catch (error) {
        console.error("Get service by ID error:", error);
        return sendResponse(res, 500, false, `Failed to retrieve service: ${error.message}`);
    }
};

/**
 * Create new service
 */
export const createService = async (req, res) => {
    try {
        const serviceData = {
            categoryServiceName: req.body.serviceName || req.body.categoryServiceName,
            categoryServiceCode: req.body.serviceCode || req.body.categoryServiceCode,
            description: req.body.description,
            appointmentCategoryId: req.body.appointmentCategoryId,
            defaultPrice: req.body.defaultPrice,
            currency: req.body.currency || 'KES',
            estimatedDuration: req.body.estimatedDuration || 30,
            isActive: req.body.isActive !== undefined ? req.body.isActive : true,
            isCustom: req.body.isCustom !== undefined ? req.body.isCustom : true,
            clinicId: req.body.clinicId || req.user?.clinicId || null,
            createdBy: req.user?.staffId || req.user?.userId || 1001
        };

        // Validate required fields
        if (!serviceData.categoryServiceName) {
            return sendResponse(res, 400, false, "Service name is required");
        }
        if (!serviceData.appointmentCategoryId) {
            return sendResponse(res, 400, false, "Appointment category ID is required");
        }
        if (!serviceData.defaultPrice) {
            return sendResponse(res, 400, false, "Default price is required");
        }

        const service = await CategoryService.create(serviceData);

        // Get populated service for response
        const populatedService = await CategoryService.findOne({
            categoryServiceId: service.categoryServiceId
        }).lean();

        // Add created by staff data
        if (service.createdBy) {
            const staffData = await CategoryService.model('Staff').findOne({
                staffId: service.createdBy
            }).lean();
            if (staffData) {
                populatedService.createdByData = staffData;
                populatedService.createdByName = `${staffData.firstName} ${staffData.lastName}`;
            }
        }

        return sendResponse(res, 201, true, "Service created successfully", populatedService);
    } catch (error) {
        console.error("Create service error:", error);
        return sendResponse(res, 400, false, `Failed to create service: ${error.message}`);
    }
};

/**
 * Update service
 */
export const updateService = async (req, res) => {
    try {
        const { serviceId } = req.params;

        // Map old field names to new ones for backward compatibility
        const updateData = {};
        if (req.body.serviceName) updateData.categoryServiceName = req.body.serviceName;
        if (req.body.categoryServiceName) updateData.categoryServiceName = req.body.categoryServiceName;
        if (req.body.serviceCode) updateData.categoryServiceCode = req.body.serviceCode;
        if (req.body.categoryServiceCode) updateData.categoryServiceCode = req.body.categoryServiceCode;
        if (req.body.description) updateData.description = req.body.description;
        if (req.body.appointmentCategoryId) updateData.appointmentCategoryId = req.body.appointmentCategoryId;
        if (req.body.defaultPrice) updateData.defaultPrice = req.body.defaultPrice;
        if (req.body.currency) updateData.currency = req.body.currency;
        if (req.body.estimatedDuration) updateData.estimatedDuration = req.body.estimatedDuration;
        if (req.body.isActive !== undefined) updateData.isActive = req.body.isActive;
        if (req.body.isCustom !== undefined) updateData.isCustom = req.body.isCustom;
        if (req.body.clinicId !== undefined) updateData.clinicId = req.body.clinicId;

        updateData.modifiedBy = req.user?.staffId || req.user?.userId;

        const service = await CategoryService.findOneAndUpdate(
            { categoryServiceId: parseInt(serviceId) },
            updateData,
            { new: true, runValidators: true }
        ).lean();

        if (!service) {
            return sendResponse(res, 404, false, "Service not found");
        }

        // Add staff data
        if (service.createdBy) {
            const staffData = await CategoryService.model('Staff').findOne({
                staffId: service.createdBy
            }).lean();
            if (staffData) {
                service.createdByData = staffData;
                service.createdByName = `${staffData.firstName} ${staffData.lastName}`;
            }
        }

        return sendResponse(res, 200, true, "Service updated successfully", service);
    } catch (error) {
        console.error("Update service error:", error);
        return sendResponse(res, 400, false, `Failed to update service: ${error.message}`);
    }
};

/**
 * Delete service (soft delete)
 */
export const deleteService = async (req, res) => {
    try {
        const { serviceId } = req.params;

        const service = await CategoryService.findOneAndUpdate(
            { categoryServiceId: parseInt(serviceId) },
            {
                isActive: false,
                modifiedBy: req.user?.staffId || req.user?.userId
            },
            { new: true }
        );

        if (!service) {
            return sendResponse(res, 404, false, "Service not found");
        }

        return sendResponse(res, 200, true, "Service deleted successfully");
    } catch (error) {
        console.error("Delete service error:", error);
        return sendResponse(res, 500, false, `Failed to delete service: ${error.message}`);
    }
};

/**
 * Search services
 */
export const searchServices = async (req, res) => {
    try {
        const { q: searchTerm, appointmentCategoryId, limit = 20 } = req.query;

        if (!searchTerm) {
            return sendResponse(res, 400, false, "Search term is required");
        }

        const userClinicId = req.user?.clinicId;
        const services = await CategoryService.searchServices(searchTerm, appointmentCategoryId, userClinicId);

        const limitedServices = services.slice(0, parseInt(limit));

        return sendResponse(res, 200, true, "Search completed successfully", limitedServices);
    } catch (error) {
        console.error("Search services error:", error);
        return sendResponse(res, 500, false, `Search failed: ${error.message}`);
    }
};

/**
 * Get appointment categories with service counts
 */
export const getServiceCategories = async (req, res) => {
    try {
        const { clinicId } = req.query;
        const userClinicId = clinicId || req.user?.clinicId;

        // Get all appointment categories
        let categoryQuery = { isActive: true };
        if (userClinicId) {
            categoryQuery.$or = [
                { clinicId: null },
                { clinicId: parseInt(userClinicId) }
            ];
        }

        const appointmentCategories = await CategoryService.model('AppointmentCategory')
            .find(categoryQuery)
            .sort({ displayOrder: 1 })
            .lean();

        // Get service counts for each category
        const categoriesWithCounts = await Promise.all(
            appointmentCategories.map(async (category) => {
                let serviceQuery = {
                    appointmentCategoryId: category.appointmentCategoryId,
                    isActive: true
                };

                if (userClinicId) {
                    serviceQuery.$or = [
                        { ...serviceQuery, clinicId: null },
                        { ...serviceQuery, clinicId: parseInt(userClinicId) }
                    ];
                    delete serviceQuery.appointmentCategoryId;
                    delete serviceQuery.isActive;
                }

                const services = await CategoryService.find(serviceQuery).lean();

                const count = services.length;
                const avgPrice = count > 0 ? services.reduce((sum, s) => sum + s.defaultPrice, 0) / count : 0;
                const minPrice = count > 0 ? Math.min(...services.map(s => s.defaultPrice)) : 0;
                const maxPrice = count > 0 ? Math.max(...services.map(s => s.defaultPrice)) : 0;

                return {
                    appointmentCategoryId: category.appointmentCategoryId,
                    category: category.name,
                    name: category.name,
                    description: category.description,
                    icon: category.icon,
                    color: category.color,
                    count,
                    avgPrice: Math.round(avgPrice * 100) / 100,
                    minPrice,
                    maxPrice
                };
            })
        );

        return sendResponse(res, 200, true, "Service categories retrieved successfully", categoriesWithCounts);
    } catch (error) {
        console.error("Get service categories error:", error);
        return sendResponse(res, 500, false, `Failed to retrieve categories: ${error.message}`);
    }
};
