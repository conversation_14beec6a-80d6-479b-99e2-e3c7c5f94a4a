import ClinicCategorySettings from '../models/clinicCategorySettings.model.js';
import AppointmentCategory from '../models/appointmentCategory.model.js';
import Clinic from '../models/clinic.model.js';
import { sendResponse } from '../utils/responseHandler.js';

/**
 * Get all categories available for a clinic (enabled and disabled)
 */
const getClinicCategories = async (req, res) => {
    try {
        const { clinicId } = req.params;
        const { enabled } = req.query;

        // Validate clinicId
        const parsedClinicId = parseInt(clinicId);
        if (isNaN(parsedClinicId)) {
            return sendResponse(res, 400, false, "Invalid clinic ID provided");
        }

        // Build query
        let query = { clinicId: parsedClinicId };
        if (enabled !== undefined) {
            query.isEnabled = enabled === 'true';
        }

        const clinicCategories = await ClinicCategorySettings.find(query)
            .populate('categoryData')
            .sort({ displayOrder: 1, 'categoryData.name': 1 });

        // Transform data to include effective values
        const transformedCategories = clinicCategories.map(setting => {
            const effectiveValues = setting.getEffectiveValues();
            return {
                clinicCategorySettingsId: setting.clinicCategorySettingsId,
                appointmentCategoryId: setting.appointmentCategoryId,
                clinicId: setting.clinicId,
                isEnabled: setting.isEnabled,
                displayOrder: setting.displayOrder,
                ...effectiveValues,
                customizations: {
                    customName: setting.customName,
                    customDescription: setting.customDescription,
                    customCharge: setting.customCharge,
                    customDiscountPercentage: setting.customDiscountPercentage,
                    customDuration: setting.customDuration,
                    allowedStaffRoles: setting.allowedStaffRoles,
                    preferredStaff: setting.preferredStaff,
                    clinicNotes: setting.clinicNotes
                },
                originalCategory: setting.categoryData
            };
        });

        return sendResponse(res, 200, true, "Clinic categories retrieved successfully", transformedCategories);
    } catch (error) {
        console.error("Get clinic categories error:", error);
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Enable/disable a category for a clinic
 */
const updateClinicCategoryStatus = async (req, res) => {
    try {
        const { clinicId, appointmentCategoryId } = req.params;
        const { isEnabled } = req.body;

        // Validate parameters
        const parsedClinicId = parseInt(clinicId);
        const parsedCategoryId = parseInt(appointmentCategoryId);
        
        if (isNaN(parsedClinicId) || isNaN(parsedCategoryId)) {
            return sendResponse(res, 400, false, "Invalid clinic ID or category ID provided");
        }

        // Check if category exists
        const category = await AppointmentCategory.findOne({ appointmentCategoryId: parsedCategoryId });
        if (!category) {
            return sendResponse(res, 404, false, "Appointment category not found");
        }

        // Find or create clinic category settings
        let clinicCategorySettings = await ClinicCategorySettings.findOne({
            clinicId: parsedClinicId,
            appointmentCategoryId: parsedCategoryId
        });

        if (!clinicCategorySettings) {
            // Create new settings
            clinicCategorySettings = new ClinicCategorySettings({
                clinicId: parsedClinicId,
                appointmentCategoryId: parsedCategoryId,
                isEnabled: isEnabled !== undefined ? isEnabled : true,
                createdBy: req.user?.staffId || req.user?.userId
            });
        } else {
            // Update existing settings
            if (isEnabled !== undefined) {
                clinicCategorySettings.isEnabled = isEnabled;
            }
            clinicCategorySettings.modifiedBy = req.user?.staffId || req.user?.userId;
        }

        await clinicCategorySettings.save();

        return sendResponse(res, 200, true, "Clinic category status updated successfully", clinicCategorySettings);
    } catch (error) {
        console.error("Update clinic category status error:", error);
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Update clinic-specific category settings
 */
const updateClinicCategorySettings = async (req, res) => {
    try {
        const { clinicId, appointmentCategoryId } = req.params;
        const {
            customName,
            customDescription,
            customCharge,
            customDiscountPercentage,
            customDuration,
            displayOrder,
            allowedStaffRoles,
            preferredStaff,
            clinicNotes
        } = req.body;

        // Validate parameters
        const parsedClinicId = parseInt(clinicId);
        const parsedCategoryId = parseInt(appointmentCategoryId);
        
        if (isNaN(parsedClinicId) || isNaN(parsedCategoryId)) {
            return sendResponse(res, 400, false, "Invalid clinic ID or category ID provided");
        }

        // Find or create clinic category settings
        let clinicCategorySettings = await ClinicCategorySettings.findOne({
            clinicId: parsedClinicId,
            appointmentCategoryId: parsedCategoryId
        });

        if (!clinicCategorySettings) {
            // Create new settings
            clinicCategorySettings = new ClinicCategorySettings({
                clinicId: parsedClinicId,
                appointmentCategoryId: parsedCategoryId,
                createdBy: req.user?.staffId || req.user?.userId
            });
        }

        // Update fields
        if (customName !== undefined) clinicCategorySettings.customName = customName;
        if (customDescription !== undefined) clinicCategorySettings.customDescription = customDescription;
        if (customCharge !== undefined) clinicCategorySettings.customCharge = customCharge;
        if (customDiscountPercentage !== undefined) clinicCategorySettings.customDiscountPercentage = customDiscountPercentage;
        if (customDuration !== undefined) clinicCategorySettings.customDuration = customDuration;
        if (displayOrder !== undefined) clinicCategorySettings.displayOrder = displayOrder;
        if (allowedStaffRoles !== undefined) clinicCategorySettings.allowedStaffRoles = allowedStaffRoles;
        if (preferredStaff !== undefined) clinicCategorySettings.preferredStaff = preferredStaff;
        if (clinicNotes !== undefined) clinicCategorySettings.clinicNotes = clinicNotes;

        clinicCategorySettings.modifiedBy = req.user?.staffId || req.user?.userId;
        await clinicCategorySettings.save();

        return sendResponse(res, 200, true, "Clinic category settings updated successfully", clinicCategorySettings);
    } catch (error) {
        console.error("Update clinic category settings error:", error);
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Get all available categories that a clinic can enable
 */
const getAvailableCategories = async (req, res) => {
    try {
        const { clinicId } = req.params;

        // Validate clinicId
        const parsedClinicId = parseInt(clinicId);
        if (isNaN(parsedClinicId)) {
            return sendResponse(res, 400, false, "Invalid clinic ID provided");
        }

        // Get all active appointment categories
        const allCategories = await AppointmentCategory.find({ isActive: true })
            .sort({ displayOrder: 1, name: 1 });

        // Get clinic's current settings
        const clinicSettings = await ClinicCategorySettings.find({ clinicId: parsedClinicId });
        const enabledCategoryIds = clinicSettings
            .filter(setting => setting.isEnabled)
            .map(setting => setting.appointmentCategoryId);

        // Transform data
        const availableCategories = allCategories.map(category => ({
            appointmentCategoryId: category.appointmentCategoryId,
            name: category.name,
            description: category.description,
            icon: category.icon,
            color: category.color,
            defaultCharge: category.defaultCharge,
            currency: category.currency,
            estimatedDuration: category.estimatedDuration,
            requiresEquipment: category.requiresEquipment,
            requiresQualification: category.requiresQualification,
            defaultStaffRoles: category.defaultStaffRoles,
            isEnabledForClinic: enabledCategoryIds.includes(category.appointmentCategoryId)
        }));

        return sendResponse(res, 200, true, "Available categories retrieved successfully", availableCategories);
    } catch (error) {
        console.error("Get available categories error:", error);
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Bulk update clinic category selections
 */
const bulkUpdateClinicCategories = async (req, res) => {
    try {
        const { clinicId } = req.params;
        const { categoryIds } = req.body; // Array of category IDs to enable

        // Validate clinicId
        const parsedClinicId = parseInt(clinicId);
        if (isNaN(parsedClinicId)) {
            return sendResponse(res, 400, false, "Invalid clinic ID provided");
        }

        if (!Array.isArray(categoryIds)) {
            return sendResponse(res, 400, false, "Category IDs must be an array");
        }

        const currentUserId = req.user?.staffId || req.user?.userId;

        // Get all existing settings for this clinic
        const existingSettings = await ClinicCategorySettings.find({ clinicId: parsedClinicId });

        // Disable all existing categories
        for (const setting of existingSettings) {
            setting.isEnabled = categoryIds.includes(setting.appointmentCategoryId);
            setting.modifiedBy = currentUserId;
            await setting.save();
        }

        // Create settings for new categories
        const existingCategoryIds = existingSettings.map(s => s.appointmentCategoryId);
        const newCategoryIds = categoryIds.filter(id => !existingCategoryIds.includes(id));

        for (const categoryId of newCategoryIds) {
            const newSetting = new ClinicCategorySettings({
                clinicId: parsedClinicId,
                appointmentCategoryId: categoryId,
                isEnabled: true,
                createdBy: currentUserId
            });
            await newSetting.save();
        }

        return sendResponse(res, 200, true, "Clinic categories updated successfully", {
            enabledCategories: categoryIds.length,
            totalProcessed: existingSettings.length + newCategoryIds.length
        });
    } catch (error) {
        console.error("Bulk update clinic categories error:", error);
        return sendResponse(res, 500, false, error.message);
    }
};

export {
    getClinicCategories,
    updateClinicCategoryStatus,
    updateClinicCategorySettings,
    getAvailableCategories,
    bulkUpdateClinicCategories
};
