import Receipt from "../models/receipt.model.js";
import Appointment from "../models/appointment.model.js";
import Invoice from "../models/invoice.model.js";
import Payment from "../models/payment.model.js";
import { sendResponse } from '../utils/responseHandler.js';

/**
 * Get all receipts with pagination
 */
export const getAllReceipts = async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const offset = (page - 1) * limit;

        const clinicId = req.user?.clinicId || req.staff?.clinicId;

        const query = clinicId ? { clinicId } : {};

        const [receipts, totalCount] = await Promise.all([
            Receipt.find(query)
                .sort({ createdAt: -1 })
                .skip(offset)
                .limit(limit)
                .lean(),
            Receipt.countDocuments(query)
        ]);

        const totalPages = Math.ceil(totalCount / limit);

        return sendResponse(res, 200, true, "Receipts retrieved successfully", {
            data: receipts,
            pagination: {
                totalCount,
                page,
                limit,
                offset,
                totalPages
            }
        });
    } catch (error) {
        console.error("Get receipts error:", error);
        return sendResponse(res, 500, false, `Failed to retrieve receipts: ${error.message}`);
    }
};

/**
 * Get receipt by ID
 */
export const getReceiptById = async (req, res) => {
    try {
        const { receiptId } = req.params;

        const receipt = await Receipt.findOne({
            receiptId: parseInt(receiptId)
        }).lean();

        if (!receipt) {
            return sendResponse(res, 404, false, "Receipt not found");
        }

        // Get related data
        const [clientData, petData, staffData] = await Promise.all([
            Receipt.model('Client').findOne({ clientId: receipt.clientId }).lean(),
            Receipt.model('Pet').findOne({ petId: receipt.petId }).lean(),
            Receipt.model('Staff').findOne({ staffId: receipt.issuedBy }).lean()
        ]);

        // Add related data to receipt
        receipt.clientData = clientData;
        receipt.petData = petData;
        receipt.staffData = staffData;

        return sendResponse(res, 200, true, "Receipt retrieved successfully", receipt);
    } catch (error) {
        console.error("Get receipt error:", error);
        return sendResponse(res, 500, false, `Failed to retrieve receipt: ${error.message}`);
    }
};

/**
 * Get receipt by appointment ID
 */
export const getReceiptByAppointment = async (req, res) => {
    try {
        const { appointmentId } = req.params;

        // Validate appointmentId parameter
        const parsedAppointmentId = parseInt(appointmentId);
        if (isNaN(parsedAppointmentId)) {
            return sendResponse(res, 400, false, "Invalid appointment ID provided");
        }

        const receipt = await Receipt.findOne({
            appointmentId: parsedAppointmentId
        }).lean();

        if (!receipt) {
            return sendResponse(res, 404, false, "Receipt not found for this appointment");
        }

        // Get related data
        const [clientData, petData, staffData] = await Promise.all([
            Receipt.model('Client').findOne({ clientId: receipt.clientId }).lean(),
            Receipt.model('Pet').findOne({ petId: receipt.petId }).lean(),
            Receipt.model('Staff').findOne({ staffId: receipt.issuedBy }).lean()
        ]);

        // Add related data to receipt
        receipt.clientData = clientData;
        receipt.petData = petData;
        receipt.staffData = staffData;

        return sendResponse(res, 200, true, "Receipt retrieved successfully", receipt);
    } catch (error) {
        console.error("Get receipt by appointment error:", error);
        return sendResponse(res, 500, false, `Failed to retrieve receipt: ${error.message}`);
    }
};

/**
 * Generate receipt from appointment
 */
export const generateReceiptFromAppointment = async (req, res) => {
    try {
        console.log("Starting receipt generation for appointment:", req.params.appointmentId);
        const { appointmentId } = req.params;

        // Validate appointmentId parameter
        const parsedAppointmentId = parseInt(appointmentId);
        if (isNaN(parsedAppointmentId)) {
            return sendResponse(res, 400, false, "Invalid appointment ID provided");
        }

        // Get appointment details
        const appointment = await Appointment.findOne({
            appointmentId: parsedAppointmentId
        }).lean();

        if (!appointment) {
            return sendResponse(res, 404, false, "Appointment not found");
        }

        // Check if receipt already exists
        let existingReceipt = await Receipt.findOne({
            appointmentId: parsedAppointmentId
        }).lean();

        if (existingReceipt) {
            return sendResponse(res, 200, true, "Receipt already exists", existingReceipt);
        }

        // Get the invoice for this appointment
        const invoice = await Invoice.findOne({
            appointmentId: parsedAppointmentId
        }).lean();

        if (!invoice) {
            return sendResponse(res, 404, false, "No invoice found for this appointment. Please generate an invoice first.");
        }

        // Get payments for this invoice
        const payments = await Payment.find({
            invoiceId: invoice.invoiceId,
            status: 'completed'
        }).lean();

        if (payments.length === 0) {
            return sendResponse(res, 400, false, "No payments found for this appointment. Please process payment first.");
        }

        // Calculate total paid amount
        const totalPaid = payments.reduce((sum, payment) => sum + payment.amount, 0);

        // Generate receipt number
        const receiptCount = await Receipt.countDocuments();
        const receiptNumber = `RCP-${String(receiptCount + 1).padStart(6, '0')}`;

        // Create receipt
        const receipt = await Receipt.create({
            receiptNumber,
            appointmentId: appointment.appointmentId,
            healthRecordId: invoice.healthRecordId,
            clientId: invoice.clientId,
            petId: invoice.petId,
            clinicId: invoice.clinicId,
            services: invoice.services || [],
            medications: invoice.medications || [],
            subtotal: invoice.subtotal,
            afterHoursCharge: invoice.afterHoursCharge || 0,
            taxRate: invoice.taxRate,
            taxAmount: invoice.taxAmount,
            totalAmount: invoice.totalAmount,
            currency: invoice.currency,
            discounts: invoice.discounts || [],
            totalDiscounts: invoice.totalDiscounts || 0,
            amountPaid: totalPaid,
            paymentMethod: payments[0].paymentMethod,
            paymentReference: payments[0].transactionReference || payments[0].paymentId?.toString(),
            paymentDate: payments[0].paymentDate || payments[0].createdAt,
            issuedBy: req.user?.staffId || req.user?.userId || 1001,
            issuedDate: new Date(),
            status: 'issued'
        });

        console.log("Receipt generated successfully:", receipt.receiptNumber);
        return sendResponse(res, 201, true, "Receipt generated successfully", receipt);

    } catch (error) {
        console.error("Generate receipt error:", error);
        return sendResponse(res, 500, false, `Failed to generate receipt: ${error.message}`);
    }
};

/**
 * Update receipt status
 */
export const updateReceiptStatus = async (req, res) => {
    try {
        const { receiptId } = req.params;
        const { status } = req.body;

        const receipt = await Receipt.findOneAndUpdate(
            { receiptId: parseInt(receiptId) },
            { status },
            { new: true }
        );

        if (!receipt) {
            return sendResponse(res, 404, false, "Receipt not found");
        }

        return sendResponse(res, 200, true, "Receipt status updated successfully", receipt);
    } catch (error) {
        console.error("Update receipt status error:", error);
        return sendResponse(res, 500, false, `Failed to update receipt status: ${error.message}`);
    }
};
