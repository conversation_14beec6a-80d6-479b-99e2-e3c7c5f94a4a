import AppointmentCategory from '../models/appointmentCategory.model.js';
import CategoryService from '../models/categoryService.model.js';
import { sendResponse, paginateResults } from '../utils/responseHandler.js';

/**
 * Create a new service category
 */
const createServiceCategory = async (req, res) => {
    try {
        const categoryData = {
            ...req.body,
            createdBy: req.user?.staffId || req.user?.userId || 1001
        };

        const category = await AppointmentCategory.create(categoryData);
        return sendResponse(res, 201, true, "Service category created successfully", category);
    } catch (error) {
        console.error("Service category creation error:", error);
        return sendResponse(res, 400, false, `Service category creation failed: ${error.message}`);
    }
};

/**
 * Get all service categories with their services
 */
const getAllServiceCategories = async (req, res) => {
    try {
        const {
            page = 1,
            limit = 10,
            sortBy = "displayOrder",
            sortOrder = "asc",
            isActive,
            clinicId,
            includeServices = false
        } = req.query;

        const skip = (parseInt(page) - 1) * parseInt(limit);
        const sortOptions = { [sortBy]: sortOrder === "desc" ? -1 : 1 };

        let query = {};
        if (isActive !== undefined) query.isActive = isActive === 'true';
        if (clinicId) {
            query.$or = [
                { clinicId: null }, // Global categories
                { clinicId: parseInt(clinicId) }
            ];
        }

        const [categories, totalCount] = await Promise.all([
            AppointmentCategory.find(query)
                .sort(sortOptions)
                .skip(skip)
                .limit(parseInt(limit))
                .lean(),
            AppointmentCategory.countDocuments(query)
        ]);

        // Include services if requested
        if (includeServices === 'true') {
            for (let category of categories) {
                const services = await CategoryService.find({
                    appointmentCategoryId: category.appointmentCategoryId,
                    isActive: true
                }).lean();
                category.services = services;
                category.serviceCount = services.length;
            }
        }

        return sendResponse(
            res,
            200,
            true,
            "Service categories retrieved successfully",
            paginateResults(categories, totalCount, page, limit)
        );
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Get single service category by ID with services
 */
const getServiceCategoryById = async (req, res) => {
    try {
        const category = await AppointmentCategory.findOne({
            appointmentCategoryId: parseInt(req.params.serviceCategoryId)
        }).lean();

        if (!category) {
            return sendResponse(res, 404, false, "Service category not found");
        }

        // Get services for this category
        const services = await CategoryService.find({
            appointmentCategoryId: category.appointmentCategoryId,
            isActive: true
        }).lean();

        category.services = services;
        category.serviceCount = services.length;

        return sendResponse(res, 200, true, "Service category retrieved successfully", category);
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Update service category
 */
const updateServiceCategory = async (req, res) => {
    try {
        const updateData = {
            ...req.body,
            modifiedBy: req.user?.staffId || req.user?.userId
        };

        const category = await AppointmentCategory.findOneAndUpdate(
            { appointmentCategoryId: parseInt(req.params.serviceCategoryId) },
            { $set: updateData },
            { new: true, runValidators: true }
        ).lean();

        if (!category) {
            return sendResponse(res, 404, false, "Service category not found");
        }

        return sendResponse(res, 200, true, "Service category updated successfully", category);
    } catch (error) {
        return sendResponse(res, 400, false, error.message);
    }
};

/**
 * Delete service category
 */
const deleteServiceCategory = async (req, res) => {
    try {
        // Check if category has services
        const serviceCount = await CategoryService.countDocuments({
            appointmentCategoryId: parseInt(req.params.serviceCategoryId)
        });

        if (serviceCount > 0) {
            return sendResponse(res, 400, false, "Cannot delete category with existing services");
        }

        const category = await AppointmentCategory.findOneAndDelete({
            appointmentCategoryId: parseInt(req.params.serviceCategoryId)
        });

        if (!category) {
            return sendResponse(res, 404, false, "Service category not found");
        }

        return sendResponse(res, 200, true, "Service category deleted successfully");
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Get services by category
 */
const getServicesByCategory = async (req, res) => {
    try {
        const { serviceCategoryId } = req.params;
        const { clinicId, includeCustom = true } = req.query;

        let query = {
            appointmentCategoryId: parseInt(serviceCategoryId),
            isActive: true
        };

        if (clinicId) {
            if (includeCustom === 'true') {
                query.$or = [
                    { clinicId: null }, // Global services
                    { clinicId: parseInt(clinicId) } // Clinic-specific services
                ];
            } else {
                query.clinicId = null; // Only global services
            }
        }

        const services = await CategoryService.find(query)
            .sort({ categoryServiceName: 1 })
            .lean();

        return sendResponse(res, 200, true, "Services retrieved successfully", services);
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Reorder service categories
 */
const reorderServiceCategories = async (req, res) => {
    try {
        const { categoryOrders } = req.body; // Array of { serviceCategoryId, displayOrder }

        const updatePromises = categoryOrders.map(({ serviceCategoryId, displayOrder }) =>
            ServiceCategory.findOneAndUpdate(
                { serviceCategoryId },
                { displayOrder, modifiedBy: req.user?.staffId || req.user?.userId },
                { new: true }
            )
        );

        await Promise.all(updatePromises);

        return sendResponse(res, 200, true, "Service categories reordered successfully");
    } catch (error) {
        return sendResponse(res, 400, false, error.message);
    }
};

/**
 * Get categories with service counts for dashboard
 */
const getCategoriesWithStats = async (req, res) => {
    try {
        const { clinicId } = req.query;

        let categoryQuery = { isActive: true };
        if (clinicId) {
            categoryQuery.$or = [
                { clinicId: null },
                { clinicId: parseInt(clinicId) }
            ];
        }

        const categories = await ServiceCategory.find(categoryQuery)
            .sort({ displayOrder: 1 })
            .lean();

        // Get service counts for each category
        for (let category of categories) {
            const serviceCount = await Service.countDocuments({
                serviceCategoryId: category.serviceCategoryId,
                isActive: true
            });
            category.serviceCount = serviceCount;
        }

        return sendResponse(res, 200, true, "Categories with stats retrieved successfully", categories);
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

export {
    createServiceCategory,
    getAllServiceCategories,
    getServiceCategoryById,
    updateServiceCategory,
    deleteServiceCategory,
    getServicesByCategory,
    reorderServiceCategories,
    getCategoriesWithStats
};
