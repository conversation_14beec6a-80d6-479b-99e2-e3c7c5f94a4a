import mongoose from 'mongoose';
import Invoice from "../models/invoice.model.js";
import Appointment from "../models/appointment.model.js";
import HealthRecord from "../models/healthRecord.model.js";
import Discount from "../models/discount.model.js";
import { sendResponse } from '../utils/responseHandler.js';

/**
 * Generate invoice from appointment
 */
export const generateInvoiceFromAppointment = async (req, res) => {
    try {
        console.log("Starting invoice generation for appointment:", req.params.appointmentId);
        const { appointmentId } = req.params;

        // Validate appointmentId parameter
        const parsedAppointmentId = parseInt(appointmentId);
        if (isNaN(parsedAppointmentId)) {
            return sendResponse(res, 400, false, "Invalid appointment ID provided");
        }

        // Get appointment details with related data
        const appointment = await Appointment.findOne({
            appointmentId: parsedAppointmentId
        }).populate('petData', 'petId clientId owner')
        .populate('clientData', 'clientId firstName lastName')
        .lean();

        if (!appointment) {
            return sendResponse(res, 404, false, "Appointment not found");
        }

        console.log("Appointment data:", JSON.stringify(appointment, null, 2));

        // Get client ID from multiple possible sources
        let clientId = appointment.clientId;

        // Try to get clientId from pet data if not directly available
        if (!clientId && appointment.petId) {
            const Pet = (await import("../models/pet.model.js")).default;
            const pet = await Pet.findOne({ petId: appointment.petId }).lean();
            console.log("Pet data:", JSON.stringify(pet, null, 2));

            if (pet) {
                clientId = pet.clientId || pet.owner;
            }
        }

        // Try to get from populated petData
        if (!clientId && appointment.petData) {
            clientId = appointment.petData.clientId || appointment.petData.owner;
        }

        // Try to get from populated clientData
        if (!clientId && appointment.clientData) {
            clientId = appointment.clientData.clientId;
        }

        console.log("Resolved clientId:", clientId);

        // Validate required fields for invoice generation
        if (!clientId) {
            return sendResponse(res, 400, false, "Cannot determine client ID for this appointment. Please ensure the appointment has a valid client association.");
        }

        if (!appointment.petId) {
            return sendResponse(res, 400, false, "Appointment must have a pet ID to generate invoice");
        }

        // Check if invoice already exists
        const existingInvoice = await Invoice.findOne({ appointmentId: appointment.appointmentId });
        if (existingInvoice) {
            return sendResponse(res, 200, true, "Invoice already exists", existingInvoice);
        }

        // Get health record if appointment is completed
        let healthRecord = null;
        if (appointment.status === 'completed') {
            healthRecord = await HealthRecord.findOne({
                appointmentId: appointment.appointmentId
            }).lean();
        }

        // Calculate charges from appointment categories and services
        const appointmentCategories = [];
        const services = []; // Legacy services for backward compatibility

        if (appointment.appointmentCategories && appointment.appointmentCategories.length > 0) {
            for (const category of appointment.appointmentCategories) {
                // Get category details for pricing
                const categoryDetails = await mongoose.model('AppointmentCategory').findOne({
                    appointmentCategoryId: category.appointmentCategoryId
                }).lean();

                const categoryCharge = categoryDetails?.defaultCharge || 0;
                const categoryDiscountPercentage = categoryDetails?.defaultDiscountPercentage || 0;
                const categoryDiscount = (categoryCharge * categoryDiscountPercentage) / 100;
                const categoryTotal = categoryCharge - categoryDiscount;

                // Process category services
                const categoryServices = [];
                if (category.categoryServices && category.categoryServices.length > 0) {
                    for (const service of category.categoryServices) {
                        const servicePrice = service.price || 0;
                        const serviceDiscountPercentage = 0; // Default no discount
                        const serviceDiscount = (servicePrice * serviceDiscountPercentage) / 100;
                        const serviceTotalPrice = servicePrice - serviceDiscount;

                        categoryServices.push({
                            categoryServiceId: service.categoryServiceId,
                            serviceName: service.categoryServiceName,
                            description: service.notes || '',
                            quantity: 1,
                            unitPrice: servicePrice,
                            serviceDiscount: serviceDiscount,
                            serviceDiscountPercentage: serviceDiscountPercentage,
                            totalPrice: serviceTotalPrice,
                            performedBy: service.performedBy,
                            performedByName: service.performedByName,
                            notes: service.notes
                        });
                    }
                }

                appointmentCategories.push({
                    appointmentCategoryId: category.appointmentCategoryId,
                    categoryName: category.categoryName,
                    categoryCharge: categoryCharge,
                    categoryDiscount: categoryDiscount,
                    categoryDiscountPercentage: categoryDiscountPercentage,
                    categoryTotal: categoryTotal,
                    staffAssigned: category.staffAssigned,
                    staffAssignedName: category.staffAssignedName,
                    categoryServices: categoryServices
                });
            }
        } else {
            // Fallback to appointment type if no categories
            const appointmentType = appointment.appointmentTypes?.[0];
            if (appointmentType) {
                services.push({
                    serviceId: appointmentType.appointmentTypeId,
                    serviceName: appointmentType.name || 'Veterinary Service',
                    description: appointment.reason,
                    quantity: 1,
                    unitPrice: appointmentType.price || 0,
                    totalPrice: appointmentType.price || 0
                });
            }
        }

        // Add medications from health record
        const medications = [];
        if (healthRecord?.medications?.length > 0) {
            healthRecord.medications.forEach((med, index) => {
                medications.push({
                    inventoryItemId: index + 1, // Placeholder
                    medicationName: med.name,
                    quantity: 1,
                    unitPrice: 100, // Default price - should come from inventory
                    totalPrice: 100
                });
            });
        }

        // Calculate subtotal
        const categoriesTotal = appointmentCategories.reduce((sum, category) => {
            const categoryServicesTotal = category.categoryServices.reduce((serviceSum, service) => serviceSum + service.totalPrice, 0);
            return sum + category.categoryTotal + categoryServicesTotal;
        }, 0);
        const servicesTotal = services.reduce((sum, service) => sum + service.totalPrice, 0);
        const medicationsTotal = medications.reduce((sum, med) => sum + med.totalPrice, 0);
        const subtotal = categoriesTotal + servicesTotal + medicationsTotal;

        // Add after-hours charge if applicable
        const afterHoursCharge = healthRecord?.billingDetails?.afterHoursCharge || 0;

        // Calculate tax (16% VAT for Kenya)
        const taxRate = 16;
        const taxableAmount = subtotal + afterHoursCharge;
        const taxAmount = (taxableAmount * taxRate) / 100;

        // Calculate total
        const totalAmount = taxableAmount + taxAmount;

        // Set due date (7 days from invoice date)
        const dueDate = new Date();
        dueDate.setDate(dueDate.getDate() + 7);

        // Generate invoice number
        const invoiceCount = await Invoice.countDocuments();
        const invoiceNumber = `INV-${String(invoiceCount + 1).padStart(6, '0')}`;

        // Create invoice
        const invoice = await Invoice.create({
            invoiceNumber,
            appointmentId: appointment.appointmentId,
            healthRecordId: healthRecord?.healthRecordId,
            clientId: clientId, // Use the resolved clientId
            petId: appointment.petId,
            clinicId: appointment.clinicId || req.user?.clinicId || 1019,
            dueDate,
            appointmentCategories,
            services,
            medications,
            subtotal,
            afterHoursCharge,
            taxRate,
            taxAmount,
            totalAmount,
            amountDue: totalAmount,
            currency: 'KES',
            generatedBy: req.user?.staffId || req.user?.userId || 1001
        });

        return sendResponse(res, 201, true, "Invoice generated successfully", invoice);
    } catch (error) {
        console.error("Generate invoice error:", error);
        return sendResponse(res, 500, false, `Failed to generate invoice: ${error.message}`);
    }
};

/**
 * Get invoice by ID
 */
export const getInvoiceById = async (req, res) => {
    try {
        const { invoiceId } = req.params;

        const invoice = await Invoice.findOne({
            invoiceId: parseInt(invoiceId)
        }).lean();

        if (!invoice) {
            return sendResponse(res, 404, false, "Invoice not found");
        }

        // Get related data
        const [appointment, healthRecord] = await Promise.all([
            Appointment.findOne({ appointmentId: invoice.appointmentId }).lean(),
            invoice.healthRecordId ? HealthRecord.findOne({ healthRecordId: invoice.healthRecordId }).lean() : null
        ]);

        // Add related data to invoice
        invoice.appointmentData = appointment;
        invoice.healthRecordData = healthRecord;

        return sendResponse(res, 200, true, "Invoice retrieved successfully", invoice);
    } catch (error) {
        console.error("Get invoice error:", error);
        return sendResponse(res, 500, false, `Failed to retrieve invoice: ${error.message}`);
    }
};

/**
 * Get invoice by appointment ID
 */
export const getInvoiceByAppointment = async (req, res) => {
    try {
        const { appointmentId } = req.params;

        // Validate appointmentId parameter
        const parsedAppointmentId = parseInt(appointmentId);
        if (isNaN(parsedAppointmentId)) {
            return sendResponse(res, 400, false, "Invalid appointment ID provided");
        }

        const invoice = await Invoice.findOne({
            appointmentId: parsedAppointmentId
        }).lean();

        if (!invoice) {
            return sendResponse(res, 404, false, "Invoice not found for this appointment");
        }

        return sendResponse(res, 200, true, "Invoice retrieved successfully", invoice);
    } catch (error) {
        console.error("Get invoice by appointment error:", error);
        return sendResponse(res, 500, false, `Failed to retrieve invoice: ${error.message}`);
    }
};

/**
 * Apply discount to invoice
 */
export const applyDiscountToInvoice = async (req, res) => {
    try {
        const { invoiceId } = req.params;
        const { discountType, discountValue, reason } = req.body;

        const invoice = await Invoice.findOne({ invoiceId: parseInt(invoiceId) });

        if (!invoice) {
            return sendResponse(res, 404, false, "Invoice not found");
        }

        if (invoice.paymentStatus === 'paid') {
            return sendResponse(res, 400, false, "Cannot apply discount to paid invoice");
        }

        // Calculate discount amount
        let discountAmount = 0;
        if (discountType === 'percentage') {
            discountAmount = (invoice.subtotal * discountValue) / 100;
        } else {
            discountAmount = discountValue;
        }

        // Create discount record
        const discount = await Discount.create({
            healthRecordId: invoice.healthRecordId,
            appointmentId: invoice.appointmentId,
            discountType,
            discountValue,
            discountAmount,
            reason,
            authorizedBy: req.user?.staffId || req.user?.userId || 1001,
            clientId: invoice.clientId,
            clinicId: invoice.clinicId
        });

        // Add discount to invoice
        invoice.discounts.push({
            discountId: discount.discountId,
            discountType,
            discountValue,
            discountAmount,
            reason,
            appliedBy: req.user?.staffId || req.user?.userId || 1001
        });

        // Recalculate totals
        invoice.totalDiscounts = invoice.discounts.reduce((sum, d) => sum + d.discountAmount, 0);
        const taxableAmount = invoice.subtotal + invoice.afterHoursCharge - invoice.totalDiscounts;
        invoice.taxAmount = (taxableAmount * invoice.taxRate) / 100;
        invoice.totalAmount = taxableAmount + invoice.taxAmount;
        invoice.amountDue = invoice.totalAmount - invoice.amountPaid;

        await invoice.save();

        return sendResponse(res, 200, true, "Discount applied successfully", invoice);
    } catch (error) {
        console.error("Apply discount error:", error);
        return sendResponse(res, 500, false, `Failed to apply discount: ${error.message}`);
    }
};

/**
 * Update invoice with new categories, services, and charges
 */
export const updateInvoiceCharges = async (req, res) => {
    try {
        const { invoiceId } = req.params;
        const { appointmentCategories, services, medications, discounts } = req.body;

        const invoice = await Invoice.findOne({ invoiceId: parseInt(invoiceId) });

        if (!invoice) {
            return sendResponse(res, 404, false, "Invoice not found");
        }

        if (invoice.paymentStatus === 'paid') {
            return sendResponse(res, 400, false, "Cannot update charges for paid invoice");
        }

        // Update appointment categories with new charges and discounts
        if (appointmentCategories) {
            invoice.appointmentCategories = appointmentCategories.map(category => ({
                ...category,
                categoryTotal: category.categoryCharge - category.categoryDiscount,
                categoryServices: category.categoryServices?.map(service => ({
                    ...service,
                    totalPrice: service.unitPrice * service.quantity - service.serviceDiscount
                })) || []
            }));
        }

        // Update services if provided
        if (services) {
            invoice.services = services;
        }

        // Update medications if provided
        if (medications) {
            invoice.medications = medications;
        }

        // Recalculate totals
        const categoriesTotal = invoice.appointmentCategories.reduce((sum, category) => {
            const categoryServicesTotal = category.categoryServices.reduce((serviceSum, service) => serviceSum + service.totalPrice, 0);
            return sum + category.categoryTotal + categoryServicesTotal;
        }, 0);
        const servicesTotal = invoice.services.reduce((sum, service) => sum + service.totalPrice, 0);
        const medicationsTotal = invoice.medications.reduce((sum, med) => sum + med.totalPrice, 0);

        invoice.subtotal = categoriesTotal + servicesTotal + medicationsTotal;

        // Apply any new discounts
        if (discounts && discounts.length > 0) {
            invoice.discounts = discounts;
            invoice.totalDiscounts = discounts.reduce((sum, d) => sum + d.discountAmount, 0);
        }

        // Recalculate tax and total
        const taxableAmount = invoice.subtotal + invoice.afterHoursCharge - invoice.totalDiscounts;
        invoice.taxAmount = (taxableAmount * invoice.taxRate) / 100;
        invoice.totalAmount = taxableAmount + invoice.taxAmount;
        invoice.amountDue = invoice.totalAmount - invoice.amountPaid;

        await invoice.save();

        return sendResponse(res, 200, true, "Invoice charges updated successfully", invoice);
    } catch (error) {
        console.error("Update invoice charges error:", error);
        return sendResponse(res, 500, false, `Failed to update invoice charges: ${error.message}`);
    }
};

/**
 * Apply discount to specific category or service
 */
export const applyCategoryDiscount = async (req, res) => {
    try {
        const { invoiceId } = req.params;
        const {
            categoryId,
            serviceId,
            discountType,
            discountValue,
            reason
        } = req.body;

        const invoice = await Invoice.findOne({ invoiceId: parseInt(invoiceId) });

        if (!invoice) {
            return sendResponse(res, 404, false, "Invoice not found");
        }

        if (invoice.paymentStatus === 'paid') {
            return sendResponse(res, 400, false, "Cannot apply discount to paid invoice");
        }

        // Find and update the category or service
        if (categoryId) {
            const categoryIndex = invoice.appointmentCategories.findIndex(
                cat => cat.appointmentCategoryId === categoryId
            );

            if (categoryIndex === -1) {
                return sendResponse(res, 404, false, "Category not found in invoice");
            }

            const category = invoice.appointmentCategories[categoryIndex];

            // Calculate discount
            let discountAmount = 0;
            if (discountType === 'percentage') {
                discountAmount = (category.categoryCharge * discountValue) / 100;
                category.categoryDiscountPercentage = discountValue;
            } else {
                discountAmount = discountValue;
            }

            category.categoryDiscount = discountAmount;
            category.categoryTotal = category.categoryCharge - discountAmount;

        } else if (serviceId) {
            // Find service in categories
            let serviceFound = false;
            for (const category of invoice.appointmentCategories) {
                const serviceIndex = category.categoryServices.findIndex(
                    service => service.categoryServiceId === serviceId
                );

                if (serviceIndex !== -1) {
                    const service = category.categoryServices[serviceIndex];

                    // Calculate discount
                    let discountAmount = 0;
                    if (discountType === 'percentage') {
                        discountAmount = (service.unitPrice * service.quantity * discountValue) / 100;
                        service.serviceDiscountPercentage = discountValue;
                    } else {
                        discountAmount = discountValue;
                    }

                    service.serviceDiscount = discountAmount;
                    service.totalPrice = (service.unitPrice * service.quantity) - discountAmount;
                    serviceFound = true;
                    break;
                }
            }

            if (!serviceFound) {
                return sendResponse(res, 404, false, "Service not found in invoice");
            }
        }

        // Recalculate totals
        const categoriesTotal = invoice.appointmentCategories.reduce((sum, category) => {
            const categoryServicesTotal = category.categoryServices.reduce((serviceSum, service) => serviceSum + service.totalPrice, 0);
            return sum + category.categoryTotal + categoryServicesTotal;
        }, 0);
        const servicesTotal = invoice.services.reduce((sum, service) => sum + service.totalPrice, 0);
        const medicationsTotal = invoice.medications.reduce((sum, med) => sum + med.totalPrice, 0);

        invoice.subtotal = categoriesTotal + servicesTotal + medicationsTotal;

        // Recalculate tax and total
        const taxableAmount = invoice.subtotal + invoice.afterHoursCharge - invoice.totalDiscounts;
        invoice.taxAmount = (taxableAmount * invoice.taxRate) / 100;
        invoice.totalAmount = taxableAmount + invoice.taxAmount;
        invoice.amountDue = invoice.totalAmount - invoice.amountPaid;

        await invoice.save();

        return sendResponse(res, 200, true, "Discount applied successfully", invoice);
    } catch (error) {
        console.error("Apply category discount error:", error);
        return sendResponse(res, 500, false, `Failed to apply discount: ${error.message}`);
    }
};

/**
 * Update invoice status
 */
export const updateInvoiceStatus = async (req, res) => {
    try {
        const { invoiceId } = req.params;
        const { status } = req.body;

        const invoice = await Invoice.findOneAndUpdate(
            { invoiceId: parseInt(invoiceId) },
            { status },
            { new: true }
        );

        if (!invoice) {
            return sendResponse(res, 404, false, "Invoice not found");
        }

        return sendResponse(res, 200, true, "Invoice status updated successfully", invoice);
    } catch (error) {
        console.error("Update invoice status error:", error);
        return sendResponse(res, 500, false, `Failed to update invoice status: ${error.message}`);
    }
};
