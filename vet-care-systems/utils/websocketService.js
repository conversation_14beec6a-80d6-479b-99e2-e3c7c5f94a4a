/**
 * WebSocket Service for Real-time Updates
 * 
 * This service provides real-time communication capabilities for the veterinary care system,
 * enabling live updates for appointments, notifications, and system events.
 */

import { Server } from 'socket.io';
import jwt from 'jsonwebtoken';
import { JWT_SECRET } from '../config/env.js';
import User from '../models/user.model.js';
import Staff from '../models/staff.model.js';

class WebSocketService {
    constructor() {
        this.io = null;
        this.connectedUsers = new Map(); // userId/staffId -> socket info
        this.clinicRooms = new Map(); // clinicId -> Set of socket IDs
        this.userSockets = new Map(); // socketId -> user info
    }

    /**
     * Initialize WebSocket server
     * @param {Object} server - HTTP server instance
     */
    initialize(server) {
        this.io = new Server(server, {
            cors: {
                origin: process.env.FRONTEND_URL || "http://localhost:8080",
                methods: ["GET", "POST"],
                credentials: true
            },
            transports: ['websocket', 'polling']
        });

        this.setupMiddleware();
        this.setupEventHandlers();
        
        console.log('WebSocket service initialized');
    }

    /**
     * Setup authentication middleware
     */
    setupMiddleware() {
        this.io.use(async (socket, next) => {
            try {
                const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.split(' ')[1];
                
                if (!token) {
                    return next(new Error('Authentication token required'));
                }

                // Verify JWT token
                const decoded = jwt.verify(token, JWT_SECRET);
                
                // Get user/staff information
                let userInfo = null;
                
                // Check if it's a staff member first
                if (decoded.staffId || decoded.userId) {
                    const staff = await Staff.findOne({ 
                        $or: [
                            { staffId: decoded.staffId },
                            { userId: decoded.userId }
                        ]
                    }).select('staffId userId firstName lastName email clinicId primaryClinicId currentClinicId').lean();
                    
                    if (staff) {
                        userInfo = {
                            type: 'staff',
                            staffId: staff.staffId,
                            userId: staff.userId,
                            name: `${staff.firstName} ${staff.lastName}`,
                            email: staff.email,
                            clinicId: staff.currentClinicId || staff.primaryClinicId || staff.clinicId
                        };
                    }
                }
                
                // If not staff, check user table
                if (!userInfo && decoded.userId) {
                    const user = await User.findOne({ userId: decoded.userId })
                        .select('userId firstName lastName email roleId').lean();
                    
                    if (user) {
                        userInfo = {
                            type: 'user',
                            userId: user.userId,
                            name: `${user.firstName} ${user.lastName}`,
                            email: user.email,
                            roleId: user.roleId
                        };
                    }
                }
                
                if (!userInfo) {
                    return next(new Error('Invalid authentication token'));
                }
                
                socket.userInfo = userInfo;
                next();
            } catch (error) {
                console.error('WebSocket authentication error:', error);
                next(new Error('Authentication failed'));
            }
        });
    }

    /**
     * Setup event handlers
     */
    setupEventHandlers() {
        this.io.on('connection', (socket) => {
            this.handleConnection(socket);
            
            socket.on('join_clinic', (data) => this.handleJoinClinic(socket, data));
            socket.on('leave_clinic', (data) => this.handleLeaveClinic(socket, data));
            socket.on('appointment_update', (data) => this.handleAppointmentUpdate(socket, data));
            socket.on('notification_read', (data) => this.handleNotificationRead(socket, data));
            socket.on('disconnect', () => this.handleDisconnection(socket));
            
            // Custom event handlers
            socket.on('ping', () => socket.emit('pong'));
        });
    }

    /**
     * Handle new connection
     */
    handleConnection(socket) {
        const userInfo = socket.userInfo;
        const userId = userInfo.staffId || userInfo.userId;
        
        console.log(`User connected: ${userInfo.name} (${userInfo.type}:${userId})`);
        
        // Store connection info
        this.connectedUsers.set(userId, {
            socketId: socket.id,
            userInfo,
            connectedAt: new Date()
        });
        
        this.userSockets.set(socket.id, userInfo);
        
        // Join clinic room if staff member
        if (userInfo.type === 'staff' && userInfo.clinicId) {
            this.joinClinicRoom(socket, userInfo.clinicId);
        }
        
        // Send connection confirmation
        socket.emit('connected', {
            message: 'Connected successfully',
            userInfo: {
                name: userInfo.name,
                type: userInfo.type,
                clinicId: userInfo.clinicId
            }
        });
        
        // Send any pending notifications
        this.sendPendingNotifications(socket, userId);
    }

    /**
     * Handle disconnection
     */
    handleDisconnection(socket) {
        const userInfo = this.userSockets.get(socket.id);
        
        if (userInfo) {
            const userId = userInfo.staffId || userInfo.userId;
            console.log(`User disconnected: ${userInfo.name} (${userInfo.type}:${userId})`);
            
            // Remove from tracking
            this.connectedUsers.delete(userId);
            this.userSockets.delete(socket.id);
            
            // Remove from clinic rooms
            if (userInfo.clinicId) {
                this.leaveClinicRoom(socket, userInfo.clinicId);
            }
        }
    }

    /**
     * Handle joining clinic room
     */
    handleJoinClinic(socket, data) {
        const { clinicId } = data;
        this.joinClinicRoom(socket, clinicId);
    }

    /**
     * Handle leaving clinic room
     */
    handleLeaveClinic(socket, data) {
        const { clinicId } = data;
        this.leaveClinicRoom(socket, clinicId);
    }

    /**
     * Join clinic room
     */
    joinClinicRoom(socket, clinicId) {
        const roomName = `clinic_${clinicId}`;
        socket.join(roomName);
        
        if (!this.clinicRooms.has(clinicId)) {
            this.clinicRooms.set(clinicId, new Set());
        }
        this.clinicRooms.get(clinicId).add(socket.id);
        
        console.log(`Socket ${socket.id} joined clinic room ${clinicId}`);
    }

    /**
     * Leave clinic room
     */
    leaveClinicRoom(socket, clinicId) {
        const roomName = `clinic_${clinicId}`;
        socket.leave(roomName);
        
        if (this.clinicRooms.has(clinicId)) {
            this.clinicRooms.get(clinicId).delete(socket.id);
            if (this.clinicRooms.get(clinicId).size === 0) {
                this.clinicRooms.delete(clinicId);
            }
        }
        
        console.log(`Socket ${socket.id} left clinic room ${clinicId}`);
    }

    /**
     * Handle appointment updates
     */
    handleAppointmentUpdate(socket, data) {
        const userInfo = socket.userInfo;
        
        // Broadcast to clinic room
        if (userInfo.clinicId) {
            socket.to(`clinic_${userInfo.clinicId}`).emit('appointment_updated', {
                ...data,
                updatedBy: userInfo.name,
                timestamp: new Date()
            });
        }
    }

    /**
     * Handle notification read
     */
    handleNotificationRead(socket, data) {
        const { notificationId } = data;
        // Mark notification as read in database
        // This would integrate with your notification system
        console.log(`Notification ${notificationId} marked as read by ${socket.userInfo.name}`);
    }

    /**
     * Send pending notifications
     */
    async sendPendingNotifications(socket, userId) {
        // This would integrate with your notification system
        // For now, just send a welcome message
        socket.emit('notification', {
            type: 'welcome',
            message: 'Welcome to VetCare System',
            timestamp: new Date()
        });
    }

    /**
     * Broadcast to all connected users
     */
    broadcastToAll(event, data) {
        this.io.emit(event, data);
    }

    /**
     * Broadcast to specific clinic
     */
    broadcastToClinic(clinicId, event, data) {
        this.io.to(`clinic_${clinicId}`).emit(event, data);
    }

    /**
     * Send to specific user
     */
    sendToUser(userId, event, data) {
        const userConnection = this.connectedUsers.get(userId);
        if (userConnection) {
            this.io.to(userConnection.socketId).emit(event, data);
            return true;
        }
        return false;
    }

    /**
     * Send appointment notification
     */
    sendAppointmentNotification(clinicId, appointmentData) {
        this.broadcastToClinic(clinicId, 'appointment_notification', {
            type: 'appointment_created',
            appointment: appointmentData,
            timestamp: new Date()
        });
    }

    /**
     * Send appointment update
     */
    sendAppointmentUpdate(clinicId, appointmentData, updateType = 'updated') {
        this.broadcastToClinic(clinicId, 'appointment_update', {
            type: updateType,
            appointment: appointmentData,
            timestamp: new Date()
        });
    }

    /**
     * Send inventory alert
     */
    sendInventoryAlert(clinicId, inventoryData) {
        this.broadcastToClinic(clinicId, 'inventory_alert', {
            type: 'low_stock',
            inventory: inventoryData,
            timestamp: new Date()
        });
    }

    /**
     * Send system notification
     */
    sendSystemNotification(message, severity = 'info', targetUsers = null) {
        const notification = {
            type: 'system',
            message,
            severity,
            timestamp: new Date()
        };

        if (targetUsers) {
            targetUsers.forEach(userId => {
                this.sendToUser(userId, 'system_notification', notification);
            });
        } else {
            this.broadcastToAll('system_notification', notification);
        }
    }

    /**
     * Get connection statistics
     */
    getConnectionStats() {
        return {
            totalConnections: this.connectedUsers.size,
            clinicRooms: Array.from(this.clinicRooms.entries()).map(([clinicId, sockets]) => ({
                clinicId,
                connections: sockets.size
            })),
            userTypes: Array.from(this.connectedUsers.values()).reduce((acc, conn) => {
                acc[conn.userInfo.type] = (acc[conn.userInfo.type] || 0) + 1;
                return acc;
            }, {})
        };
    }
}

// Create singleton instance
const webSocketService = new WebSocketService();

export default webSocketService;
