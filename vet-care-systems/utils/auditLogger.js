/**
 * Audit Logging System
 * 
 * This utility provides comprehensive audit logging for all critical actions
 * in the veterinary care system, ensuring compliance and security monitoring.
 */

import winston from 'winston';
import mongoose from 'mongoose';
import { NODE_ENV } from '../config/env.js';

/**
 * Audit Log Schema
 */
const auditLogSchema = new mongoose.Schema({
    // Action details
    action: {
        type: String,
        required: true,
        enum: [
            'CREATE', 'READ', 'UPDATE', 'DELETE', 'OPTIONS',
            'LOGIN', 'LOGOUT', 'LOGIN_FAILED',
            'PERMISSION_GRANTED', 'PERMISSION_DENIED',
            'DATA_EXPORT', 'DATA_IMPORT',
            'SYSTEM_CONFIG_CHANGE',
            'APPOINTMENT_CREATED', 'APPOINTMENT_UPDATED', 'APPOINTMENT_CANCELLED',
            'MEDICAL_RECORD_CREATED', 'MEDICAL_RECORD_UPDATED',
            'INVOICE_GENERATED', 'PAYMENT_PROCESSED',
            'USER_CREATED', 'USER_UPDATED', 'USER_DEACTIVATED',
            'CLINIC_CREATED', 'CLINIC_UPDATED',
            'PET_REGISTERED', 'PET_UPDATED'
        ],
        index: true
    },
    
    // Entity information
    entityType: {
        type: String,
        required: true,
        enum: [
            'User', 'Staff', 'Client', 'Pet', 'Clinic',
            'Appointment', 'HealthRecord', 'Invoice', 'Payment',
            'Inventory', 'Role', 'Permission', 'System'
        ],
        index: true
    },
    
    entityId: {
        type: String, // Can be numeric ID or ObjectId
        required: true,
        index: true
    },
    
    // User who performed the action
    performedBy: {
        userId: { type: Number, index: true },
        staffId: { type: Number, index: true },
        userType: { type: String, enum: ['user', 'staff', 'system'] },
        email: String,
        name: String
    },
    
    // Context information
    clinicId: { type: Number, index: true },
    sessionId: String,
    ipAddress: String,
    userAgent: String,
    
    // Change details
    changes: {
        before: mongoose.Schema.Types.Mixed,
        after: mongoose.Schema.Types.Mixed,
        fields: [String] // List of changed fields
    },
    
    // Additional metadata
    metadata: {
        reason: String,
        notes: String,
        severity: {
            type: String,
            enum: ['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'],
            default: 'MEDIUM'
        },
        category: {
            type: String,
            enum: ['SECURITY', 'DATA', 'SYSTEM', 'BUSINESS', 'COMPLIANCE'],
            default: 'BUSINESS'
        }
    },
    
    // Request details
    requestDetails: {
        method: String,
        url: String,
        body: mongoose.Schema.Types.Mixed,
        query: mongoose.Schema.Types.Mixed,
        params: mongoose.Schema.Types.Mixed
    },
    
    // Response details
    responseDetails: {
        statusCode: Number,
        success: Boolean,
        message: String
    },
    
    // Timing
    timestamp: { type: Date, default: Date.now, index: true },
    processingTime: Number // in milliseconds
    
}, {
    timestamps: true,
    collection: 'audit_logs'
});

// Indexes for efficient querying
auditLogSchema.index({ action: 1, timestamp: -1 });
auditLogSchema.index({ entityType: 1, entityId: 1, timestamp: -1 });
auditLogSchema.index({ 'performedBy.userId': 1, timestamp: -1 });
auditLogSchema.index({ 'performedBy.staffId': 1, timestamp: -1 });
auditLogSchema.index({ clinicId: 1, timestamp: -1 });
auditLogSchema.index({ 'metadata.severity': 1, timestamp: -1 });
auditLogSchema.index({ 'metadata.category': 1, timestamp: -1 });

const AuditLog = mongoose.model('AuditLog', auditLogSchema);

/**
 * Winston logger configuration for audit logs
 */
const auditLogger = winston.createLogger({
    level: 'info',
    format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true }),
        winston.format.json()
    ),
    defaultMeta: { service: 'vet-care-audit' },
    transports: [
        // Write all audit logs to audit.log
        new winston.transports.File({ 
            filename: 'logs/audit.log',
            maxsize: 10485760, // 10MB
            maxFiles: 10
        }),
        
        // Write critical audit logs to critical-audit.log
        new winston.transports.File({ 
            filename: 'logs/critical-audit.log',
            level: 'error',
            maxsize: 10485760, // 10MB
            maxFiles: 5
        })
    ]
});

// Add console transport in development
if (NODE_ENV === 'development') {
    auditLogger.add(new winston.transports.Console({
        format: winston.format.simple()
    }));
}

/**
 * Log audit event
 * @param {Object} auditData - Audit log data
 * @returns {Promise<Object>} Created audit log
 */
export const logAuditEvent = async (auditData) => {
    try {
        // Create audit log in database
        const auditLog = new AuditLog(auditData);
        await auditLog.save();
        
        // Also log to Winston for file-based logging
        const logLevel = auditData.metadata?.severity === 'CRITICAL' ? 'error' : 'info';
        auditLogger.log(logLevel, 'Audit Event', auditData);
        
        return auditLog;
    } catch (error) {
        console.error('Failed to log audit event:', error);
        // Still log to Winston even if database fails
        auditLogger.error('Failed to save audit log to database', { error: error.message, auditData });
        throw error;
    }
};

/**
 * Middleware to automatically log API requests
 * @param {Object} options - Configuration options
 * @returns {Function} Express middleware
 */
export const auditMiddleware = (options = {}) => {
    const {
        excludePaths = ['/health', '/metrics'],
        excludeMethods = ['GET'],
        logBody = false,
        logResponse = false
    } = options;
    
    return async (req, res, next) => {
        const startTime = Date.now();
        
        // Skip excluded paths and methods
        if (excludePaths.some(path => req.path.includes(path)) ||
            excludeMethods.includes(req.method)) {
            return next();
        }
        
        // Store original res.json to capture response
        const originalJson = res.json;
        let responseData = null;
        
        if (logResponse) {
            res.json = function(data) {
                responseData = data;
                return originalJson.call(this, data);
            };
        }
        
        // Continue with request processing
        res.on('finish', async () => {
            try {
                const processingTime = Date.now() - startTime;
                
                const auditData = {
                    action: getActionFromRequest(req),
                    entityType: getEntityTypeFromPath(req.path),
                    entityId: req.params.id || req.params.userId || req.params.clinicId || 'unknown',
                    performedBy: extractUserInfo(req),
                    clinicId: req.user?.clinicId || null,
                    sessionId: req.sessionID,
                    ipAddress: req.ip || req.connection.remoteAddress,
                    userAgent: req.get('User-Agent'),
                    requestDetails: {
                        method: req.method,
                        url: req.originalUrl,
                        body: logBody ? sanitizeRequestBody(req.body) : undefined,
                        query: req.query,
                        params: req.params
                    },
                    responseDetails: {
                        statusCode: res.statusCode,
                        success: res.statusCode < 400,
                        message: responseData?.message || undefined
                    },
                    metadata: {
                        severity: getSeverityFromStatusCode(res.statusCode),
                        category: getCategoryFromPath(req.path)
                    },
                    processingTime
                };
                
                await logAuditEvent(auditData);
            } catch (error) {
                console.error('Audit middleware error:', error);
            }
        });
        
        next();
    };
};

/**
 * Helper functions
 */

function getActionFromRequest(req) {
    const method = req.method.toUpperCase();
    const path = req.path.toLowerCase();
    
    if (path.includes('login')) return 'LOGIN';
    if (path.includes('logout')) return 'LOGOUT';
    
    switch (method) {
        case 'POST': return 'CREATE';
        case 'GET': return 'READ';
        case 'PUT':
        case 'PATCH': return 'UPDATE';
        case 'DELETE': return 'DELETE';
        default: return method;
    }
}

function getEntityTypeFromPath(path) {
    const segments = path.split('/').filter(Boolean);
    const entityMap = {
        'users': 'User',
        'staff': 'Staff',
        'clients': 'Client',
        'pets': 'Pet',
        'clinics': 'Clinic',
        'appointments': 'Appointment',
        'health-records': 'HealthRecord',
        'invoices': 'Invoice',
        'payments': 'Payment',
        'inventory': 'Inventory',
        'roles': 'Role',
        'permissions': 'Permission'
    };
    
    for (const segment of segments) {
        if (entityMap[segment]) {
            return entityMap[segment];
        }
    }
    
    return 'System';
}

function extractUserInfo(req) {
    if (!req.user) return null;
    
    return {
        userId: req.user.userId || null,
        staffId: req.user.staffId || null,
        userType: req.user.userType || 'unknown',
        email: req.user.email || null,
        name: req.user.name || null
    };
}

function sanitizeRequestBody(body) {
    if (!body) return null;
    
    const sanitized = { ...body };
    
    // Remove sensitive fields
    const sensitiveFields = ['password', 'token', 'secret', 'key'];
    sensitiveFields.forEach(field => {
        if (sanitized[field]) {
            sanitized[field] = '[REDACTED]';
        }
    });
    
    return sanitized;
}

function getSeverityFromStatusCode(statusCode) {
    if (statusCode >= 500) return 'CRITICAL';
    if (statusCode >= 400) return 'HIGH';
    if (statusCode >= 300) return 'MEDIUM';
    return 'LOW';
}

function getCategoryFromPath(path) {
    if (path.includes('auth') || path.includes('login')) return 'SECURITY';
    if (path.includes('admin') || path.includes('system')) return 'SYSTEM';
    if (path.includes('export') || path.includes('import')) return 'DATA';
    if (path.includes('audit') || path.includes('compliance')) return 'COMPLIANCE';
    return 'BUSINESS';
}

/**
 * Query audit logs
 * @param {Object} filters - Query filters
 * @param {Object} options - Query options (pagination, sorting)
 * @returns {Promise<Object>} Query results
 */
export const queryAuditLogs = async (filters = {}, options = {}) => {
    try {
        const {
            page = 1,
            limit = 50,
            sortBy = 'timestamp',
            sortOrder = 'desc'
        } = options;
        
        const query = AuditLog.find(filters);
        
        // Apply sorting
        const sort = {};
        sort[sortBy] = sortOrder === 'desc' ? -1 : 1;
        query.sort(sort);
        
        // Apply pagination
        const skip = (page - 1) * limit;
        query.skip(skip).limit(limit);
        
        const [logs, total] = await Promise.all([
            query.exec(),
            AuditLog.countDocuments(filters)
        ]);
        
        return {
            logs,
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit)
            }
        };
    } catch (error) {
        console.error('Error querying audit logs:', error);
        throw error;
    }
};

export default {
    logAuditEvent,
    auditMiddleware,
    queryAuditLogs,
    AuditLog
};
