import mongoose from 'mongoose';

/**
 * Denormalization Synchronization Utility
 * 
 * This utility manages the synchronization of denormalized data across models
 * to maintain data consistency while providing performance benefits.
 */

/**
 * Sync pet data changes to all related appointments
 * @param {Number} petId - The pet ID that was updated
 * @param {Object} updatedFields - The fields that were updated
 */
export const syncPetDataToAppointments = async (petId, updatedFields) => {
    try {
        const Appointment = mongoose.model('Appointment');
        const Pet = mongoose.model('Pet');
        const Species = mongoose.model('Species');
        const Breed = mongoose.model('Breed');

        // Get the updated pet with populated species and breed
        const pet = await Pet.findOne({ petId })
            .populate('speciesId', 'speciesName')
            .populate('breedId', 'breedName');

        if (!pet) {
            console.warn(`Pet with ID ${petId} not found for sync`);
            return;
        }

        // Prepare update object for appointments
        const appointmentUpdate = {};
        
        if (updatedFields.name || updatedFields.petName) {
            appointmentUpdate.petName = pet.name;
        }
        
        if (updatedFields.speciesId) {
            appointmentUpdate.petSpecies = pet.speciesId?.speciesName || 'Unknown';
        }
        
        if (updatedFields.breedId) {
            appointmentUpdate.petBreed = pet.breedId?.breedName || 'Unknown';
        }
        
        if (updatedFields.gender) {
            appointmentUpdate.petGender = pet.gender;
        }
        
        if (updatedFields.weight) {
            appointmentUpdate.petWeight = pet.weight;
        }
        
        if (updatedFields.dateOfBirth) {
            // Recalculate age in months
            const ageInMonths = Math.floor((new Date() - new Date(pet.dateOfBirth)) / (1000 * 60 * 60 * 24 * 30.44));
            appointmentUpdate.petAge = ageInMonths;
        }

        // Update all appointments for this pet
        if (Object.keys(appointmentUpdate).length > 0) {
            const result = await Appointment.updateMany(
                { petId: petId },
                { $set: appointmentUpdate }
            );
            console.log(`Synced pet data to ${result.modifiedCount} appointments for pet ${petId}`);
        }

    } catch (error) {
        console.error('Error syncing pet data to appointments:', error);
    }
};

/**
 * Sync client data changes to all related appointments
 * @param {Number} clientId - The client ID that was updated
 * @param {Object} updatedFields - The fields that were updated
 */
export const syncClientDataToAppointments = async (clientId, updatedFields) => {
    try {
        const Appointment = mongoose.model('Appointment');
        const Client = mongoose.model('Client');

        // Get the updated client
        const client = await Client.findOne({ clientId });

        if (!client) {
            console.warn(`Client with ID ${clientId} not found for sync`);
            return;
        }

        // Prepare update object for appointments
        const appointmentUpdate = {};
        
        if (updatedFields.firstName || updatedFields.lastName) {
            appointmentUpdate.clientName = `${client.firstName} ${client.lastName}`.trim();
        }
        
        if (updatedFields.phoneNumber) {
            appointmentUpdate.clientPhone = client.phoneNumber;
        }
        
        if (updatedFields.email) {
            appointmentUpdate.clientEmail = client.email;
        }

        // Update all appointments for this client
        if (Object.keys(appointmentUpdate).length > 0) {
            const result = await Appointment.updateMany(
                { clientId: clientId },
                { $set: appointmentUpdate }
            );
            console.log(`Synced client data to ${result.modifiedCount} appointments for client ${clientId}`);
        }

    } catch (error) {
        console.error('Error syncing client data to appointments:', error);
    }
};

/**
 * Sync staff data changes to all related appointments
 * @param {Number} staffId - The staff ID that was updated
 * @param {Object} updatedFields - The fields that were updated
 */
export const syncStaffDataToAppointments = async (staffId, updatedFields) => {
    try {
        const Appointment = mongoose.model('Appointment');
        const Staff = mongoose.model('Staff');

        // Get the updated staff
        const staff = await Staff.findOne({ staffId });

        if (!staff) {
            console.warn(`Staff with ID ${staffId} not found for sync`);
            return;
        }

        // Prepare update object for appointments
        const appointmentUpdate = {};
        
        if (updatedFields.firstName || updatedFields.lastName) {
            appointmentUpdate.staffInChargeName = `${staff.firstName} ${staff.lastName}`.trim();
        }
        
        if (updatedFields.jobTitle) {
            appointmentUpdate.staffInChargeJobTitle = staff.jobTitle;
        }

        // Update all appointments where this staff is in charge
        if (Object.keys(appointmentUpdate).length > 0) {
            const result = await Appointment.updateMany(
                { staffInCharge: staffId },
                { $set: appointmentUpdate }
            );
            console.log(`Synced staff data to ${result.modifiedCount} appointments for staff ${staffId}`);
        }

        // Also update appointments where this staff performed services
        if (updatedFields.firstName || updatedFields.lastName) {
            const staffName = `${staff.firstName} ${staff.lastName}`.trim();
            const appointmentsWithStaffServices = await Appointment.find({
                'appointmentCategories.categoryServices.performedBy': staffId
            });

            for (const appointment of appointmentsWithStaffServices) {
                let updated = false;
                appointment.appointmentCategories.forEach(category => {
                    category.categoryServices.forEach(service => {
                        if (service.performedBy === staffId) {
                            service.performedByName = staffName;
                            updated = true;
                        }
                    });
                });
                
                if (updated) {
                    await appointment.save();
                }
            }
        }

    } catch (error) {
        console.error('Error syncing staff data to appointments:', error);
    }
};

/**
 * Sync clinic data changes to all related appointments
 * @param {Number} clinicId - The clinic ID that was updated
 * @param {Object} updatedFields - The fields that were updated
 */
export const syncClinicDataToAppointments = async (clinicId, updatedFields) => {
    try {
        const Appointment = mongoose.model('Appointment');
        const Clinic = mongoose.model('Clinic');

        // Get the updated clinic
        const clinic = await Clinic.findOne({ clinicId });

        if (!clinic) {
            console.warn(`Clinic with ID ${clinicId} not found for sync`);
            return;
        }

        // Prepare update object for appointments
        const appointmentUpdate = {};
        
        if (updatedFields.clinicName) {
            appointmentUpdate.clinicName = clinic.clinicName;
        }

        // Update all appointments for this clinic
        if (Object.keys(appointmentUpdate).length > 0) {
            const result = await Appointment.updateMany(
                { clinicId: clinicId },
                { $set: appointmentUpdate }
            );
            console.log(`Synced clinic data to ${result.modifiedCount} appointments for clinic ${clinicId}`);
        }

    } catch (error) {
        console.error('Error syncing clinic data to appointments:', error);
    }
};

/**
 * Sync species data changes to all related appointments
 * @param {Number} speciesId - The species ID that was updated
 * @param {Object} updatedFields - The fields that were updated
 */
export const syncSpeciesDataToAppointments = async (speciesId, updatedFields) => {
    try {
        const Appointment = mongoose.model('Appointment');
        const Pet = mongoose.model('Pet');
        const Species = mongoose.model('Species');

        if (!updatedFields.speciesName) return;

        // Get the updated species
        const species = await Species.findOne({ speciesId });
        if (!species) return;

        // Find all pets with this species
        const pets = await Pet.find({ speciesId }, 'petId');
        const petIds = pets.map(pet => pet.petId);

        if (petIds.length > 0) {
            const result = await Appointment.updateMany(
                { petId: { $in: petIds } },
                { $set: { petSpecies: species.speciesName } }
            );
            console.log(`Synced species data to ${result.modifiedCount} appointments for species ${speciesId}`);
        }

    } catch (error) {
        console.error('Error syncing species data to appointments:', error);
    }
};

/**
 * Sync breed data changes to all related appointments
 * @param {Number} breedId - The breed ID that was updated
 * @param {Object} updatedFields - The fields that were updated
 */
export const syncBreedDataToAppointments = async (breedId, updatedFields) => {
    try {
        const Appointment = mongoose.model('Appointment');
        const Pet = mongoose.model('Pet');
        const Breed = mongoose.model('Breed');

        if (!updatedFields.breedName) return;

        // Get the updated breed
        const breed = await Breed.findOne({ breedId });
        if (!breed) return;

        // Find all pets with this breed
        const pets = await Pet.find({ breedId }, 'petId');
        const petIds = pets.map(pet => pet.petId);

        if (petIds.length > 0) {
            const result = await Appointment.updateMany(
                { petId: { $in: petIds } },
                { $set: { petBreed: breed.breedName } }
            );
            console.log(`Synced breed data to ${result.modifiedCount} appointments for breed ${breedId}`);
        }

    } catch (error) {
        console.error('Error syncing breed data to appointments:', error);
    }
};

export default {
    syncPetDataToAppointments,
    syncClientDataToAppointments,
    syncStaffDataToAppointments,
    syncClinicDataToAppointments,
    syncSpeciesDataToAppointments,
    syncBreedDataToAppointments
};
