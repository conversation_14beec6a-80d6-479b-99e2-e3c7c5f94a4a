/**
 * Counter Management System
 * 
 * This utility provides enhanced counter management for auto-incrementing IDs
 * with collision handling and clinic-specific sequences for multi-clinic environments.
 */

import mongoose from 'mongoose';

/**
 * Counter Schema for managing sequences
 */
const counterSchema = new mongoose.Schema({
    _id: { type: String, required: true },
    sequence_value: { type: Number, default: 1000 },
    clinic_id: { type: Number, default: null }, // For clinic-specific sequences
    last_updated: { type: Date, default: Date.now },
    metadata: {
        entity_type: String,
        description: String,
        start_sequence: { type: Number, default: 1000 },
        max_sequence: { type: Number, default: 999999999 }
    }
});

const Counter = mongoose.model('Counter', counterSchema);

/**
 * Get next sequence number for an entity
 * @param {string} entityName - Name of the entity (e.g., 'patient', 'appointment')
 * @param {number} clinicId - Optional clinic ID for clinic-specific sequences
 * @param {number} startSeq - Starting sequence number (default: 1000)
 * @returns {Promise<number>} Next sequence number
 */
export const getNextSequence = async (entityName, clinicId = null, startSeq = 1000) => {
    try {
        const counterId = clinicId ? `${entityName}_${clinicId}` : entityName;
        
        const counter = await Counter.findByIdAndUpdate(
            counterId,
            {
                $inc: { sequence_value: 1 },
                $set: { 
                    last_updated: new Date(),
                    clinic_id: clinicId,
                    'metadata.entity_type': entityName,
                    'metadata.start_sequence': startSeq
                }
            },
            {
                new: true,
                upsert: true,
                setDefaultsOnInsert: true
            }
        );

        // Ensure sequence starts at the specified start sequence
        if (counter.sequence_value < startSeq) {
            counter.sequence_value = startSeq;
            await counter.save();
        }

        return counter.sequence_value;
    } catch (error) {
        console.error('Error getting next sequence:', error);
        throw new Error(`Failed to generate sequence for ${entityName}: ${error.message}`);
    }
};

/**
 * Reset sequence for an entity
 * @param {string} entityName - Name of the entity
 * @param {number} clinicId - Optional clinic ID
 * @param {number} newValue - New sequence value
 * @returns {Promise<boolean>} Success status
 */
export const resetSequence = async (entityName, clinicId = null, newValue = 1000) => {
    try {
        const counterId = clinicId ? `${entityName}_${clinicId}` : entityName;
        
        await Counter.findByIdAndUpdate(
            counterId,
            {
                $set: {
                    sequence_value: newValue,
                    last_updated: new Date()
                }
            },
            { upsert: true }
        );

        return true;
    } catch (error) {
        console.error('Error resetting sequence:', error);
        return false;
    }
};

/**
 * Get current sequence value without incrementing
 * @param {string} entityName - Name of the entity
 * @param {number} clinicId - Optional clinic ID
 * @returns {Promise<number>} Current sequence value
 */
export const getCurrentSequence = async (entityName, clinicId = null) => {
    try {
        const counterId = clinicId ? `${entityName}_${clinicId}` : entityName;
        const counter = await Counter.findById(counterId);
        return counter ? counter.sequence_value : 1000;
    } catch (error) {
        console.error('Error getting current sequence:', error);
        return 1000;
    }
};

/**
 * Handle sequence collision by finding the next available ID
 * @param {string} entityName - Name of the entity
 * @param {mongoose.Model} Model - Mongoose model to check against
 * @param {string} idField - Field name for the ID (e.g., 'petId', 'appointmentId')
 * @param {number} clinicId - Optional clinic ID
 * @returns {Promise<number>} Next available ID
 */
export const handleSequenceCollision = async (entityName, Model, idField, clinicId = null) => {
    try {
        let attempts = 0;
        const maxAttempts = 10;

        while (attempts < maxAttempts) {
            const nextId = await getNextSequence(entityName, clinicId);
            
            // Check if this ID already exists
            const existing = await Model.findOne({ [idField]: nextId });
            
            if (!existing) {
                return nextId;
            }

            attempts++;
            console.warn(`ID collision detected for ${entityName} ID ${nextId}, attempt ${attempts}`);
        }

        throw new Error(`Failed to generate unique ID for ${entityName} after ${maxAttempts} attempts`);
    } catch (error) {
        console.error('Error handling sequence collision:', error);
        throw error;
    }
};

/**
 * Backup all counters
 * @returns {Promise<Array>} Array of counter documents
 */
export const backupCounters = async () => {
    try {
        const counters = await Counter.find({}).lean();
        return counters;
    } catch (error) {
        console.error('Error backing up counters:', error);
        throw error;
    }
};

/**
 * Restore counters from backup
 * @param {Array} backupData - Array of counter documents
 * @returns {Promise<boolean>} Success status
 */
export const restoreCounters = async (backupData) => {
    try {
        await Counter.deleteMany({});
        await Counter.insertMany(backupData);
        return true;
    } catch (error) {
        console.error('Error restoring counters:', error);
        return false;
    }
};

/**
 * Get statistics about all counters
 * @returns {Promise<Object>} Counter statistics
 */
export const getCounterStats = async () => {
    try {
        const counters = await Counter.find({}).lean();
        
        const stats = {
            total_counters: counters.length,
            global_counters: counters.filter(c => !c.clinic_id).length,
            clinic_specific_counters: counters.filter(c => c.clinic_id).length,
            entities: {},
            clinics: new Set()
        };

        counters.forEach(counter => {
            const entityType = counter.metadata?.entity_type || 'unknown';
            
            if (!stats.entities[entityType]) {
                stats.entities[entityType] = {
                    global: 0,
                    clinic_specific: 0,
                    total_sequence: 0
                };
            }

            if (counter.clinic_id) {
                stats.entities[entityType].clinic_specific++;
                stats.clinics.add(counter.clinic_id);
            } else {
                stats.entities[entityType].global++;
            }

            stats.entities[entityType].total_sequence += counter.sequence_value;
        });

        stats.unique_clinics = stats.clinics.size;
        delete stats.clinics; // Remove Set object for JSON serialization

        return stats;
    } catch (error) {
        console.error('Error getting counter stats:', error);
        throw error;
    }
};

/**
 * Initialize default counters for all entities
 * @returns {Promise<boolean>} Success status
 */
export const initializeDefaultCounters = async () => {
    try {
        const defaultCounters = [
            { _id: 'user', metadata: { entity_type: 'user', description: 'System users', start_sequence: 1001 } },
            { _id: 'staff', metadata: { entity_type: 'staff', description: 'Staff members', start_sequence: 1001 } },
            { _id: 'clinic', metadata: { entity_type: 'clinic', description: 'Veterinary clinics', start_sequence: 1001 } },
            { _id: 'client', metadata: { entity_type: 'client', description: 'Pet owners', start_sequence: 1001 } },
            { _id: 'pet', metadata: { entity_type: 'pet', description: 'Pets/Patients', start_sequence: 1001 } },
            { _id: 'appointment', metadata: { entity_type: 'appointment', description: 'Appointments', start_sequence: 3001 } },
            { _id: 'healthRecord', metadata: { entity_type: 'healthRecord', description: 'Medical records', start_sequence: 1001 } },
            { _id: 'invoice', metadata: { entity_type: 'invoice', description: 'Invoices', start_sequence: 1001 } },
            { _id: 'inventory', metadata: { entity_type: 'inventory', description: 'Inventory items', start_sequence: 1001 } },
            { _id: 'species', metadata: { entity_type: 'species', description: 'Animal species', start_sequence: 1001 } },
            { _id: 'breed', metadata: { entity_type: 'breed', description: 'Animal breeds', start_sequence: 1001 } },
            { _id: 'role', metadata: { entity_type: 'role', description: 'User roles', start_sequence: 1001 } },
            { _id: 'permission', metadata: { entity_type: 'permission', description: 'Permissions', start_sequence: 1001 } }
        ];

        for (const counter of defaultCounters) {
            await Counter.findByIdAndUpdate(
                counter._id,
                {
                    $setOnInsert: {
                        sequence_value: counter.metadata.start_sequence,
                        last_updated: new Date(),
                        metadata: counter.metadata
                    }
                },
                { upsert: true }
            );
        }

        console.log('Default counters initialized successfully');
        return true;
    } catch (error) {
        console.error('Error initializing default counters:', error);
        return false;
    }
};

export default {
    getNextSequence,
    resetSequence,
    getCurrentSequence,
    handleSequenceCollision,
    backupCounters,
    restoreCounters,
    getCounterStats,
    initializeDefaultCounters
};
