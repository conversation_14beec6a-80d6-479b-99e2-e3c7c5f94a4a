import mongoose from 'mongoose';

/**
 * Follow-up Appointment Management Utility
 * 
 * This utility provides functions to manage follow-up appointments,
 * including creation, tracking, and reminder management.
 */

/**
 * Create a follow-up appointment from a parent appointment
 * @param {Number} parentAppointmentId - The parent appointment ID
 * @param {Object} followUpData - Follow-up appointment data
 * @param {Number} createdBy - Staff ID creating the follow-up
 * @returns {Object} Created follow-up appointment
 */
export const createFollowUpAppointment = async (parentAppointmentId, followUpData, createdBy) => {
    try {
        const Appointment = mongoose.model('Appointment');
        
        // Get parent appointment
        const parentAppointment = await Appointment.findOne({ appointmentId: parentAppointmentId });
        if (!parentAppointment) {
            throw new Error('Parent appointment not found');
        }

        // Prepare follow-up appointment data
        const followUpAppointment = {
            // Copy core data from parent
            petId: parentAppointment.petId,
            petName: parentAppointment.petName,
            petSpecies: parentAppointment.petSpecies,
            petBreed: parentAppointment.petBreed,
            petGender: parentAppointment.petGender,
            petAge: parentAppointment.petAge,
            petWeight: parentAppointment.petWeight,
            
            clientId: parentAppointment.clientId,
            clientName: parentAppointment.clientName,
            clientPhone: parentAppointment.clientPhone,
            clientEmail: parentAppointment.clientEmail,
            
            clinicId: parentAppointment.clinicId,
            clinicName: parentAppointment.clinicName,
            
            // Follow-up specific data
            appointmentDate: followUpData.appointmentDate,
            staffInCharge: followUpData.staffInCharge || parentAppointment.staffInCharge,
            staffInChargeName: followUpData.staffInChargeName || parentAppointment.staffInChargeName,
            staffInChargeJobTitle: followUpData.staffInChargeJobTitle || parentAppointment.staffInChargeJobTitle,
            
            priority: followUpData.priority || 'normal',
            status: 'scheduled',
            
            // Follow-up tracking
            followUp: {
                isFollowUp: true,
                parentAppointmentId: parentAppointmentId,
                parentAppointmentDate: parentAppointment.appointmentDate,
                followUpReason: followUpData.reason || 'Routine follow-up',
                followUpType: followUpData.type || 'routine_checkup'
            },
            
            generalNotes: followUpData.notes || `Follow-up appointment for ${parentAppointment.appointmentDate.toDateString()}`,
            createdBy: createdBy
        };

        // Create the follow-up appointment
        const newFollowUp = await Appointment.create(followUpAppointment);

        // Update parent appointment with follow-up information
        await Appointment.findOneAndUpdate(
            { appointmentId: parentAppointmentId },
            {
                $set: {
                    'followUp.hasScheduledFollowUps': true,
                    'followUp.nextFollowUpDate': followUpData.appointmentDate
                },
                $push: {
                    'followUp.childAppointments': {
                        appointmentId: newFollowUp.appointmentId,
                        scheduledDate: followUpData.appointmentDate,
                        status: 'scheduled',
                        followUpType: followUpData.type || 'routine_checkup'
                    }
                }
            }
        );

        return newFollowUp;

    } catch (error) {
        console.error('Error creating follow-up appointment:', error);
        throw error;
    }
};

/**
 * Schedule a follow-up reminder without creating the appointment
 * @param {Number} appointmentId - The appointment ID to add follow-up to
 * @param {Object} followUpData - Follow-up scheduling data
 * @returns {Object} Updated appointment
 */
export const scheduleFollowUpReminder = async (appointmentId, followUpData) => {
    try {
        const Appointment = mongoose.model('Appointment');
        
        const updatedAppointment = await Appointment.findOneAndUpdate(
            { appointmentId: appointmentId },
            {
                $set: {
                    'followUp.nextFollowUpDate': followUpData.followUpDate,
                    'followUp.followUpInstructions': followUpData.instructions || '',
                    'followUp.followUpPriority': followUpData.priority || 'normal',
                    'followUp.hasScheduledFollowUps': true
                }
            },
            { new: true }
        );

        if (!updatedAppointment) {
            throw new Error('Appointment not found');
        }

        return updatedAppointment;

    } catch (error) {
        console.error('Error scheduling follow-up reminder:', error);
        throw error;
    }
};

/**
 * Get all follow-up appointments for a parent appointment
 * @param {Number} parentAppointmentId - The parent appointment ID
 * @returns {Array} Array of follow-up appointments
 */
export const getFollowUpAppointments = async (parentAppointmentId) => {
    try {
        const Appointment = mongoose.model('Appointment');
        
        const followUps = await Appointment.find({
            'followUp.parentAppointmentId': parentAppointmentId
        }).sort({ appointmentDate: 1 });

        return followUps;

    } catch (error) {
        console.error('Error getting follow-up appointments:', error);
        throw error;
    }
};

/**
 * Get appointment chain (parent + all follow-ups)
 * @param {Number} appointmentId - Any appointment ID in the chain
 * @returns {Object} Object with parent and follow-ups
 */
export const getAppointmentChain = async (appointmentId) => {
    try {
        const Appointment = mongoose.model('Appointment');
        
        // Get the appointment
        const appointment = await Appointment.findOne({ appointmentId });
        if (!appointment) {
            throw new Error('Appointment not found');
        }

        let parentAppointment;
        let followUps = [];

        if (appointment.followUp?.isFollowUp) {
            // This is a follow-up, get the parent
            parentAppointment = await Appointment.findOne({
                appointmentId: appointment.followUp.parentAppointmentId
            });
            
            // Get all follow-ups for the parent
            followUps = await Appointment.find({
                'followUp.parentAppointmentId': appointment.followUp.parentAppointmentId
            }).sort({ appointmentDate: 1 });
        } else {
            // This is a parent, get its follow-ups
            parentAppointment = appointment;
            followUps = await Appointment.find({
                'followUp.parentAppointmentId': appointmentId
            }).sort({ appointmentDate: 1 });
        }

        return {
            parent: parentAppointment,
            followUps: followUps,
            totalAppointments: 1 + followUps.length
        };

    } catch (error) {
        console.error('Error getting appointment chain:', error);
        throw error;
    }
};

/**
 * Get overdue follow-ups for a clinic
 * @param {Number} clinicId - The clinic ID
 * @param {Number} daysOverdue - Number of days overdue (default: 0)
 * @returns {Array} Array of overdue follow-up appointments
 */
export const getOverdueFollowUps = async (clinicId, daysOverdue = 0) => {
    try {
        const Appointment = mongoose.model('Appointment');
        
        const overdueDate = new Date();
        overdueDate.setDate(overdueDate.getDate() - daysOverdue);

        const overdueAppointments = await Appointment.find({
            clinicId: clinicId,
            'followUp.hasScheduledFollowUps': true,
            'followUp.nextFollowUpDate': { $lt: overdueDate },
            status: { $ne: 'completed' }
        }).sort({ 'followUp.nextFollowUpDate': 1 });

        return overdueAppointments;

    } catch (error) {
        console.error('Error getting overdue follow-ups:', error);
        throw error;
    }
};

/**
 * Get upcoming follow-ups for a clinic
 * @param {Number} clinicId - The clinic ID
 * @param {Number} daysAhead - Number of days to look ahead (default: 7)
 * @returns {Array} Array of upcoming follow-up appointments
 */
export const getUpcomingFollowUps = async (clinicId, daysAhead = 7) => {
    try {
        const Appointment = mongoose.model('Appointment');
        
        const today = new Date();
        const futureDate = new Date();
        futureDate.setDate(futureDate.getDate() + daysAhead);

        const upcomingAppointments = await Appointment.find({
            clinicId: clinicId,
            'followUp.hasScheduledFollowUps': true,
            'followUp.nextFollowUpDate': { 
                $gte: today,
                $lte: futureDate 
            }
        }).sort({ 'followUp.nextFollowUpDate': 1 });

        return upcomingAppointments;

    } catch (error) {
        console.error('Error getting upcoming follow-ups:', error);
        throw error;
    }
};

/**
 * Update follow-up status when child appointment is completed
 * @param {Number} followUpAppointmentId - The follow-up appointment ID
 * @returns {Object} Updated parent appointment
 */
export const markFollowUpCompleted = async (followUpAppointmentId) => {
    try {
        const Appointment = mongoose.model('Appointment');
        
        // Get the follow-up appointment
        const followUpAppointment = await Appointment.findOne({ 
            appointmentId: followUpAppointmentId 
        });
        
        if (!followUpAppointment?.followUp?.isFollowUp) {
            throw new Error('Not a follow-up appointment');
        }

        // Update parent appointment's child appointments array
        const parentAppointment = await Appointment.findOneAndUpdate(
            { 
                appointmentId: followUpAppointment.followUp.parentAppointmentId,
                'followUp.childAppointments.appointmentId': followUpAppointmentId
            },
            {
                $set: {
                    'followUp.childAppointments.$.status': 'completed'
                }
            },
            { new: true }
        );

        return parentAppointment;

    } catch (error) {
        console.error('Error marking follow-up completed:', error);
        throw error;
    }
};

export default {
    createFollowUpAppointment,
    scheduleFollowUpReminder,
    getFollowUpAppointments,
    getAppointmentChain,
    getOverdueFollowUps,
    getUpcomingFollowUps,
    markFollowUpCompleted
};
