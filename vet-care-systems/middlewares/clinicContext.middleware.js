/**
 * Middleware to automatically inject clinic context into requests
 * This ensures all data operations are properly scoped to the current clinic
 */

import { sendResponse } from '../utils/responseHandler.js';
import Staff from '../models/staff.model.js';
import Clinic from '../models/clinic.model.js';

/**
 * Middleware that automatically adds clinicId to request body and query parameters
 * This ensures all data operations are clinic-scoped by default
 */
export const injectClinicContext = (options = {}) => {
    const {
        required = true,
        bodyField = 'clinicId',
        queryField = 'clinicId',
        skipRoutes = []
    } = options;

    return (req, res, next) => {
        // Skip for certain routes if specified
        if (skipRoutes.some(route => req.path.includes(route))) {
            return next();
        }

        // Get clinic ID from various sources
        let clinicId = null;

        // 1. From request body (highest priority)
        if (req.body && req.body[bodyField]) {
            clinicId = parseInt(req.body[bodyField]);
        }
        // 2. From query parameters
        else if (req.query && req.query[queryField]) {
            clinicId = parseInt(req.query[queryField]);
        }
        // 3. From user context (staff member's current clinic)
        else if (req.user && req.user.currentClinicId) {
            clinicId = parseInt(req.user.currentClinicId);
        }
        // 4. From user context (staff member's primary clinic)
        else if (req.user && req.user.primaryClinicId) {
            clinicId = parseInt(req.user.primaryClinicId);
        }
        // 5. From user context (staff member's clinic)
        else if (req.user && req.user.clinicId) {
            clinicId = parseInt(req.user.clinicId);
        }

        // If clinic ID is required but not found, return error
        if (required && !clinicId) {
            return sendResponse(res, 400, false, "Clinic context is required for this operation");
        }

        // Inject clinic ID into request body for POST/PUT operations
        if (clinicId && req.body && (req.method === 'POST' || req.method === 'PUT')) {
            req.body[bodyField] = clinicId;
        }

        // Inject clinic ID into query parameters for GET operations
        if (clinicId && (req.method === 'GET' || req.method === 'DELETE')) {
            req.query[queryField] = clinicId.toString();
        }

        // Store clinic ID in request for easy access
        req.clinicId = clinicId;

        next();
    };
};

/**
 * Middleware specifically for data creation endpoints
 * Ensures all created data is properly linked to a clinic
 */
export const requireClinicContext = injectClinicContext({
    required: true,
    skipRoutes: ['/auth/', '/admin/', '/system/']
});

/**
 * Middleware for optional clinic context
 * Adds clinic ID if available but doesn't require it
 */
export const optionalClinicContext = injectClinicContext({
    required: false
});

/**
 * Helper function to validate staff clinic access
 * Checks if staff member has access to the specified clinic
 */
async function validateStaffClinicAccess(staffId, clinicId) {
    try {
        // Get staff record with clinic information
        const staff = await Staff.findOne({ staffId })
            .select('clinicId primaryClinicId currentClinicId additionalClinics isClinicOwner')
            .lean();

        if (!staff) {
            console.log(`❌ Staff ${staffId} not found`);
            return false;
        }

        // Check if staff has access to this clinic
        const hasAccess =
            staff.clinicId === clinicId ||
            staff.primaryClinicId === clinicId ||
            staff.currentClinicId === clinicId ||
            (staff.additionalClinics && staff.additionalClinics.includes(clinicId));

        if (hasAccess) {
            console.log(`✅ Staff ${staffId} has access to clinic ${clinicId}`);
            return true;
        }

        // If staff is a clinic owner, check if they own the requested clinic
        if (staff.isClinicOwner) {
            const clinic = await Clinic.findOne({ clinicId, owner: staffId }).lean();
            if (clinic) {
                console.log(`✅ Staff ${staffId} owns clinic ${clinicId}`);
                return true;
            }
        }

        console.log(`❌ Staff ${staffId} does not have access to clinic ${clinicId}`);
        return false;
    } catch (error) {
        console.error('Error validating staff clinic access:', error);
        return false;
    }
}

/**
 * Middleware to validate clinic access
 * Ensures the user has access to the specified clinic
 */
export const validateClinicAccess = async (req, res, next) => {
    try {
        const clinicId = req.clinicId || req.body.clinicId || req.query.clinicId;

        if (!clinicId) {
            return next(); // Skip validation if no clinic ID
        }

        const user = req.user;
        if (!user) {
            return sendResponse(res, 401, false, "Authentication required");
        }

        // Admin users have access to all clinics
        if (user.email === '<EMAIL>' || user.roleId === 1001 || user.roleName === 'super_admin') {
            console.log(`🔑 Admin clinic access granted for ${user.email || user.userId}`);
            return next();
        }

        // For staff users, validate clinic access more thoroughly
        if (user.userType === 'staff' && user.staffId) {
            const hasAccess = await validateStaffClinicAccess(user.staffId, parseInt(clinicId));
            if (!hasAccess) {
                console.log(`❌ Staff ${user.staffId} denied access to clinic ${clinicId}`);
                return sendResponse(res, 403, false, "Access denied to this clinic");
            }
            console.log(`✅ Staff ${user.staffId} granted access to clinic ${clinicId}`);
            return next();
        }

        // For regular users, check basic clinic access
        const hasAccess =
            user.currentClinicId === parseInt(clinicId) ||
            user.primaryClinicId === parseInt(clinicId) ||
            user.clinicId === parseInt(clinicId) ||
            (user.additionalClinics && user.additionalClinics.includes(parseInt(clinicId)));

        if (!hasAccess) {
            console.log(`❌ User ${user.userId} denied access to clinic ${clinicId}`);
            return sendResponse(res, 403, false, "Access denied to this clinic");
        }

        console.log(`✅ User ${user.userId} granted access to clinic ${clinicId}`);
        next();
    } catch (error) {
        console.error('Clinic access validation error:', error);
        return sendResponse(res, 500, false, "Error validating clinic access");
    }
};

/**
 * Get available clinics for a user
 * Returns list of clinics the user has access to
 */
export const getUserAvailableClinics = async (req, res) => {
    try {
        const user = req.user;
        if (!user) {
            return sendResponse(res, 401, false, "Authentication required");
        }

        let availableClinics = [];

        // Admin users have access to all clinics
        if (user.email === '<EMAIL>' || user.roleId === 1001 || user.roleName === 'super_admin') {
            availableClinics = await Clinic.find({ status: 1 })
                .select('clinicId clinicName address phoneNumber email logoUrl')
                .lean();
        } else if (user.userType === 'staff' && user.staffId) {
            // Get staff record with clinic access
            const staff = await Staff.findOne({ staffId: user.staffId })
                .select('clinicId primaryClinicId additionalClinics isClinicOwner')
                .lean();

            if (staff) {
                const clinicIds = new Set();

                // Add primary clinic
                if (staff.clinicId) clinicIds.add(staff.clinicId);
                if (staff.primaryClinicId) clinicIds.add(staff.primaryClinicId);

                // Add additional clinics
                if (staff.additionalClinics) {
                    staff.additionalClinics.forEach(id => clinicIds.add(id));
                }

                // If clinic owner, add owned clinics
                if (staff.isClinicOwner) {
                    const ownedClinics = await Clinic.find({ owner: user.staffId })
                        .select('clinicId')
                        .lean();
                    ownedClinics.forEach(clinic => clinicIds.add(clinic.clinicId));
                }

                // Get clinic details
                if (clinicIds.size > 0) {
                    availableClinics = await Clinic.find({
                        clinicId: { $in: Array.from(clinicIds) },
                        status: 1
                    })
                    .select('clinicId clinicName address phoneNumber email logoUrl')
                    .lean();
                }
            }
        }

        return sendResponse(res, 200, true, "Available clinics retrieved", {
            clinics: availableClinics,
            count: availableClinics.length
        });
    } catch (error) {
        console.error('Error getting available clinics:', error);
        return sendResponse(res, 500, false, "Error retrieving available clinics");
    }
};

/**
 * Combined middleware for secure clinic-scoped operations
 */
export const secureClinicOperation = [
    requireClinicContext,
    validateClinicAccess
];
