# Follow-up Appointment System

## Overview
The follow-up appointment system provides comprehensive tracking and management of appointment relationships, enabling veterinary clinics to maintain continuity of care and ensure proper follow-up scheduling.

## System Architecture

### 1. Bidirectional Linking
- **Parent → Child**: Parent appointments track their follow-ups
- **Child → Parent**: Follow-up appointments reference their parent
- **Chain Navigation**: Easy traversal of appointment relationships

### 2. Data Structure

#### Follow-up Fields in Appointment Model
```javascript
followUp: {
    // For follow-up appointments
    isFollowUp: Boolean,
    parentAppointmentId: Number,
    parentAppointmentDate: Date,
    followUpReason: String,
    followUpType: String, // routine_checkup, medication_review, etc.
    
    // For parent appointments
    hasScheduledFollowUps: Boolean,
    nextFollowUpDate: Date,
    followUpInstructions: String,
    followUpPriority: String,
    
    // Child appointments tracking
    childAppointments: [{
        appointmentId: Number,
        scheduledDate: Date,
        status: String,
        followUpType: String,
        createdAt: Date
    }]
}
```

## API Endpoints

### Create Follow-up Appointment
```
POST /api/appointments/:parentAppointmentId/followup
```

**Request Body:**
```json
{
    "appointmentDate": "2024-02-15T10:00:00Z",
    "staffInCharge": 1001,
    "priority": "normal",
    "reason": "Post-surgery checkup",
    "type": "surgery_followup",
    "notes": "Check surgical site healing"
}
```

### Schedule Follow-up Reminder
```
POST /api/appointments/:appointmentId/followup-reminder
```

**Request Body:**
```json
{
    "followUpDate": "2024-02-15T10:00:00Z",
    "instructions": "Schedule vaccination booster",
    "priority": "high"
}
```

### Get Appointment Chain
```
GET /api/appointments/:appointmentId/chain
```

**Response:**
```json
{
    "success": true,
    "data": {
        "parent": { /* parent appointment */ },
        "followUps": [ /* array of follow-up appointments */ ],
        "totalAppointments": 3
    }
}
```

### Get Overdue Follow-ups
```
GET /api/appointments/overdue-followups?clinicId=1001&daysOverdue=7
```

### Get Upcoming Follow-ups
```
GET /api/appointments/upcoming-followups?clinicId=1001&daysAhead=14
```

## Follow-up Types

### Available Types
1. **routine_checkup** - Regular health checkups
2. **medication_review** - Medication effectiveness review
3. **surgery_followup** - Post-surgical monitoring
4. **vaccination_reminder** - Vaccination scheduling
5. **treatment_progress** - Treatment effectiveness monitoring
6. **emergency_followup** - Emergency case follow-up
7. **other** - Custom follow-up types

## Usage Examples

### 1. Creating a Follow-up Appointment
```javascript
// After completing a surgery appointment
const followUpData = {
    appointmentDate: new Date('2024-02-15T10:00:00Z'),
    staffInCharge: 1001,
    priority: 'high',
    reason: 'Post-surgery wound check',
    type: 'surgery_followup',
    notes: 'Check incision site for healing and remove stitches if ready'
};

const followUp = await createFollowUpAppointment(3001, followUpData, currentStaffId);
```

### 2. Scheduling a Reminder
```javascript
// Schedule a vaccination reminder
const reminderData = {
    followUpDate: new Date('2024-03-01T09:00:00Z'),
    instructions: 'Annual vaccination due - contact client to schedule',
    priority: 'normal'
};

await scheduleFollowUpReminder(3001, reminderData);
```

### 3. Getting Appointment History
```javascript
// Get complete appointment chain
const chain = await getAppointmentChain(3002);
console.log(`Total appointments in chain: ${chain.totalAppointments}`);
console.log(`Parent appointment: ${chain.parent.appointmentDate}`);
console.log(`Follow-ups: ${chain.followUps.length}`);
```

## Database Queries

### Find All Follow-ups for a Pet
```javascript
const petFollowUps = await Appointment.find({
    petId: 1001,
    'followUp.isFollowUp': true
}).sort({ appointmentDate: -1 });
```

### Find Appointments Needing Follow-up
```javascript
const needingFollowUp = await Appointment.find({
    clinicId: 1001,
    status: 'completed',
    'followUp.hasScheduledFollowUps': false,
    appointmentDate: { $gte: thirtyDaysAgo }
});
```

### Find Overdue Follow-ups
```javascript
const overdue = await Appointment.find({
    'followUp.hasScheduledFollowUps': true,
    'followUp.nextFollowUpDate': { $lt: new Date() },
    status: { $ne: 'completed' }
});
```

## Performance Optimizations

### Indexes
- `followUp.isFollowUp + appointmentDate` - Follow-up listings
- `followUp.parentAppointmentId` - Parent-child relationships
- `followUp.nextFollowUpDate` - Follow-up reminders
- `petId + followUp.isFollowUp + appointmentDate` - Pet history

### Virtual Fields
- `activeFollowUpCount` - Count of scheduled follow-ups
- `isFollowUpOverdue` - Boolean for overdue status
- `parentAppointment` - Virtual populate parent
- `childAppointments` - Virtual populate children

## Best Practices

### 1. Follow-up Creation
- Always copy essential data from parent appointment
- Set appropriate follow-up type for categorization
- Include clear reason and instructions
- Update parent appointment with follow-up information

### 2. Data Consistency
- Use transactions for follow-up creation
- Update both parent and child appointments
- Maintain bidirectional references
- Handle cancellations properly

### 3. User Experience
- Show appointment chains in UI
- Highlight overdue follow-ups
- Provide quick follow-up scheduling
- Display follow-up history

## Monitoring and Analytics

### Key Metrics
1. **Follow-up Compliance Rate** - % of scheduled follow-ups completed
2. **Average Follow-up Time** - Time between appointments
3. **Follow-up Type Distribution** - Most common follow-up reasons
4. **Overdue Follow-ups** - Count and aging of overdue appointments

### Dashboard Queries
```javascript
// Follow-up compliance rate
const compliance = await Appointment.aggregate([
    { $match: { 'followUp.hasScheduledFollowUps': true } },
    { $group: {
        _id: null,
        total: { $sum: 1 },
        completed: { $sum: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] } }
    }},
    { $project: { complianceRate: { $divide: ['$completed', '$total'] } } }
]);
```

## Integration Points

### 1. Health Records
- Automatically create health records for follow-ups
- Link follow-up appointments to medical history
- Track treatment progress across appointments

### 2. Billing System
- Handle follow-up appointment billing
- Apply follow-up discounts if applicable
- Track revenue from follow-up care

### 3. Notification System
- Send follow-up reminders to clients
- Alert staff about overdue follow-ups
- Automated follow-up scheduling suggestions

## Future Enhancements

### Planned Features
1. **Automated Follow-up Suggestions** - AI-powered follow-up recommendations
2. **Client Self-Scheduling** - Allow clients to schedule their own follow-ups
3. **Follow-up Templates** - Pre-defined follow-up schedules by condition
4. **Integration with Calendar** - Sync with external calendar systems

---

**Last Updated**: [Current Date]
**Version**: 1.0
