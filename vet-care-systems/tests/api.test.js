import { describe, it, expect, beforeAll, afterAll, beforeEach } from '@jest/globals';
import request from 'supertest';
import mongoose from 'mongoose';
import app from '../app.js';

// Test database URI
const TEST_DB_URI = process.env.TEST_DB_URI || 'mongodb://localhost:27017/vet-care-test';

describe('Veterinary SaaS API Tests', () => {
    let authToken;
    let clinicId;
    let staffId;
    let clientId;
    let petId;
    let appointmentId;

    beforeAll(async () => {
        // Connect to test database
        await mongoose.connect(TEST_DB_URI);
        
        // Clear test database
        await mongoose.connection.db.dropDatabase();
    });

    afterAll(async () => {
        // Clean up and close connection
        await mongoose.connection.close();
    });

    beforeEach(async () => {
        // Reset database state before each test
        const collections = await mongoose.connection.db.collections();
        for (let collection of collections) {
            await collection.deleteMany({});
        }
    });

    describe('Authentication & Authorization', () => {
        it('should register a new clinic owner', async () => {
            const response = await request(app)
                .post('/api/auth/sign-up')
                .send({
                    firstName: 'John',
                    lastName: 'Doe',
                    email: '<EMAIL>',
                    password: 'password123',
                    phoneNumber: '+1234567890',
                    jobTitle: 'Veterinarian',
                    specializations: ['General Practice'],
                    clinicName: 'Test Veterinary Clinic',
                    clinicAddress: '123 Test Street',
                    clinicPhone: '+1234567890',
                    clinicEmail: '<EMAIL>'
                });

            expect(response.status).toBe(201);
            expect(response.body.success).toBe(true);
            expect(response.body.data.token).toBeDefined();
            
            authToken = response.body.data.token;
            staffId = response.body.data.staff.staffId;
            clinicId = response.body.data.clinic.clinicId;
        });

        it('should login with valid credentials', async () => {
            // First register a user
            await request(app)
                .post('/api/auth/sign-up')
                .send({
                    firstName: 'Jane',
                    lastName: 'Smith',
                    email: '<EMAIL>',
                    password: 'password123',
                    phoneNumber: '+1234567891',
                    jobTitle: 'Veterinarian',
                    specializations: ['Surgery'],
                    clinicName: 'Jane\'s Clinic',
                    clinicAddress: '456 Test Avenue',
                    clinicPhone: '+1234567891',
                    clinicEmail: '<EMAIL>'
                });

            const response = await request(app)
                .post('/api/auth/login')
                .send({
                    email: '<EMAIL>',
                    password: 'password123'
                });

            expect(response.status).toBe(200);
            expect(response.body.success).toBe(true);
            expect(response.body.data.token).toBeDefined();
        });

        it('should reject login with invalid credentials', async () => {
            const response = await request(app)
                .post('/api/auth/login')
                .send({
                    email: '<EMAIL>',
                    password: 'wrongpassword'
                });

            expect(response.status).toBe(401);
            expect(response.body.success).toBe(false);
        });
    });

    describe('Clinic Management', () => {
        beforeEach(async () => {
            // Setup authenticated user
            const authResponse = await request(app)
                .post('/api/auth/sign-up')
                .send({
                    firstName: 'Test',
                    lastName: 'Owner',
                    email: '<EMAIL>',
                    password: 'password123',
                    phoneNumber: '+1234567892',
                    jobTitle: 'Veterinarian',
                    specializations: ['General Practice'],
                    clinicName: 'Test Clinic',
                    clinicAddress: '789 Test Road',
                    clinicPhone: '+1234567892',
                    clinicEmail: '<EMAIL>'
                });
            
            authToken = authResponse.body.data.token;
            clinicId = authResponse.body.data.clinic.clinicId;
            staffId = authResponse.body.data.staff.staffId;
        });

        it('should get clinic information', async () => {
            const response = await request(app)
                .get(`/api/clinics/${clinicId}`)
                .set('Authorization', `Bearer ${authToken}`);

            expect(response.status).toBe(200);
            expect(response.body.success).toBe(true);
            expect(response.body.data.clinic.clinicName).toBe('Test Clinic');
        });

        it('should get clinic staff', async () => {
            const response = await request(app)
                .get(`/api/clinics/${clinicId}/staff`)
                .set('Authorization', `Bearer ${authToken}`);

            expect(response.status).toBe(200);
            expect(response.body.success).toBe(true);
            expect(Array.isArray(response.body.data.data)).toBe(true);
        });

        it('should switch clinic context', async () => {
            const response = await request(app)
                .post('/api/clinics/switch-clinic')
                .set('Authorization', `Bearer ${authToken}`)
                .send({ clinicId });

            expect(response.status).toBe(200);
            expect(response.body.success).toBe(true);
        });
    });

    describe('Client Management', () => {
        beforeEach(async () => {
            // Setup authenticated user and clinic
            const authResponse = await request(app)
                .post('/api/auth/sign-up')
                .send({
                    firstName: 'Test',
                    lastName: 'Vet',
                    email: '<EMAIL>',
                    password: 'password123',
                    phoneNumber: '+1234567893',
                    jobTitle: 'Veterinarian',
                    specializations: ['General Practice'],
                    clinicName: 'Vet Clinic',
                    clinicAddress: '321 Vet Street',
                    clinicPhone: '+1234567893',
                    clinicEmail: '<EMAIL>'
                });
            
            authToken = authResponse.body.data.token;
            clinicId = authResponse.body.data.clinic.clinicId;
        });

        it('should create a new client', async () => {
            const response = await request(app)
                .post('/api/clients')
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                    firstName: 'Pet',
                    lastName: 'Owner',
                    email: '<EMAIL>',
                    phoneNumber: '+1234567894',
                    address: '123 Pet Street',
                    preferredClinicId: clinicId
                });

            expect(response.status).toBe(201);
            expect(response.body.success).toBe(true);
            expect(response.body.data.client.firstName).toBe('Pet');
            
            clientId = response.body.data.client.clientId;
        });

        it('should get all clients', async () => {
            // First create a client
            await request(app)
                .post('/api/clients')
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                    firstName: 'Test',
                    lastName: 'Client',
                    email: '<EMAIL>',
                    phoneNumber: '+1234567895',
                    address: '456 Client Avenue',
                    preferredClinicId: clinicId
                });

            const response = await request(app)
                .get('/api/clients')
                .set('Authorization', `Bearer ${authToken}`);

            expect(response.status).toBe(200);
            expect(response.body.success).toBe(true);
            expect(Array.isArray(response.body.data.clients)).toBe(true);
            expect(response.body.data.clients.length).toBeGreaterThan(0);
        });
    });

    describe('Pet Management', () => {
        beforeEach(async () => {
            // Setup authenticated user, clinic, and client
            const authResponse = await request(app)
                .post('/api/auth/sign-up')
                .send({
                    firstName: 'Pet',
                    lastName: 'Vet',
                    email: '<EMAIL>',
                    password: 'password123',
                    phoneNumber: '+1234567896',
                    jobTitle: 'Veterinarian',
                    specializations: ['Pet Care'],
                    clinicName: 'Pet Care Clinic',
                    clinicAddress: '654 Pet Avenue',
                    clinicPhone: '+1234567896',
                    clinicEmail: '<EMAIL>'
                });
            
            authToken = authResponse.body.data.token;
            clinicId = authResponse.body.data.clinic.clinicId;

            // Create a client
            const clientResponse = await request(app)
                .post('/api/clients')
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                    firstName: 'Pet',
                    lastName: 'Owner',
                    email: '<EMAIL>',
                    phoneNumber: '+1234567897',
                    address: '789 Owner Street',
                    preferredClinicId: clinicId
                });
            
            clientId = clientResponse.body.data.client.clientId;
        });

        it('should create a new pet', async () => {
            const response = await request(app)
                .post('/api/pets')
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                    petName: 'Fluffy',
                    species: 'cat',
                    breed: 'Persian',
                    gender: 'female',
                    age: 3,
                    weight: 4.5,
                    clientId: clientId,
                    clinicId: clinicId
                });

            expect(response.status).toBe(201);
            expect(response.body.success).toBe(true);
            expect(response.body.data.pet.petName).toBe('Fluffy');
            
            petId = response.body.data.pet.petId;
        });

        it('should get all pets', async () => {
            // First create a pet
            await request(app)
                .post('/api/pets')
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                    petName: 'Buddy',
                    species: 'dog',
                    breed: 'Golden Retriever',
                    gender: 'male',
                    age: 2,
                    weight: 25.0,
                    clientId: clientId,
                    clinicId: clinicId
                });

            const response = await request(app)
                .get('/api/pets')
                .set('Authorization', `Bearer ${authToken}`);

            expect(response.status).toBe(200);
            expect(response.body.success).toBe(true);
            expect(Array.isArray(response.body.data.pets)).toBe(true);
        });
    });

    describe('Appointment Management', () => {
        beforeEach(async () => {
            // Setup complete test environment
            const authResponse = await request(app)
                .post('/api/auth/sign-up')
                .send({
                    firstName: 'Appointment',
                    lastName: 'Vet',
                    email: '<EMAIL>',
                    password: 'password123',
                    phoneNumber: '+1234567898',
                    jobTitle: 'Veterinarian',
                    specializations: ['General Practice'],
                    clinicName: 'Appointment Clinic',
                    clinicAddress: '987 Appointment Street',
                    clinicPhone: '+1234567898',
                    clinicEmail: '<EMAIL>'
                });
            
            authToken = authResponse.body.data.token;
            clinicId = authResponse.body.data.clinic.clinicId;

            // Create client and pet
            const clientResponse = await request(app)
                .post('/api/clients')
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                    firstName: 'Appointment',
                    lastName: 'Client',
                    email: '<EMAIL>',
                    phoneNumber: '+1234567899',
                    address: '123 Appointment Avenue',
                    preferredClinicId: clinicId
                });
            
            clientId = clientResponse.body.data.client.clientId;

            const petResponse = await request(app)
                .post('/api/pets')
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                    petName: 'Test Pet',
                    species: 'dog',
                    breed: 'Labrador',
                    gender: 'male',
                    age: 1,
                    weight: 15.0,
                    clientId: clientId,
                    clinicId: clinicId
                });
            
            petId = petResponse.body.data.pet.petId;
        });

        it('should book a new appointment', async () => {
            const appointmentDate = new Date();
            appointmentDate.setDate(appointmentDate.getDate() + 1); // Tomorrow

            const response = await request(app)
                .post('/api/appointments/book')
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                    clientType: 'registered',
                    clientData: {
                        clientId: clientId,
                        firstName: 'Appointment',
                        lastName: 'Client',
                        phoneNumber: '+1234567899',
                        email: '<EMAIL>'
                    },
                    petData: {
                        petId: petId,
                        petName: 'Test Pet',
                        species: 'dog',
                        breed: 'Labrador',
                        gender: 'male',
                        age: 1,
                        weight: 15.0
                    },
                    appointmentData: {
                        appointmentDate: appointmentDate.toISOString().split('T')[0],
                        appointmentTime: '10:00',
                        categories: [],
                        notes: 'Test appointment',
                        priority: 'normal',
                        estimatedDuration: 30
                    }
                });

            expect(response.status).toBe(201);
            expect(response.body.success).toBe(true);
            expect(response.body.data.appointment).toBeDefined();
            
            appointmentId = response.body.data.appointment.appointmentId;
        });

        it('should check for appointment conflicts', async () => {
            const appointmentDate = new Date();
            appointmentDate.setDate(appointmentDate.getDate() + 1);

            const response = await request(app)
                .get('/api/appointments/conflicts')
                .set('Authorization', `Bearer ${authToken}`)
                .query({
                    date: appointmentDate.toISOString().split('T')[0],
                    time: '10:00',
                    duration: 30
                });

            expect(response.status).toBe(200);
            expect(response.body.success).toBe(true);
            expect(response.body.data).toHaveProperty('hasConflicts');
        });

        it('should get all appointments', async () => {
            const response = await request(app)
                .get('/api/appointments')
                .set('Authorization', `Bearer ${authToken}`);

            expect(response.status).toBe(200);
            expect(response.body.success).toBe(true);
            expect(Array.isArray(response.body.data.appointments)).toBe(true);
        });
    });

    describe('AI Integration', () => {
        beforeEach(async () => {
            const authResponse = await request(app)
                .post('/api/auth/sign-up')
                .send({
                    firstName: 'AI',
                    lastName: 'Vet',
                    email: '<EMAIL>',
                    password: 'password123',
                    phoneNumber: '+1234567800',
                    jobTitle: 'Veterinarian',
                    specializations: ['AI Integration'],
                    clinicName: 'AI Clinic',
                    clinicAddress: '111 AI Street',
                    clinicPhone: '+1234567800',
                    clinicEmail: '<EMAIL>'
                });
            
            authToken = authResponse.body.data.token;
            clinicId = authResponse.body.data.clinic.clinicId;
        });

        it('should get AI suggestions', async () => {
            const response = await request(app)
                .get('/api/ai/suggestions')
                .set('Authorization', `Bearer ${authToken}`)
                .query({ clinicId });

            expect(response.status).toBe(200);
            expect(response.body.success).toBe(true);
        });

        it('should get clinic insights', async () => {
            const response = await request(app)
                .get('/api/ai/clinic-insights')
                .set('Authorization', `Bearer ${authToken}`)
                .query({ clinicId });

            expect(response.status).toBe(200);
            expect(response.body.success).toBe(true);
        });

        it('should handle AI chat', async () => {
            const response = await request(app)
                .post('/api/ai/chat')
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                    message: 'What are the symptoms of kennel cough?',
                    clinicId: clinicId,
                    context: 'general'
                });

            // AI might be disabled in test environment
            expect([200, 503]).toContain(response.status);
        });
    });
});
