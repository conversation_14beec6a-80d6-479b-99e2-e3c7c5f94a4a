import nodemailer from 'nodemailer';
import { EMAIL_CONFIG } from '../config/env.js';

class EmailProcessor {
  constructor() {
    this.transporter = null;
    this.isConfigured = false;
    this.initializeTransporter();
  }

  initializeTransporter() {
    try {
      if (!EMAIL_CONFIG.user || !EMAIL_CONFIG.password) {
        console.warn('⚠️  Email configuration incomplete - email sending disabled');
        return;
      }

      this.transporter = nodemailer.createTransporter({
        host: EMAIL_CONFIG.host,
        port: EMAIL_CONFIG.port,
        secure: EMAIL_CONFIG.secure,
        auth: {
          user: EMAIL_CONFIG.user,
          pass: EMAIL_CONFIG.password
        }
      });

      this.isConfigured = true;
      console.log('📧 Email transporter initialized');
    } catch (error) {
      console.error('❌ Email transporter initialization failed:', error);
      this.isConfigured = false;
    }
  }

  async sendEmail(job) {
    const { to, subject, body, template, attachments, priority } = job.data;

    if (!this.isConfigured) {
      console.log(`📧 Email simulation: To: ${to}, Subject: ${subject}`);
      return {
        status: 'simulated',
        messageId: `sim_${Date.now()}`,
        to,
        subject
      };
    }

    try {
      const mailOptions = {
        from: EMAIL_CONFIG.from,
        to,
        subject,
        html: body,
        attachments: attachments || []
      };

      // Add template processing if template is provided
      if (template) {
        mailOptions.html = await this.processTemplate(template, job.data);
      }

      const result = await this.transporter.sendMail(mailOptions);
      
      console.log(`📧 Email sent successfully to ${to}: ${result.messageId}`);
      
      return {
        status: 'sent',
        messageId: result.messageId,
        to,
        subject
      };
    } catch (error) {
      console.error(`❌ Email sending failed to ${to}:`, error);
      throw error;
    }
  }

  async processTemplate(template, data) {
    // Basic template processing - replace placeholders
    let html = template;
    
    Object.keys(data).forEach(key => {
      const placeholder = new RegExp(`{{${key}}}`, 'g');
      html = html.replace(placeholder, data[key] || '');
    });

    return html;
  }

  // Predefined email templates
  getAppointmentReminderTemplate(appointmentData) {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2563eb;">Appointment Reminder</h2>
        <p>Dear {{clientName}},</p>
        <p>This is a reminder for your upcoming appointment:</p>
        <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <p><strong>Pet:</strong> {{petName}}</p>
          <p><strong>Date:</strong> {{appointmentDate}}</p>
          <p><strong>Time:</strong> {{appointmentTime}}</p>
          <p><strong>Clinic:</strong> {{clinicName}}</p>
          <p><strong>Service:</strong> {{serviceName}}</p>
        </div>
        <p>Please arrive 15 minutes early for check-in.</p>
        <p>If you need to reschedule, please contact us at {{clinicPhone}}.</p>
        <p>Best regards,<br>{{clinicName}} Team</p>
      </div>
    `;
  }

  getAppointmentConfirmationTemplate() {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #059669;">Appointment Confirmed</h2>
        <p>Dear {{clientName}},</p>
        <p>Your appointment has been successfully scheduled:</p>
        <div style="background-color: #ecfdf5; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <p><strong>Pet:</strong> {{petName}}</p>
          <p><strong>Date:</strong> {{appointmentDate}}</p>
          <p><strong>Time:</strong> {{appointmentTime}}</p>
          <p><strong>Clinic:</strong> {{clinicName}}</p>
          <p><strong>Service:</strong> {{serviceName}}</p>
          <p><strong>Appointment ID:</strong> {{appointmentId}}</p>
        </div>
        <p>We look forward to seeing you and {{petName}}!</p>
        <p>Best regards,<br>{{clinicName}} Team</p>
      </div>
    `;
  }

  getInvoiceTemplate() {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #dc2626;">Invoice</h2>
        <p>Dear {{clientName}},</p>
        <p>Please find your invoice details below:</p>
        <div style="background-color: #fef2f2; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <p><strong>Invoice ID:</strong> {{invoiceId}}</p>
          <p><strong>Date:</strong> {{invoiceDate}}</p>
          <p><strong>Pet:</strong> {{petName}}</p>
          <p><strong>Services:</strong> {{services}}</p>
          <p><strong>Total Amount:</strong> ${{totalAmount}}</p>
        </div>
        <p>Payment is due within 30 days.</p>
        <p>Thank you for choosing {{clinicName}}!</p>
        <p>Best regards,<br>{{clinicName}} Team</p>
      </div>
    `;
  }

  async sendAppointmentReminder(appointmentData) {
    const template = this.getAppointmentReminderTemplate();
    return await this.sendEmail({
      data: {
        to: appointmentData.clientEmail,
        subject: `Appointment Reminder - ${appointmentData.petName}`,
        template,
        ...appointmentData
      }
    });
  }

  async sendAppointmentConfirmation(appointmentData) {
    const template = this.getAppointmentConfirmationTemplate();
    return await this.sendEmail({
      data: {
        to: appointmentData.clientEmail,
        subject: `Appointment Confirmed - ${appointmentData.petName}`,
        template,
        ...appointmentData
      }
    });
  }

  async sendInvoice(invoiceData) {
    const template = this.getInvoiceTemplate();
    return await this.sendEmail({
      data: {
        to: invoiceData.clientEmail,
        subject: `Invoice #${invoiceData.invoiceId}`,
        template,
        ...invoiceData
      }
    });
  }

  // Health check
  async healthCheck() {
    if (!this.isConfigured) {
      return { status: 'disabled', message: 'Email configuration incomplete' };
    }

    try {
      await this.transporter.verify();
      return { status: 'healthy', message: 'Email service ready' };
    } catch (error) {
      return { status: 'unhealthy', error: error.message };
    }
  }
}

// Create singleton instance
const emailProcessor = new EmailProcessor();

export default emailProcessor;
export { emailProcessor };
