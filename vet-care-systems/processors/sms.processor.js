import twilio from 'twi<PERSON>';
import { SMS_CONFIG } from '../config/env.js';

class SMSProcessor {
  constructor() {
    this.client = null;
    this.isConfigured = false;
    this.initializeClient();
  }

  initializeClient() {
    try {
      if (!SMS_CONFIG.accountSid || !SMS_CONFIG.authToken || !SMS_CONFIG.phoneNumber) {
        console.warn('⚠️  SMS configuration incomplete - SMS sending disabled');
        return;
      }

      this.client = twilio(SMS_CONFIG.accountSid, SMS_CONFIG.authToken);
      this.isConfigured = true;
      console.log('📱 SMS client initialized');
    } catch (error) {
      console.error('❌ SMS client initialization failed:', error);
      this.isConfigured = false;
    }
  }

  async sendSMS(job) {
    const { to, message, priority } = job.data;

    if (!this.isConfigured) {
      console.log(`📱 SMS simulation: To: ${to}, Message: ${message}`);
      return {
        status: 'simulated',
        messageId: `sim_sms_${Date.now()}`,
        to,
        message
      };
    }

    try {
      // Clean phone number (remove non-digits except +)
      const cleanPhone = to.replace(/[^\d+]/g, '');
      
      const result = await this.client.messages.create({
        body: message,
        from: SMS_CONFIG.phoneNumber,
        to: cleanPhone
      });

      console.log(`📱 SMS sent successfully to ${to}: ${result.sid}`);
      
      return {
        status: 'sent',
        messageId: result.sid,
        to: cleanPhone,
        message
      };
    } catch (error) {
      console.error(`❌ SMS sending failed to ${to}:`, error);
      throw error;
    }
  }

  // Predefined SMS templates
  getAppointmentReminderMessage(appointmentData) {
    return `Hi ${appointmentData.clientName}! Reminder: ${appointmentData.petName} has an appointment at ${appointmentData.clinicName} on ${appointmentData.appointmentDate} at ${appointmentData.appointmentTime}. Please arrive 15 mins early. Call ${appointmentData.clinicPhone} to reschedule.`;
  }

  getAppointmentConfirmationMessage(appointmentData) {
    return `Hi ${appointmentData.clientName}! Your appointment for ${appointmentData.petName} is confirmed for ${appointmentData.appointmentDate} at ${appointmentData.appointmentTime} at ${appointmentData.clinicName}. Appointment ID: ${appointmentData.appointmentId}`;
  }

  getAppointmentCancellationMessage(appointmentData) {
    return `Hi ${appointmentData.clientName}! Your appointment for ${appointmentData.petName} on ${appointmentData.appointmentDate} has been cancelled. Please call ${appointmentData.clinicPhone} to reschedule.`;
  }

  getPaymentReminderMessage(invoiceData) {
    return `Hi ${invoiceData.clientName}! This is a reminder that invoice #${invoiceData.invoiceId} for $${invoiceData.totalAmount} is due. Please contact ${invoiceData.clinicName} at ${invoiceData.clinicPhone} for payment.`;
  }

  getTestResultsMessage(testData) {
    return `Hi ${testData.clientName}! ${testData.petName}'s test results are ready. Please call ${testData.clinicName} at ${testData.clinicPhone} to discuss the results with the veterinarian.`;
  }

  async sendAppointmentReminder(appointmentData) {
    const message = this.getAppointmentReminderMessage(appointmentData);
    return await this.sendSMS({
      data: {
        to: appointmentData.clientPhone,
        message
      }
    });
  }

  async sendAppointmentConfirmation(appointmentData) {
    const message = this.getAppointmentConfirmationMessage(appointmentData);
    return await this.sendSMS({
      data: {
        to: appointmentData.clientPhone,
        message
      }
    });
  }

  async sendAppointmentCancellation(appointmentData) {
    const message = this.getAppointmentCancellationMessage(appointmentData);
    return await this.sendSMS({
      data: {
        to: appointmentData.clientPhone,
        message
      }
    });
  }

  async sendPaymentReminder(invoiceData) {
    const message = this.getPaymentReminderMessage(invoiceData);
    return await this.sendSMS({
      data: {
        to: invoiceData.clientPhone,
        message
      }
    });
  }

  async sendTestResults(testData) {
    const message = this.getTestResultsMessage(testData);
    return await this.sendSMS({
      data: {
        to: testData.clientPhone,
        message
      }
    });
  }

  // Utility methods
  validatePhoneNumber(phone) {
    // Basic phone number validation
    const cleanPhone = phone.replace(/[^\d+]/g, '');
    return cleanPhone.length >= 10 && cleanPhone.length <= 15;
  }

  formatPhoneNumber(phone) {
    // Format phone number for display
    const cleanPhone = phone.replace(/[^\d]/g, '');
    if (cleanPhone.length === 10) {
      return `(${cleanPhone.slice(0, 3)}) ${cleanPhone.slice(3, 6)}-${cleanPhone.slice(6)}`;
    }
    return phone;
  }

  // Health check
  async healthCheck() {
    if (!this.isConfigured) {
      return { status: 'disabled', message: 'SMS configuration incomplete' };
    }

    try {
      // Test by getting account info
      const account = await this.client.api.accounts(SMS_CONFIG.accountSid).fetch();
      return { 
        status: 'healthy', 
        message: 'SMS service ready',
        accountStatus: account.status
      };
    } catch (error) {
      return { status: 'unhealthy', error: error.message };
    }
  }
}

// Create singleton instance
const smsProcessor = new SMSProcessor();

export default smsProcessor;
export { smsProcessor };
