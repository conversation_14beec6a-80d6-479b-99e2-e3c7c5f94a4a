import redisConnection from '../config/redis.js';
import { NODE_ENV } from '../config/env.js';

class CacheService {
  constructor() {
    this.redis = null;
    this.isEnabled = false;
  }

  async initialize() {
    try {
      this.redis = await redisConnection.connect();
      this.isEnabled = !!this.redis;
      console.log(`🔴 Cache service ${this.isEnabled ? 'enabled' : 'disabled'}`);
    } catch (error) {
      console.error('❌ Cache service initialization failed:', error);
      this.isEnabled = false;
    }
  }

  async set(key, value, ttl = 3600) {
    if (!this.isEnabled) return false;
    
    try {
      const serializedValue = JSON.stringify(value);
      if (ttl > 0) {
        return await this.redis.setex(key, ttl, serializedValue);
      } else {
        return await this.redis.set(key, serializedValue);
      }
    } catch (error) {
      console.error('❌ Cache SET error:', error);
      return false;
    }
  }

  async get(key) {
    if (!this.isEnabled) return null;
    
    try {
      const value = await this.redis.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.error('❌ Cache GET error:', error);
      return null;
    }
  }

  async del(key) {
    if (!this.isEnabled) return false;
    
    try {
      return await this.redis.del(key);
    } catch (error) {
      console.error('❌ Cache DEL error:', error);
      return false;
    }
  }

  async invalidate(pattern) {
    if (!this.isEnabled) return 0;
    
    try {
      const keys = await this.redis.keys(pattern);
      if (keys.length > 0) {
        return await this.redis.del(...keys);
      }
      return 0;
    } catch (error) {
      console.error('❌ Cache INVALIDATE error:', error);
      return 0;
    }
  }

  async exists(key) {
    if (!this.isEnabled) return false;
    
    try {
      return await this.redis.exists(key);
    } catch (error) {
      console.error('❌ Cache EXISTS error:', error);
      return false;
    }
  }

  async increment(key, value = 1) {
    if (!this.isEnabled) return 0;
    
    try {
      return await this.redis.incrby(key, value);
    } catch (error) {
      console.error('❌ Cache INCREMENT error:', error);
      return 0;
    }
  }

  async setHash(key, field, value, ttl = 3600) {
    if (!this.isEnabled) return false;
    
    try {
      await this.redis.hset(key, field, JSON.stringify(value));
      if (ttl > 0) {
        await this.redis.expire(key, ttl);
      }
      return true;
    } catch (error) {
      console.error('❌ Cache HSET error:', error);
      return false;
    }
  }

  async getHash(key, field) {
    if (!this.isEnabled) return null;
    
    try {
      const value = await this.redis.hget(key, field);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.error('❌ Cache HGET error:', error);
      return null;
    }
  }

  async getAllHash(key) {
    if (!this.isEnabled) return {};
    
    try {
      const hash = await this.redis.hgetall(key);
      const result = {};
      for (const [field, value] of Object.entries(hash)) {
        result[field] = JSON.parse(value);
      }
      return result;
    } catch (error) {
      console.error('❌ Cache HGETALL error:', error);
      return {};
    }
  }

  // Session management methods
  async setSession(userId, sessionData, ttl = 3600) {
    const sessionKey = `session:${userId}`;
    return await this.set(sessionKey, sessionData, ttl);
  }

  async getSession(userId) {
    const sessionKey = `session:${userId}`;
    return await this.get(sessionKey);
  }

  async deleteSession(userId) {
    const sessionKey = `session:${userId}`;
    return await this.del(sessionKey);
  }

  // Permission caching
  async setUserPermissions(userId, permissions, ttl = 1800) {
    const permissionKey = `permissions:${userId}`;
    return await this.set(permissionKey, permissions, ttl);
  }

  async getUserPermissions(userId) {
    const permissionKey = `permissions:${userId}`;
    return await this.get(permissionKey);
  }

  async invalidateUserPermissions(userId) {
    const permissionKey = `permissions:${userId}`;
    return await this.del(permissionKey);
  }

  // Rate limiting
  async checkRateLimit(key, limit, window) {
    if (!this.isEnabled) return { allowed: true, remaining: limit };
    
    try {
      const current = await this.redis.incr(key);
      if (current === 1) {
        await this.redis.expire(key, window);
      }
      
      const remaining = Math.max(0, limit - current);
      return {
        allowed: current <= limit,
        remaining,
        resetTime: await this.redis.ttl(key)
      };
    } catch (error) {
      console.error('❌ Rate limit check error:', error);
      return { allowed: true, remaining: limit };
    }
  }

  // Health check
  async healthCheck() {
    if (!this.isEnabled) return { status: 'disabled' };
    
    try {
      const start = Date.now();
      await this.redis.ping();
      const latency = Date.now() - start;
      
      return {
        status: 'healthy',
        latency: `${latency}ms`,
        connected: redisConnection.isHealthy()
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message
      };
    }
  }
}

// Create singleton instance
const cacheService = new CacheService();

export default cacheService;
export { cacheService };
