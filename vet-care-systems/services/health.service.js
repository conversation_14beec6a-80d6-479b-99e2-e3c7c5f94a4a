import mongoose from 'mongoose';
import { NODE_ENV, PORT } from '../config/env.js';
import cacheService from './cache.service.js';
import queueService from './queue.service.js';
import authService from './auth.service.js';
import redisConnection from '../config/redis.js';

class HealthService {
  constructor() {
    this.startTime = new Date();
    this.version = process.env.npm_package_version || '1.0.0';
  }

  /**
   * Comprehensive health check
   */
  async getHealthStatus() {
    const checks = await Promise.allSettled([
      this.checkDatabase(),
      this.checkRedis(),
      this.checkQueue(),
      this.checkAuth(),
      this.checkMemory(),
      this.checkDisk()
    ]);

    const results = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: this.getUptime(),
      version: this.version,
      environment: NODE_ENV,
      port: PORT,
      checks: {
        database: this.getCheckResult(checks[0]),
        redis: this.getCheckResult(checks[1]),
        queue: this.getCheckResult(checks[2]),
        auth: this.getCheckResult(checks[3]),
        memory: this.getCheckResult(checks[4]),
        disk: this.getCheckResult(checks[5])
      }
    };

    // Determine overall status
    const hasUnhealthy = Object.values(results.checks).some(check => 
      check.status === 'unhealthy' || check.status === 'critical'
    );

    if (hasUnhealthy) {
      results.status = 'unhealthy';
    }

    return results;
  }

  /**
   * Quick health check for load balancers
   */
  async getQuickHealth() {
    try {
      // Quick database ping
      await mongoose.connection.db.admin().ping();
      
      return {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: this.getUptime()
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error.message
      };
    }
  }

  /**
   * Database health check
   */
  async checkDatabase() {
    try {
      const start = Date.now();
      
      // Check connection state
      if (mongoose.connection.readyState !== 1) {
        throw new Error('Database not connected');
      }

      // Ping database
      await mongoose.connection.db.admin().ping();
      
      const latency = Date.now() - start;
      
      // Get database stats
      const stats = await mongoose.connection.db.stats();
      
      return {
        status: 'healthy',
        latency: `${latency}ms`,
        connections: mongoose.connection.readyState,
        database: mongoose.connection.name,
        collections: stats.collections,
        dataSize: this.formatBytes(stats.dataSize),
        indexSize: this.formatBytes(stats.indexSize)
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message
      };
    }
  }

  /**
   * Redis health check
   */
  async checkRedis() {
    try {
      const health = await cacheService.healthCheck();
      return health;
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message
      };
    }
  }

  /**
   * Queue health check
   */
  async checkQueue() {
    try {
      const health = await queueService.healthCheck();
      return health;
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message
      };
    }
  }

  /**
   * Auth service health check
   */
  async checkAuth() {
    try {
      const health = await authService.healthCheck();
      return health;
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message
      };
    }
  }

  /**
   * Memory usage check
   */
  async checkMemory() {
    try {
      const usage = process.memoryUsage();
      const totalMem = require('os').totalmem();
      const freeMem = require('os').freemem();
      
      const memoryUsagePercent = ((totalMem - freeMem) / totalMem) * 100;
      const heapUsagePercent = (usage.heapUsed / usage.heapTotal) * 100;
      
      let status = 'healthy';
      if (memoryUsagePercent > 90 || heapUsagePercent > 90) {
        status = 'critical';
      } else if (memoryUsagePercent > 80 || heapUsagePercent > 80) {
        status = 'warning';
      }

      return {
        status,
        systemMemory: {
          total: this.formatBytes(totalMem),
          free: this.formatBytes(freeMem),
          used: this.formatBytes(totalMem - freeMem),
          usagePercent: Math.round(memoryUsagePercent * 100) / 100
        },
        processMemory: {
          rss: this.formatBytes(usage.rss),
          heapTotal: this.formatBytes(usage.heapTotal),
          heapUsed: this.formatBytes(usage.heapUsed),
          heapUsagePercent: Math.round(heapUsagePercent * 100) / 100,
          external: this.formatBytes(usage.external)
        }
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message
      };
    }
  }

  /**
   * Disk usage check
   */
  async checkDisk() {
    try {
      const fs = require('fs');
      const stats = fs.statSync('.');
      
      // This is a simplified disk check
      // In production, you might want to use a more comprehensive solution
      return {
        status: 'healthy',
        message: 'Disk check not implemented'
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message
      };
    }
  }

  /**
   * Get system metrics
   */
  async getMetrics() {
    const os = require('os');
    
    return {
      timestamp: new Date().toISOString(),
      uptime: this.getUptime(),
      system: {
        platform: os.platform(),
        arch: os.arch(),
        cpus: os.cpus().length,
        loadAverage: os.loadavg(),
        hostname: os.hostname(),
        nodeVersion: process.version
      },
      memory: await this.checkMemory(),
      database: await this.checkDatabase(),
      redis: await this.checkRedis(),
      queue: await this.checkQueue()
    };
  }

  /**
   * Get application uptime
   */
  getUptime() {
    const uptime = Date.now() - this.startTime.getTime();
    const seconds = Math.floor(uptime / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    return {
      milliseconds: uptime,
      seconds: seconds % 60,
      minutes: minutes % 60,
      hours: hours % 24,
      days,
      formatted: `${days}d ${hours % 24}h ${minutes % 60}m ${seconds % 60}s`
    };
  }

  /**
   * Format bytes to human readable format
   */
  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Get check result from Promise.allSettled
   */
  getCheckResult(settledResult) {
    if (settledResult.status === 'fulfilled') {
      return settledResult.value;
    } else {
      return {
        status: 'unhealthy',
        error: settledResult.reason?.message || 'Unknown error'
      };
    }
  }

  /**
   * Check if service is ready to accept requests
   */
  async isReady() {
    try {
      // Check critical dependencies
      await mongoose.connection.db.admin().ping();
      
      return {
        ready: true,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        ready: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Check if service is alive (basic liveness probe)
   */
  isAlive() {
    return {
      alive: true,
      timestamp: new Date().toISOString(),
      uptime: this.getUptime()
    };
  }
}

// Create singleton instance
const healthService = new HealthService();

export default healthService;
export { healthService };
