import Queue from 'bull';
import redisConnection from '../config/redis.js';
import { NODE_ENV } from '../config/env.js';

class QueueService {
  constructor() {
    this.queues = {};
    this.isEnabled = false;
  }

  async initialize() {
    try {
      const redis = redisConnection.getClient();
      if (!redis) {
        console.warn('⚠️  Queue service disabled - Redis not available');
        return;
      }

      // Initialize queues
      this.queues = {
        notifications: new Queue('notifications', { redis: redisConnection.getClient() }),
        reports: new Queue('reports', { redis: redisConnection.getClient() }),
        appointments: new Queue('appointments', { redis: redisConnection.getClient() }),
        medical: new Queue('medical', { redis: redisConnection.getClient() }),
        billing: new Queue('billing', { redis: redisConnection.getClient() }),
        ai: new Queue('ai', { redis: redisConnection.getClient() })
      };

      this.setupProcessors();
      this.setupEventHandlers();
      this.isEnabled = true;

      console.log('📋 Queue service initialized successfully');
    } catch (error) {
      console.error('❌ Queue service initialization failed:', error);
      this.isEnabled = false;
    }
  }

  setupProcessors() {
    if (!this.isEnabled) return;

    // Email notifications
    this.queues.notifications.process('email', 5, async (job) => {
      const { to, subject, body, template } = job.data;
      console.log(`📧 Processing email to ${to}: ${subject}`);
      // Email processor implementation will be added later
      return { status: 'sent', messageId: `email_${Date.now()}` };
    });

    // SMS notifications
    this.queues.notifications.process('sms', 10, async (job) => {
      const { to, message } = job.data;
      console.log(`📱 Processing SMS to ${to}: ${message}`);
      // SMS processor implementation will be added later
      return { status: 'sent', messageId: `sms_${Date.now()}` };
    });

    // Report generation
    this.queues.reports.process('generate', 2, async (job) => {
      const { type, clinicId, dateRange, format } = job.data;
      console.log(`📊 Generating ${type} report for clinic ${clinicId}`);
      // Report processor implementation will be added later
      return { status: 'generated', reportId: `report_${Date.now()}` };
    });

    // Appointment reminders
    this.queues.appointments.process('reminder', 10, async (job) => {
      const { appointmentId, clientId, reminderType } = job.data;
      console.log(`⏰ Processing ${reminderType} reminder for appointment ${appointmentId}`);
      // Reminder processor implementation will be added later
      return { status: 'sent', reminderType };
    });

    // Medical record processing
    this.queues.medical.process('record', 5, async (job) => {
      const { appointmentId, petId, recordData } = job.data;
      console.log(`🏥 Processing medical record for pet ${petId}`);
      // Medical record processor implementation will be added later
      return { status: 'processed', recordId: `record_${Date.now()}` };
    });

    // Billing and invoice processing
    this.queues.billing.process('invoice', 5, async (job) => {
      const { appointmentId, clientId, amount } = job.data;
      console.log(`💰 Processing invoice for appointment ${appointmentId}`);
      // Billing processor implementation will be added later
      return { status: 'processed', invoiceId: `invoice_${Date.now()}` };
    });

    // AI processing
    this.queues.ai.process('suggestion', 3, async (job) => {
      const { type, data } = job.data;
      console.log(`🤖 Processing AI ${type} suggestion`);
      // AI processor implementation will be added later
      return { status: 'processed', suggestions: [] };
    });
  }

  setupEventHandlers() {
    if (!this.isEnabled) return;

    Object.entries(this.queues).forEach(([queueName, queue]) => {
      queue.on('completed', (job, result) => {
        console.log(`✅ Job ${job.id} in ${queueName} completed:`, result);
      });

      queue.on('failed', (job, err) => {
        console.error(`❌ Job ${job.id} in ${queueName} failed:`, err.message);
      });

      queue.on('stalled', (job) => {
        console.warn(`⚠️  Job ${job.id} in ${queueName} stalled`);
      });

      queue.on('progress', (job, progress) => {
        console.log(`📈 Job ${job.id} in ${queueName} progress: ${progress}%`);
      });
    });
  }

  async addJob(queueName, jobType, data, options = {}) {
    if (!this.isEnabled) {
      console.warn(`⚠️  Queue ${queueName} not available - job skipped`);
      return null;
    }

    try {
      const queue = this.queues[queueName];
      if (!queue) {
        throw new Error(`Queue ${queueName} not found`);
      }

      return await queue.add(jobType, data, {
        delay: options.delay || 0,
        attempts: options.attempts || 3,
        backoff: options.backoff || 'exponential',
        removeOnComplete: options.removeOnComplete || 10,
        removeOnFail: options.removeOnFail || 5,
        priority: options.priority || 0,
        ...options
      });
    } catch (error) {
      console.error(`❌ Error adding job to ${queueName}:`, error);
      throw error;
    }
  }

  async getQueueStats(queueName) {
    if (!this.isEnabled) return null;

    const queue = this.queues[queueName];
    if (!queue) {
      throw new Error(`Queue ${queueName} not found`);
    }

    try {
      const [waiting, active, completed, failed, delayed] = await Promise.all([
        queue.getWaiting(),
        queue.getActive(),
        queue.getCompleted(),
        queue.getFailed(),
        queue.getDelayed()
      ]);

      return {
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length,
        delayed: delayed.length,
        total: waiting.length + active.length + completed.length + failed.length + delayed.length
      };
    } catch (error) {
      console.error(`❌ Error getting stats for ${queueName}:`, error);
      return null;
    }
  }

  async getAllQueueStats() {
    if (!this.isEnabled) return {};

    const stats = {};
    for (const queueName of Object.keys(this.queues)) {
      stats[queueName] = await this.getQueueStats(queueName);
    }
    return stats;
  }

  async pauseQueue(queueName) {
    if (!this.isEnabled) return false;
    
    const queue = this.queues[queueName];
    if (queue) {
      await queue.pause();
      return true;
    }
    return false;
  }

  async resumeQueue(queueName) {
    if (!this.isEnabled) return false;
    
    const queue = this.queues[queueName];
    if (queue) {
      await queue.resume();
      return true;
    }
    return false;
  }

  async cleanQueue(queueName, grace = 0, status = 'completed') {
    if (!this.isEnabled) return 0;
    
    const queue = this.queues[queueName];
    if (queue) {
      return await queue.clean(grace, status);
    }
    return 0;
  }

  // Convenience methods for common operations
  async sendEmail(to, subject, body, options = {}) {
    return await this.addJob('notifications', 'email', {
      to, subject, body, ...options
    });
  }

  async sendSMS(to, message, options = {}) {
    return await this.addJob('notifications', 'sms', {
      to, message, ...options
    });
  }

  async scheduleAppointmentReminder(appointmentId, clientId, reminderType, delay = 0) {
    return await this.addJob('appointments', 'reminder', {
      appointmentId, clientId, reminderType
    }, { delay });
  }

  async generateReport(type, clinicId, dateRange, format = 'pdf') {
    return await this.addJob('reports', 'generate', {
      type, clinicId, dateRange, format
    });
  }

  async processAISuggestion(type, data) {
    return await this.addJob('ai', 'suggestion', {
      type, data
    });
  }

  // Health check
  async healthCheck() {
    if (!this.isEnabled) {
      return { status: 'disabled', message: 'Queue service not available' };
    }

    try {
      const stats = await this.getAllQueueStats();
      return {
        status: 'healthy',
        queues: Object.keys(this.queues).length,
        stats
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message
      };
    }
  }
}

// Create singleton instance
const queueService = new QueueService();

export default queueService;
export { queueService };
