/**
 * Appointment Scheduler Service
 * 
 * Handles appointment availability checking, conflict detection, and intelligent scheduling
 * with real-time slot locking to prevent double bookings.
 */

import Appointment from '../models/appointment.model.js';
import Staff from '../models/staff.model.js';
import { sendResponse } from '../utils/responseHandler.js';
import webSocketService from '../utils/websocketService.js';

class AppointmentSchedulerService {
    constructor() {
        this.lockedSlots = new Map(); // In-memory slot locking
        this.lockTimeout = 5 * 60 * 1000; // 5 minutes lock timeout
    }

    /**
     * Check appointment availability for a specific time slot
     * @param {Object} params - Scheduling parameters
     * @returns {Promise<Object>} Availability result
     */
    async checkAvailability({
        clinicId,
        staffId,
        appointmentDate,
        estimatedDuration = 30,
        excludeAppointmentId = null
    }) {
        try {
            const startTime = new Date(appointmentDate);
            const endTime = new Date(startTime.getTime() + (estimatedDuration * 60000));

            // Check for existing appointments that overlap
            const conflictQuery = {
                clinicId,
                appointmentDate: {
                    $gte: new Date(startTime.getTime() - (60 * 60000)), // 1 hour buffer
                    $lte: new Date(endTime.getTime() + (60 * 60000))
                },
                status: { $nin: ['cancelled', 'no_show'] }
            };

            if (excludeAppointmentId) {
                conflictQuery.appointmentId = { $ne: excludeAppointmentId };
            }

            if (staffId) {
                conflictQuery.staffAssigned = staffId;
            }

            const conflictingAppointments = await Appointment.find(conflictQuery)
                .select('appointmentId appointmentDate estimatedDuration staffAssigned staffAssignedName petName clientName')
                .lean();

            // Check for exact time conflicts
            const exactConflicts = conflictingAppointments.filter(apt => {
                const aptStart = new Date(apt.appointmentDate);
                const aptEnd = new Date(aptStart.getTime() + ((apt.estimatedDuration || 30) * 60000));
                
                return (startTime < aptEnd && endTime > aptStart);
            });

            // Check locked slots
            const slotKey = `${clinicId}_${staffId || 'any'}_${startTime.getTime()}`;
            const isSlotLocked = this.lockedSlots.has(slotKey);

            // Get staff availability if staffId provided
            let staffAvailable = true;
            if (staffId) {
                const staff = await Staff.findOne({ staffId, status: 1 })
                    .select('firstName lastName workingHours')
                    .lean();
                
                if (!staff) {
                    staffAvailable = false;
                }
                
                // Check working hours (if implemented)
                // staffAvailable = this.isWithinWorkingHours(startTime, staff.workingHours);
            }

            return {
                available: exactConflicts.length === 0 && !isSlotLocked && staffAvailable,
                conflicts: exactConflicts,
                isLocked: isSlotLocked,
                staffAvailable,
                suggestedAlternatives: exactConflicts.length > 0 ? 
                    await this.suggestAlternativeSlots({
                        clinicId,
                        staffId,
                        preferredDate: startTime,
                        duration: estimatedDuration
                    }) : []
            };
        } catch (error) {
            console.error('Error checking appointment availability:', error);
            throw new Error('Failed to check appointment availability');
        }
    }

    /**
     * Lock a time slot temporarily during booking process
     * @param {Object} params - Lock parameters
     * @returns {string} Lock key
     */
    lockTimeSlot({ clinicId, staffId, appointmentDate, sessionId }) {
        const slotKey = `${clinicId}_${staffId || 'any'}_${new Date(appointmentDate).getTime()}`;
        const lockData = {
            sessionId,
            lockedAt: new Date(),
            expiresAt: new Date(Date.now() + this.lockTimeout)
        };

        this.lockedSlots.set(slotKey, lockData);

        // Auto-release lock after timeout
        setTimeout(() => {
            if (this.lockedSlots.has(slotKey)) {
                this.lockedSlots.delete(slotKey);
                console.log(`Auto-released expired lock: ${slotKey}`);
            }
        }, this.lockTimeout);

        return slotKey;
    }

    /**
     * Release a locked time slot
     * @param {string} lockKey - Lock key to release
     * @param {string} sessionId - Session ID for verification
     */
    releaseTimeSlot(lockKey, sessionId) {
        const lockData = this.lockedSlots.get(lockKey);
        if (lockData && lockData.sessionId === sessionId) {
            this.lockedSlots.delete(lockKey);
            return true;
        }
        return false;
    }

    /**
     * Suggest alternative time slots when conflicts exist
     * @param {Object} params - Suggestion parameters
     * @returns {Promise<Array>} Alternative slots
     */
    async suggestAlternativeSlots({
        clinicId,
        staffId,
        preferredDate,
        duration = 30,
        maxSuggestions = 5
    }) {
        try {
            const suggestions = [];
            const baseDate = new Date(preferredDate);
            const searchDays = 7; // Search within next 7 days

            for (let dayOffset = 0; dayOffset < searchDays && suggestions.length < maxSuggestions; dayOffset++) {
                const searchDate = new Date(baseDate);
                searchDate.setDate(baseDate.getDate() + dayOffset);

                // Generate time slots for the day (9 AM to 5 PM, 30-minute intervals)
                for (let hour = 9; hour < 17; hour++) {
                    for (let minute = 0; minute < 60; minute += 30) {
                        if (suggestions.length >= maxSuggestions) break;

                        const slotTime = new Date(searchDate);
                        slotTime.setHours(hour, minute, 0, 0);

                        // Skip past times
                        if (slotTime <= new Date()) continue;

                        const availability = await this.checkAvailability({
                            clinicId,
                            staffId,
                            appointmentDate: slotTime,
                            estimatedDuration: duration
                        });

                        if (availability.available) {
                            suggestions.push({
                                date: slotTime,
                                dayOfWeek: slotTime.toLocaleDateString('en-US', { weekday: 'long' }),
                                time: slotTime.toLocaleTimeString('en-US', { 
                                    hour: '2-digit', 
                                    minute: '2-digit' 
                                }),
                                staffAvailable: availability.staffAvailable
                            });
                        }
                    }
                }
            }

            return suggestions;
        } catch (error) {
            console.error('Error suggesting alternative slots:', error);
            return [];
        }
    }

    /**
     * Get daily schedule for a clinic/staff member
     * @param {Object} params - Schedule parameters
     * @returns {Promise<Object>} Daily schedule
     */
    async getDailySchedule({ clinicId, staffId, date }) {
        try {
            const startOfDay = new Date(date);
            startOfDay.setHours(0, 0, 0, 0);
            
            const endOfDay = new Date(date);
            endOfDay.setHours(23, 59, 59, 999);

            const query = {
                clinicId,
                appointmentDate: {
                    $gte: startOfDay,
                    $lte: endOfDay
                },
                status: { $nin: ['cancelled'] }
            };

            if (staffId) {
                query.staffAssigned = staffId;
            }

            const appointments = await Appointment.find(query)
                .select('appointmentId appointmentDate estimatedDuration status priority petName clientName staffAssignedName')
                .sort({ appointmentDate: 1 })
                .lean();

            // Calculate schedule metrics
            const totalAppointments = appointments.length;
            const totalDuration = appointments.reduce((sum, apt) => sum + (apt.estimatedDuration || 30), 0);
            const busySlots = appointments.length;
            const availableSlots = this.calculateAvailableSlots(appointments, startOfDay, endOfDay);

            return {
                date: startOfDay,
                appointments,
                metrics: {
                    totalAppointments,
                    totalDuration,
                    busySlots,
                    availableSlots,
                    utilizationRate: Math.round((busySlots / (busySlots + availableSlots)) * 100)
                }
            };
        } catch (error) {
            console.error('Error getting daily schedule:', error);
            throw new Error('Failed to get daily schedule');
        }
    }

    /**
     * Calculate available time slots for a day
     * @param {Array} appointments - Existing appointments
     * @param {Date} startOfDay - Start of day
     * @param {Date} endOfDay - End of day
     * @returns {number} Number of available slots
     */
    calculateAvailableSlots(appointments, startOfDay, endOfDay) {
        const workingHours = 8; // 9 AM to 5 PM
        const slotDuration = 30; // 30 minutes
        const totalSlots = (workingHours * 60) / slotDuration;
        
        return Math.max(0, totalSlots - appointments.length);
    }

    /**
     * Optimize daily schedule by suggesting better arrangements
     * @param {Object} params - Optimization parameters
     * @returns {Promise<Object>} Optimization suggestions
     */
    async optimizeSchedule({ clinicId, staffId, date }) {
        try {
            const schedule = await this.getDailySchedule({ clinicId, staffId, date });
            const appointments = schedule.appointments;

            const suggestions = [];

            // Check for gaps that could be filled
            for (let i = 0; i < appointments.length - 1; i++) {
                const current = appointments[i];
                const next = appointments[i + 1];
                
                const currentEnd = new Date(current.appointmentDate.getTime() + (current.estimatedDuration * 60000));
                const nextStart = new Date(next.appointmentDate);
                const gapMinutes = (nextStart - currentEnd) / 60000;

                if (gapMinutes > 60) { // Gap longer than 1 hour
                    suggestions.push({
                        type: 'fill_gap',
                        message: `${Math.round(gapMinutes)} minute gap between appointments`,
                        timeSlot: currentEnd,
                        duration: Math.min(gapMinutes - 15, 60) // Leave 15 min buffer
                    });
                }
            }

            // Check for back-to-back high-priority appointments
            const highPriorityCount = appointments.filter(apt => apt.priority === 'high' || apt.priority === 'emergency').length;
            if (highPriorityCount > 3) {
                suggestions.push({
                    type: 'distribute_priority',
                    message: 'Consider distributing high-priority appointments throughout the day',
                    count: highPriorityCount
                });
            }

            return {
                schedule,
                suggestions,
                optimizationScore: this.calculateOptimizationScore(appointments)
            };
        } catch (error) {
            console.error('Error optimizing schedule:', error);
            throw new Error('Failed to optimize schedule');
        }
    }

    /**
     * Calculate optimization score for a schedule
     * @param {Array} appointments - Appointments array
     * @returns {number} Score from 0-100
     */
    calculateOptimizationScore(appointments) {
        if (appointments.length === 0) return 100;

        let score = 100;
        
        // Penalize for large gaps
        for (let i = 0; i < appointments.length - 1; i++) {
            const current = appointments[i];
            const next = appointments[i + 1];
            const gapMinutes = (new Date(next.appointmentDate) - new Date(current.appointmentDate)) / 60000 - current.estimatedDuration;
            
            if (gapMinutes > 60) {
                score -= Math.min(20, gapMinutes / 10);
            }
        }

        // Penalize for too many consecutive high-priority appointments
        let consecutiveHighPriority = 0;
        for (const apt of appointments) {
            if (apt.priority === 'high' || apt.priority === 'emergency') {
                consecutiveHighPriority++;
                if (consecutiveHighPriority > 2) {
                    score -= 10;
                }
            } else {
                consecutiveHighPriority = 0;
            }
        }

        return Math.max(0, Math.round(score));
    }

    /**
     * Handle appointment conflicts when they occur
     * @param {Object} conflictData - Conflict information
     * @returns {Promise<Object>} Resolution options
     */
    async handleConflict(conflictData) {
        const { appointmentId, conflictingAppointments, clinicId } = conflictData;

        // Notify relevant staff about the conflict
        webSocketService.broadcastToClinic(clinicId, 'appointment_conflict', {
            type: 'scheduling_conflict',
            appointmentId,
            conflicts: conflictingAppointments,
            timestamp: new Date()
        });

        // Generate resolution options
        const resolutionOptions = [
            {
                type: 'reschedule_new',
                description: 'Reschedule the new appointment',
                action: 'suggest_alternatives'
            },
            {
                type: 'reschedule_existing',
                description: 'Reschedule existing appointment',
                action: 'move_existing'
            },
            {
                type: 'extend_duration',
                description: 'Extend appointment duration',
                action: 'modify_timing'
            },
            {
                type: 'assign_different_staff',
                description: 'Assign to different staff member',
                action: 'change_staff'
            }
        ];

        return {
            conflictId: `conflict_${Date.now()}`,
            conflictingAppointments,
            resolutionOptions,
            suggestedAction: resolutionOptions[0] // Default to rescheduling new appointment
        };
    }

    /**
     * Clean up expired locks periodically
     */
    cleanupExpiredLocks() {
        const now = new Date();
        for (const [key, lockData] of this.lockedSlots.entries()) {
            if (lockData.expiresAt <= now) {
                this.lockedSlots.delete(key);
            }
        }
    }
}

// Create singleton instance
const appointmentScheduler = new AppointmentSchedulerService();

// Clean up expired locks every minute
setInterval(() => {
    appointmentScheduler.cleanupExpiredLocks();
}, 60000);

export default appointmentScheduler;
