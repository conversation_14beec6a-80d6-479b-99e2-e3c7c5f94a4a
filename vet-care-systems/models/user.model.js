import mongoose from 'mongoose';
import bcrypt from 'bcryptjs';
import AutoIncrementFactory from 'mongoose-sequence';

const AutoIncrement = AutoIncrementFactory(mongoose);

const userSchema = new mongoose.Schema({
    // Auto-increment ID
    userId: {
        type: Number,
        unique: true,
    },
    // Authentication fields
    email: {
        type: String,
        required: [true, "Email is required"],
        unique: true,
        lowercase: true,
        trim: true,
        match: [/\S+@\S+\.\S+/, 'Please enter a valid email address'],
    },
    password: {
        type: String,
        required: [true, "Password is required"],
        minLength: 6,
    },

    // Personal information
    firstName: {
        type: String,
        required: [true, "First name is required"],
        trim: true,
        minLength: 2,
        maxLength: 55,
    },
    middleName: {
        type: String,
        required: [false],
        trim: true,
        maxLength: 55,
    },
    lastName: {
        type: String,
        required: [true, "Last name is required"],
        trim: true,
        minLength: 2,
        maxLength: 55,
    },
    phoneNumber: {
        type: String,
        required: [true, "Phone number is required"],
        trim: true,
        min: 10,
        max: 14,
    },
    address: {
        type: String,
        required: false,
    },
    dob: {
        type: Date,
        required: false,
    },

    // User role
    roleId: {
        type: Number,
        ref: 'Role',
        required: [true, 'Role ID is required'],
    },
    permissions: [{
        type: Number,
        ref: 'Permission',
    }],

    // Status
    status: {
        type: Number,
        enum: [0, 1], // 0: Inactive, 1: Active
        default: 1, // Default to Active
    },

    // Login tracking fields
    lastLogin: {
        type: Date,
        default: null
    },
    loginCount: {
        type: Number,
        default: 0
    },
    loginHistory: [{
        timestamp: {
            type: Date,
            default: Date.now
        },
        ipAddress: String,
        userAgent: String,
        status: {
            type: String,
            enum: ['success', 'failed'],
            default: 'success'
        }
    }],
}, {timestamps: true});

// Define default admin user
const defaultAdmin = {
    email: '<EMAIL>',
    password: 'pass123',
    firstName: 'Admin',
    middleName: '',
    lastName: 'User',
    phoneNumber: '1234567890',
    address: '123 Admin Street',
    dob: new Date('1990-01-01'),
    roleId: 1001,
    permissions: [1001, 1002, 1003, 1004, 1005, 1006, 1007, 1008, 1009], // All permissions
    status: 1
};

// Add method to initialize default admin
userSchema.statics.initializeDefaultAdmin = async function() {
    try {
        // Check if admin already exists
        const existingAdmin = await this.findOne({ email: defaultAdmin.email }).lean();

        if (existingAdmin) {
            console.log('Default admin already exists:');
            console.log(`Email: ${existingAdmin.email}`);
            console.log(`Role: ${existingAdmin.role}`);
            return;
        }

        // Hash password
        const salt = await bcrypt.genSalt(10);
        const hashedPassword = await bcrypt.hash(defaultAdmin.password, salt);

        // Create admin user
        const adminUser = await this.create({
            ...defaultAdmin,
            password: hashedPassword
        });

        console.log('Default admin created successfully:');
        console.log(`Email: ${adminUser.email}`);
        console.log(`Password: ${defaultAdmin.password} (unhashed)`);
        console.log(`Role: ${adminUser.role}`);
    } catch (error) {
        console.error('Error creating default admin:', error);
    }
};

// Add auto-increment plugin
userSchema.plugin(AutoIncrement, { inc_field: 'userId', start_seq: 1001 });
userSchema.index({ userId: 1 }, { unique: true });

const User = mongoose.model('User', userSchema);

// Function to reset collection if schema changes
const resetCollectionIfSchemaChanged = async () => {
    try {
        if (!mongoose.connection.db) {
            console.log("Waiting for database connection...");
            return;
        }

        const collectionName = User.collection.name;
        const existingCollections = await mongoose.connection.db.listCollections().toArray();
        const collectionExists = existingCollections.some(col => col.name === collectionName);

        if (collectionExists) {
            console.log(`Dropping collection: ${collectionName} due to schema changes...`);
            await mongoose.connection.db.dropCollection(collectionName);
        }

        // Ensure collection is created again
        await User.init();
        console.log(`Recreated collection: ${collectionName}`);

        // Initialize default admin after collection is created
        await User.initializeDefaultAdmin();
    } catch (error) {
        console.error("Error resetting collection:", error);
    }
};

// Run after Mongoose connects to DB
// mongoose.connection.once("open", resetCollectionIfSchemaChanged);

export default User;
