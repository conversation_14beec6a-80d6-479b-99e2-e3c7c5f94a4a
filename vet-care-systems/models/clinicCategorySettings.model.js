import mongoose from "mongoose";
import AutoIncrementFactory from 'mongoose-sequence';

const AutoIncrement = AutoIncrementFactory(mongoose);

const clinicCategorySettingsSchema = new mongoose.Schema({
    // Auto-increment ID
    clinicCategorySettingsId: {
        type: Number,
        index: true,
        unique: true,
    },
    
    // Reference to the clinic
    clinicId: {
        type: Number,
        ref: 'Clinic',
        required: [true, "Clinic ID is required"],
        index: true,
    },
    
    // Reference to the appointment category
    appointmentCategoryId: {
        type: Number,
        ref: 'AppointmentCategory',
        required: [true, "Appointment category ID is required"],
        index: true,
    },
    
    // Whether this clinic offers this category
    isEnabled: {
        type: Boolean,
        default: true,
        index: true,
    },
    
    // Clinic-specific customizations
    customName: {
        type: String,
        trim: true,
        maxLength: 100,
    },
    
    customDescription: {
        type: String,
        trim: true,
        maxLength: 500,
    },
    
    // Clinic-specific pricing (overrides default)
    customCharge: {
        type: Number,
        min: 0
    },
    
    // Clinic-specific discount
    customDiscountPercentage: {
        type: Number,
        default: 0,
        min: 0,
        max: 100
    },
    
    // Currency for the clinic charge
    currency: {
        type: String,
        default: 'KES',
        enum: ['KES', 'USD', 'EUR', 'GBP']
    },
    
    // Clinic-specific duration override
    customDuration: {
        type: Number, // in minutes
        min: 5,
        max: 480 // 8 hours max
    },
    
    // Display order for this clinic
    displayOrder: {
        type: Number,
        default: 0,
        index: true,
    },
    
    // Staff roles that can handle this category in this clinic
    allowedStaffRoles: [{
        type: String,
        enum: ['veterinarian', 'vet_tech', 'groomer', 'receptionist', 'lab_tech', 'assistant']
    }],
    
    // Preferred staff for this category in this clinic
    preferredStaff: [{
        staffId: {
            type: Number,
            ref: 'Staff'
        },
        priority: {
            type: Number,
            default: 1
        }
    }],
    
    // Equipment requirements specific to this clinic
    requiresSpecificEquipment: [{
        equipmentName: String,
        isRequired: Boolean,
        notes: String
    }],
    
    // Clinic-specific notes
    clinicNotes: {
        type: String,
        trim: true
    },
    
    // Referral settings for this clinic
    referralSettings: {
        canReferOut: {
            type: Boolean,
            default: false
        },
        canReceiveReferrals: {
            type: Boolean,
            default: false
        },
        preferredReferralClinics: [{
            clinicId: {
                type: Number,
                ref: 'Clinic'
            },
            clinicName: String,
            contactInfo: String,
            notes: String
        }]
    },
    
    // Audit fields
    createdBy: {
        type: Number,
        ref: 'Staff',
        required: [true, "Created by is required"],
        index: true,
    },
    modifiedBy: {
        type: Number,
        ref: 'Staff',
        index: true,
    }
}, {
    timestamps: true,
    collection: 'clinicCategorySettings'
});

// Add auto-increment plugin
clinicCategorySettingsSchema.plugin(AutoIncrement, { inc_field: 'clinicCategorySettingsId', start_seq: 1001 });
clinicCategorySettingsSchema.index({ clinicCategorySettingsId: 1 }, { unique: true });

// Compound indexes for efficient queries
clinicCategorySettingsSchema.index({ clinicId: 1, appointmentCategoryId: 1 }, { unique: true });
clinicCategorySettingsSchema.index({ clinicId: 1, isEnabled: 1, displayOrder: 1 });
clinicCategorySettingsSchema.index({ appointmentCategoryId: 1, isEnabled: 1 });

// Virtual for the appointment category data
clinicCategorySettingsSchema.virtual('categoryData', {
    ref: 'AppointmentCategory',
    localField: 'appointmentCategoryId',
    foreignField: 'appointmentCategoryId',
    justOne: true
});

// Virtual for clinic data
clinicCategorySettingsSchema.virtual('clinicData', {
    ref: 'Clinic',
    localField: 'clinicId',
    foreignField: 'clinicId',
    justOne: true
});

// Method to get effective values (clinic-specific or global defaults)
clinicCategorySettingsSchema.methods.getEffectiveValues = function() {
    const categoryData = this.categoryData || {};
    return {
        name: this.customName || categoryData.name || 'Unknown Category',
        description: this.customDescription || categoryData.description || '',
        charge: this.customCharge !== undefined ? this.customCharge : (categoryData.defaultCharge || 0),
        discountPercentage: this.customDiscountPercentage || (categoryData.defaultDiscountPercentage || 0),
        duration: this.customDuration || categoryData.estimatedDuration || 30,
        currency: this.currency || categoryData.currency || 'KES',
        requiresEquipment: categoryData.requiresEquipment || false,
        requiresQualification: categoryData.requiresQualification || false,
        defaultStaffRoles: this.allowedStaffRoles.length > 0 ? this.allowedStaffRoles : (categoryData.defaultStaffRoles || [])
    };
};

const ClinicCategorySettings = mongoose.model("ClinicCategorySettings", clinicCategorySettingsSchema);

export default ClinicCategorySettings;
