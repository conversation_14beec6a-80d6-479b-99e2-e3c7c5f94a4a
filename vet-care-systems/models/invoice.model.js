import mongoose from 'mongoose';
import AutoIncrementFactory from 'mongoose-sequence';

const AutoIncrement = AutoIncrementFactory(mongoose);

/**
 * Invoice Model
 * 
 * This model represents an invoice for veterinary services.
 * It includes charges, discounts, taxes, and payment tracking.
 */
const invoiceSchema = new mongoose.Schema({
    // Auto-increment ID
    invoiceId: {
        type: Number,
        index: true,
        unique: true,
    },
    
    // Invoice number (formatted)
    invoiceNumber: {
        type: String,
        required: true,
        unique: true,
        index: true
    },
    
    // Related records
    appointmentId: {
        type: Number,
        required: true,
        index: true
    },
    healthRecordId: {
        type: Number,
        index: true
    },
    
    // Client and pet information
    clientId: {
        type: Number,
        required: true,
        index: true
    },
    petId: {
        type: Number,
        required: true,
        index: true
    },
    
    // Clinic information
    clinicId: {
        type: Number,
        required: true,
        index: true
    },
    
    // Invoice details
    invoiceDate: {
        type: Date,
        required: true,
        default: Date.now
    },
    dueDate: {
        type: Date,
        required: true
    },
    
    // Appointment Categories and Services
    appointmentCategories: [{
        appointmentCategoryId: {
            type: Number,
            required: true,
            ref: 'AppointmentCategory'
        },
        categoryName: {
            type: String,
            required: true
        },
        categoryCharge: {
            type: Number,
            required: true,
            min: 0
        },
        categoryDiscount: {
            type: Number,
            default: 0,
            min: 0
        },
        categoryDiscountPercentage: {
            type: Number,
            default: 0,
            min: 0,
            max: 100
        },
        categoryTotal: {
            type: Number,
            required: true,
            min: 0
        },
        staffAssigned: {
            type: Number,
            ref: 'Staff'
        },
        staffAssignedName: String,

        // Services within this category
        categoryServices: [{
            categoryServiceId: {
                type: Number,
                required: true,
                ref: 'CategoryService'
            },
            serviceName: {
                type: String,
                required: true
            },
            description: String,
            quantity: {
                type: Number,
                required: true,
                default: 1,
                min: 1
            },
            unitPrice: {
                type: Number,
                required: true,
                min: 0
            },
            serviceDiscount: {
                type: Number,
                default: 0,
                min: 0
            },
            serviceDiscountPercentage: {
                type: Number,
                default: 0,
                min: 0,
                max: 100
            },
            totalPrice: {
                type: Number,
                required: true,
                min: 0
            },
            performedBy: {
                type: Number,
                ref: 'Staff'
            },
            performedByName: String,
            notes: String
        }]
    }],

    // Legacy services field (for backward compatibility)
    services: [{
        serviceId: {
            type: Number,
            required: true
        },
        serviceName: {
            type: String,
            required: true
        },
        description: String,
        quantity: {
            type: Number,
            required: true,
            default: 1,
            min: 1
        },
        unitPrice: {
            type: Number,
            required: true,
            min: 0
        },
        totalPrice: {
            type: Number,
            required: true,
            min: 0
        }
    }],
    
    // Medications and supplies
    medications: [{
        inventoryItemId: {
            type: Number,
            required: true
        },
        medicationName: {
            type: String,
            required: true
        },
        quantity: {
            type: Number,
            required: true,
            min: 1
        },
        unitPrice: {
            type: Number,
            required: true,
            min: 0
        },
        totalPrice: {
            type: Number,
            required: true,
            min: 0
        }
    }],
    
    // Financial calculations
    subtotal: {
        type: Number,
        required: true,
        min: 0
    },
    
    // Discounts
    discounts: [{
        discountId: {
            type: Number,
            required: true
        },
        discountType: {
            type: String,
            enum: ['percentage', 'fixed'],
            required: true
        },
        discountValue: {
            type: Number,
            required: true,
            min: 0
        },
        discountAmount: {
            type: Number,
            required: true,
            min: 0
        },
        reason: String,
        appliedBy: {
            type: Number,
            required: true
        }
    }],
    
    totalDiscounts: {
        type: Number,
        default: 0,
        min: 0
    },
    
    // After-hours charges
    afterHoursCharge: {
        type: Number,
        default: 0,
        min: 0
    },
    
    // Tax information
    taxRate: {
        type: Number,
        default: 0,
        min: 0,
        max: 100
    },
    taxAmount: {
        type: Number,
        default: 0,
        min: 0
    },
    
    // Final amounts
    totalAmount: {
        type: Number,
        required: true,
        min: 0
    },
    
    // Currency
    currency: {
        type: String,
        required: true,
        enum: ['KES', 'USD', 'EUR', 'GBP', 'CNY', 'JPY', 'AUD', 'CAD', 'CHF', 'AED'],
        default: 'KES'
    },
    
    // Payment tracking
    paymentStatus: {
        type: String,
        enum: ['pending', 'partial', 'paid', 'overdue', 'cancelled'],
        default: 'pending',
        index: true
    },
    
    amountPaid: {
        type: Number,
        default: 0,
        min: 0
    },
    
    amountDue: {
        type: Number,
        required: true,
        min: 0
    },
    
    // Staff information
    generatedBy: {
        type: Number,
        required: true,
        index: true
    },
    
    // Notes
    notes: {
        type: String,
        trim: true
    },
    
    // Invoice status
    status: {
        type: String,
        enum: ['draft', 'sent', 'viewed', 'paid', 'cancelled'],
        default: 'draft',
        index: true
    }
}, { timestamps: true });

// Add indexes for better query performance
invoiceSchema.index({ appointmentId: 1 });
invoiceSchema.index({ clientId: 1, invoiceDate: -1 });
invoiceSchema.index({ clinicId: 1, invoiceDate: -1 });
invoiceSchema.index({ paymentStatus: 1 });
invoiceSchema.index({ status: 1 });
invoiceSchema.index({ dueDate: 1 });

// Pre-save middleware to generate invoice number
invoiceSchema.pre('save', async function(next) {
    if (this.isNew && !this.invoiceNumber) {
        const year = new Date().getFullYear();
        const month = String(new Date().getMonth() + 1).padStart(2, '0');
        const count = await this.constructor.countDocuments({
            invoiceDate: {
                $gte: new Date(year, new Date().getMonth(), 1),
                $lt: new Date(year, new Date().getMonth() + 1, 1)
            }
        });
        this.invoiceNumber = `INV-${year}${month}-${String(count + 1).padStart(4, '0')}`;
    }
    
    // Calculate amount due
    this.amountDue = this.totalAmount - this.amountPaid;
    
    // Update payment status based on amounts
    if (this.amountPaid === 0) {
        this.paymentStatus = 'pending';
    } else if (this.amountPaid >= this.totalAmount) {
        this.paymentStatus = 'paid';
    } else {
        this.paymentStatus = 'partial';
    }
    
    next();
});

// Add auto-increment plugin
invoiceSchema.plugin(AutoIncrement, { inc_field: 'invoiceId', start_seq: 1001 });
invoiceSchema.index({ invoiceId: 1 }, { unique: true });

const Invoice = mongoose.model('Invoice', invoiceSchema);

export default Invoice;
