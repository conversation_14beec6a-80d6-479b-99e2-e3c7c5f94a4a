import mongoose from 'mongoose';
import AutoIncrementFactory from 'mongoose-sequence';

const AutoIncrement = AutoIncrementFactory(mongoose);

const categoryServiceSchema = new mongoose.Schema({
    // Auto-increment ID
    categoryServiceId: {
        type: Number,
        index: true,
        unique: true,
    },
    categoryServiceName: {
        type: String,
        required: true,
        trim: true,
        maxlength: 200,
        index: true
    },
    categoryServiceCode: {
        type: String,
        trim: true,
        maxlength: 50,
        index: true
    },
    description: {
        type: String,
        trim: true,
        maxlength: 1000
    },
    appointmentCategoryId: {
        type: Number,
        required: true,
        ref: 'AppointmentCategory',
        index: true
    },
    defaultPrice: {
        type: Number,
        required: true,
        min: 0
    },
    currency: {
        type: String,
        required: true,
        default: 'KES',
        enum: ['KES', 'USD', 'EUR', 'GBP']
    },
    estimatedDuration: {
        type: Number, // in minutes
        min: 0,
        default: 15
    },
    isActive: {
        type: Boolean,
        default: true,
        index: true
    },
    isCustom: {
        type: Boolean,
        default: false,
        index: true
    },
    clinicId: {
        type: Number,
        index: true
    },
    createdBy: {
        type: Number,
        required: true,
        index: true
    }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});

// Add auto-increment plugin
categoryServiceSchema.plugin(AutoIncrement, { inc_field: 'categoryServiceId', start_seq: 1001 });
categoryServiceSchema.index({ categoryServiceId: 1 }, { unique: true });

// Indexes for efficient queries
categoryServiceSchema.index({ appointmentCategoryId: 1, isActive: 1 });
categoryServiceSchema.index({ categoryServiceName: 'text', description: 'text' });
categoryServiceSchema.index({ clinicId: 1, isActive: 1, categoryServiceName: 1 });
categoryServiceSchema.index({ createdBy: 1, isCustom: 1, categoryServiceName: 1 });

// Virtual for created by staff data
categoryServiceSchema.virtual('createdByData', {
    ref: 'Staff',
    localField: 'createdBy',
    foreignField: 'staffId',
    justOne: true
});

// Static method to get services by appointment category
categoryServiceSchema.statics.getByAppointmentCategory = function(appointmentCategoryId, clinicId = null) {
    const query = { appointmentCategoryId, isActive: true };
    if (clinicId) {
        query.$or = [
            { clinicId: null }, // Global services
            { clinicId: clinicId } // Clinic-specific services
        ];
    }
    return this.find(query).sort({ categoryServiceName: 1 });
};

// Static method to search services
categoryServiceSchema.statics.searchServices = function(searchTerm, appointmentCategoryId = null, clinicId = null) {
    const query = {
        $text: { $search: searchTerm },
        isActive: true
    };

    if (appointmentCategoryId) query.appointmentCategoryId = appointmentCategoryId;
    if (clinicId) {
        query.$or = [
            { clinicId: null },
            { clinicId: clinicId }
        ];
    }

    return this.find(query, { score: { $meta: 'textScore' } })
               .sort({ score: { $meta: 'textScore' } });
};

const CategoryService = mongoose.model('CategoryService', categoryServiceSchema);

export default CategoryService;
