import mongoose from 'mongoose';
import AutoIncrementFactory from 'mongoose-sequence';

const AutoIncrement = AutoIncrementFactory(mongoose);

const speciesSchema = new mongoose.Schema({
    // Auto-increment ID
    speciesId: {
        type: Number,
        index: true,
        unique: true,
    },
    speciesName: {
        type: String,
        required: [true, "Species name is required"],
        trim: true
    },
    description: {
        type: String,
        trim: true
    },
    imageUrl: {
        type: String,
        trim: true
    },
    status: {
        type: Number,
        enum: [0, 1],
        default: 1
    }
}, {
    timestamps: true,
    collection: 'species'
});

// Index for better query performance
speciesSchema.index({ speciesName: 1 }, { unique: true });
speciesSchema.index({ status: 1 });

// Add auto-increment plugin
speciesSchema.plugin(AutoIncrement, { inc_field: 'speciesId', start_seq: 1001 });

const defaultSpecies = [
    { speciesName: 'Bird', description: 'Avian species' },
    { speciesName: 'Cat', description: 'Feline companion animals' },
    { speciesName: 'Chinchilla', description: 'Soft-furred rodents native to the Andes' },
    { speciesName: 'Dog', description: 'Canine companion animals' },
    { speciesName: 'Ferret', description: 'Domesticated carnivorous mammals' },
    { speciesName: 'Fish', description: 'Aquatic species' },
    { speciesName: 'Frog', description: 'Amphibians often kept in terrariums' },
    { speciesName: 'Guinea Pig', description: 'Cavies species' },
    { speciesName: 'Hamster', description: 'Small rodent pets' },
    { speciesName: 'Hedgehog', description: 'Small spiny mammals' },
    { speciesName: 'Horse', description: 'Large domesticated animals used for riding or work' },
    { speciesName: 'Lizard', description: 'Scaled reptilian pets including geckos and iguanas' },
    { speciesName: 'Mouse', description: 'Small rodent species often kept as pets' },
    { speciesName: 'Pig', description: 'Miniature pigs often kept as pets' },
    { speciesName: 'Rabbit', description: 'Lagomorph species' },
    { speciesName: 'Rat', description: 'Intelligent and social rodent pets' },
    { speciesName: 'Reptile', description: 'Cold-blooded species' },
    { speciesName: 'Snake', description: 'Legless reptilian pets' },
    { speciesName: 'Tortoise', description: 'Land-dwelling shelled reptiles' },
    { speciesName: 'Turtle', description: 'Shelled reptiles often kept as pets' }
];

speciesSchema.statics.initializeSpecies = async function() {
    try {
        const count = await this.countDocuments();
        if (count === 0) {
            for (const species of defaultSpecies) {
                await this.create(species);
            }
            console.log('Default species created successfully');
        }
    } catch (error) {
        console.error('Error initializing species:', error);
    }
};

const resetCollectionIfSchemaChanged = async () => {
    try {
        if (!mongoose.connection.db) {
            console.log("Waiting for database connection...");
            return;
        }

        const collectionName = 'species';
        const existingCollections = await mongoose.connection.db.listCollections().toArray();

        if (existingCollections.some(col => col.name === collectionName)) {
            await mongoose.connection.db.dropCollection(collectionName);
        }

        const Species = mongoose.model('Species', speciesSchema);
        await Species.init();
        console.log(`Recreated collection: ${collectionName}`);

        await Species.initializeSpecies();
    } catch (error) {
        console.error("Error resetting collection:", error);
    }
};

const Species = mongoose.model('Species', speciesSchema);

// mongoose.connection.once("open", resetCollectionIfSchemaChanged);

export default Species;
