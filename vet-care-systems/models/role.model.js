import mongoose from 'mongoose';
import AutoIncrementFactory from 'mongoose-sequence';

const AutoIncrement = AutoIncrementFactory(mongoose);

const roleSchema = new mongoose.Schema({
    // Auto-increment ID
    roleId: {
        type: Number,
        index: true,
        unique: true,
    },
    roleName: {
        type: String,
        required: [true, 'Role name is required'],
        trim: true,
        index: true,
    },
    // Add category field
    category: {
        type: String,
        enum: ['system', 'clinic', 'client'],
        required: [true, 'Role category is required'],
        trim: true,
        index: true,
    },
    description: {
        type: String,
        trim: true,
    },
    permissions: [{
        type: Number,
        ref: 'Permission',
        index: true,
    }],
    // Appointment types this role can access (empty = all types)
    allowedAppointmentTypes: [{
        type: Number,
        ref: 'AppointmentType',
        index: true
    }],
    status: {
        type: Number,
        enum: [0, 1],
        default: 1,
        index: true,
    }
}, {
    timestamps: true,
    collection: 'roles'
});

// Add auto-increment plugin
roleSchema.plugin(AutoIncrement, { inc_field: 'roleId', start_seq: 1001 });
roleSchema.index({ roleId: 1 }, { unique: true });
roleSchema.index({ roleName: 1 });
roleSchema.index({ category: 1 });

const defaultRoles = [
    {
        roleName: 'super_admin',
        category: 'system',
        description: 'System super administrator with all permissions',
        permissions: [1001] // all_access permission
    },
    {
        roleName: 'admin',
        category: 'system',
        description: 'System administrator',
        permissions: [1002, 1003, 1004, 1005, 1006, 1007, 1008] // All except all_access
    },
    {
        roleName: 'support',
        category: 'system',
        description: 'System support staff',
        permissions: [1009] // support permission
    },
    // Clinic roles
    // Add clinic_owner role
    {
        roleName: 'clinic_owner',
        category: 'clinic',
        description: 'Clinic owner',
        permissions: [1002, 1003, 1004, 1005, 1006, 1007, 1008] // All except all_access
    },
    {
        roleName: 'clinic_admin',
        category: 'clinic',
        description: 'Clinic administrator',
        permissions: [1002, 1003, 1004, 1005, 1006, 1007, 1008] // All except all_access
    },
    {
        roleName: 'veterinarian',
        category: 'clinic',
        description: 'Licensed veterinarian',
        permissions: [1004, 1005, 1006] // patient, medical, appointment
    },
    // Add veterinarian_assistant role
    {
        roleName: 'veterinarian_assistant',
        category: 'clinic',
        description: 'Veterinarian assistant',
        permissions: [1004, 1005] // patient, medical
    },
    // Add veterinary_technician role
    {
        roleName: 'veterinary_technician',
        category: 'clinic',
        description: 'Veterinary medical technician',
        permissions: [1004, 1005] // patient, medical
    },
    {
        roleName: 'laboratory_technician',
        category: 'clinic',
        description: 'Veterinary laboratory technician',
        permissions: [1004, 1005], // patient, medical
        allowedAppointmentTypes: [1004] // Only Laboratory appointment type
    },
    {
        roleName: 'boarding_staff',
        category: 'clinic',
        description: 'Pet boarding and care staff',
        permissions: [1004, 1006], // patient, appointment
        allowedAppointmentTypes: [1008] // Only Boarding appointment type
    },
    {
        roleName: 'grooming_staff',
        category: 'clinic',
        description: 'Pet grooming specialist',
        permissions: [1004, 1006], // patient, appointment
        allowedAppointmentTypes: [1005] // Only Grooming appointment type
    },
    {
        roleName: 'receptionist',
        category: 'clinic',
        description: 'Front desk receptionist',
        permissions: [1004, 1006] // patient, appointment
    },
    // driver
    {
        roleName: 'driver',
        category: 'clinic',
        description: 'Veterinary service driver',
        permissions: [1004, 1006] // patient, appointment
    },
    // Add client roles
    {
        roleName: 'client',
        category: 'client',
        description: 'Pet owner',
        permissions: [1004, 1006] // patient, appointment
    },
    {
        roleName: 'client_rep',
        category: 'client',
        description: 'Client representative',
        permissions: [1004, 1006] // patient, appointment
    }

];

roleSchema.statics.initializeRoles = async function() {
    try {
        const existingRoles = await this.find().lean();

        if (existingRoles.length > 0) {
            console.log('Existing roles found:');
            console.table(existingRoles.map(r => ({
                ID: r.roleId,
                Name: r.roleName,
                Permissions: r.permissions.length,
                Status: r.status
            })));
            return;
        }

        const inserted = await this.insertMany(defaultRoles);
        console.log('Default roles created successfully:');
        console.table(inserted.map(r => ({
            ID: r.roleId,
            Name: r.roleName,
            Permissions: r.permissions.length
        })));
    } catch (error) {
        console.error('Error initializing roles:', error);
    }
};

const Role = mongoose.model('Role', roleSchema);

export default Role;