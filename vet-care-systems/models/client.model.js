import mongoose from 'mongoose';
import AutoIncrementFactory from 'mongoose-sequence';

const AutoIncrement = AutoIncrementFactory(mongoose);

/**
 * Client Model
 *
 * This model represents a client (pet owner) in the system.
 * It is designed to support cross-clinic functionality while maintaining
 * proper data ownership and access controls.
 */
const clientSchema = new mongoose.Schema({
    // Auto-increment ID
    clientId: {
        type: Number,
        index: true,
        unique: true,
    },
    // Authentication fields
    email: {
        type: String,
        required: [true, "Email is required"],
        unique: true,
        lowercase: true,
        trim: true,
        match: [/\S+@\S+\.\S+/, 'Please enter a valid email address'],
    },
    password: {
        type: String,
        required: [true, "Password is required"],
        minLength: 6,
    },

    // Personal information
    firstName: {
        type: String,
        required: [true, "First name is required"],
        trim: true,
        minLength: 2,
        maxLength: 55,
    },
    middleName: {
        type: String,
        required: [false],
        trim: true,
        maxLength: 55,
    },
    lastName: {
        type: String,
        required: [true, "Last name is required"],
        trim: true,
        minLength: 2,
        maxLength: 55,
    },
    phoneNumber: {
        type: String,
        required: [true, "Phone number is required"],
        trim: true,
        min: 10,
        max: 14,
    },
    address: {
        type: String,
        required: false,
        trim: true,
    },
    dob: {
        type: Date,
        required: false,
    },

    // Location information
    homeLocation: {
        type: [Number],
        default: [0, 0]
    },
    area: {
        type: String,
        trim: true,
    },
    houseNumber: {
        type: String,
        trim: true,
    },
    buildingName: {
        type: String,
        trim: true,
    },

    // Client-specific fields
    preferredClinicId: {
        type: Number,
        ref: 'Clinic',
        default: null,
        index: true,
    },

    // Clinic relationships - for quick access to clinics this client has visited
    clinicRelationships: [{
        type: Number,
        ref: 'ClientClinicRelationship',
        index: true,
    }],

    // Registration information
    registrationInfo: {
        registeredAt: {
            type: Number,
            ref: 'Clinic'
        },
        registeredBy: {
            type: Number,
            ref: 'Staff'
        },
        registrationDate: {
            type: Date,
            default: Date.now
        }
    },

    // Data sharing preferences
    dataSharingPreferences: {
        shareAllClinics: {
            type: Boolean,
            default: true,
            description: "Allow all clinics to see client's basic information"
        },
        shareWithSpecificClinics: [{
            clinicId: {
                type: Number,
                ref: 'Clinic'
            },
            shareBasicInfo: {
                type: Boolean,
                default: true
            },
            sharePetInfo: {
                type: Boolean,
                default: true
            },
            shareContactInfo: {
                type: Boolean,
                default: true
            }
        }]
    },

    // Walk-in client flag
    isWalkIn: {
        type: Boolean,
        default: false,
        index: true, // Add index for efficient querying
        description: "Indicates if this client was initially registered as a walk-in"
    },

    // Status
    status: {
        type: Number,
        enum: [0, 1, 2, 3, 4], // 0: Inactive, 1: Active, 2: Dormant, 3: Suspended, 4: Blocked
        default: 1, // Default to Active
    },

    // Note: Login tracking fields removed as clients don't log in to the system
    // Only staff and admin users have login capabilities

    // Additional client information
    notes: {
        type: String,
        trim: true
    },
    referralSource: {
        type: String,
        trim: true
    },

    // Preferences
    communicationPreferences: {
        email: {
            type: Boolean,
            default: true
        },
        sms: {
            type: Boolean,
            default: true
        },
        phone: {
            type: Boolean,
            default: true
        }
    }
}, {
    timestamps: true,
    collection: 'clients'
});

// Indexes for better query performance
clientSchema.index({ email: 1 }, { unique: true });
clientSchema.index({ phoneNumber: 1 });
clientSchema.index({ firstName: 1, lastName: 1 });
clientSchema.index({ status: 1 });
clientSchema.index({ preferredClinicId: 1 });
clientSchema.index({ isWalkIn: 1 }); // Index for walk-in queries

// Add auto-increment plugin
clientSchema.plugin(AutoIncrement, { inc_field: 'clientId', start_seq: 1001 });
clientSchema.index({ clientId: 1 }, { unique: true });

// Post-save middleware to sync denormalized data
clientSchema.post('save', async function(doc) {
    try {
        // Import sync function dynamically to avoid circular dependencies
        const { syncClientDataToAppointments } = await import('../utils/denormalizationSync.js');

        // Sync client data to appointments if name, phone, or email changed
        if (this.isModified('firstName') || this.isModified('lastName') ||
            this.isModified('phoneNumber') || this.isModified('email')) {

            const updatedFields = {};
            if (this.isModified('firstName') || this.isModified('lastName')) {
                updatedFields.firstName = true;
                updatedFields.lastName = true;
            }
            if (this.isModified('phoneNumber')) updatedFields.phoneNumber = true;
            if (this.isModified('email')) updatedFields.email = true;

            await syncClientDataToAppointments(doc.clientId, updatedFields);
        }
    } catch (error) {
        console.error('Error syncing client data:', error);
    }
});

// Post-findOneAndUpdate middleware for updates via API
clientSchema.post('findOneAndUpdate', async function(doc) {
    try {
        if (doc && this.getUpdate()) {
            const { syncClientDataToAppointments } = await import('../utils/denormalizationSync.js');
            const updateFields = this.getUpdate().$set || {};

            if (updateFields.firstName || updateFields.lastName ||
                updateFields.phoneNumber || updateFields.email) {
                await syncClientDataToAppointments(doc.clientId, updateFields);
            }
        }
    } catch (error) {
        console.error('Error syncing client data on update:', error);
    }
});

const Client = mongoose.model('Client', clientSchema);

// Function to reset collection if schema changes
const resetCollectionIfSchemaChanged = async () => {
    try {
        if (!mongoose.connection.db) {
            console.log("Waiting for database connection...");
            return;
        }

        const collectionName = 'clients';
        const existingCollections = await mongoose.connection.db.listCollections().toArray();

        if (existingCollections.some(col => col.name === collectionName)) {
            await mongoose.connection.db.dropCollection(collectionName);
        }

        await Client.init();
        console.log(`Recreated collection: ${collectionName}`);
    } catch (error) {
        console.error("Error resetting collection:", error);
    }
};

// Run after Mongoose connects to DB
// mongoose.connection.once("open", resetCollectionIfSchemaChanged);

export default Client;
