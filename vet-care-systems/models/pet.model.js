import mongoose from 'mongoose';
import AutoIncrementFactory from 'mongoose-sequence';

const AutoIncrement = AutoIncrementFactory(mongoose);

const petSchema = new mongoose.Schema({
    // Auto-increment ID
    petId: {
        type: Number,
        index: true,
        unique: true,
    },
    // Core pet information
    owner: {
        type: Number,
        required: [true, "Owner is required"],
        index: true,
    },
    clientId: {
        type: Number,
        required: [true, "Client is required"],
        index: true,
    },
    petName: {
        type: String,
        required: [true, "Pet name is required"],
        trim: true,
        minLength: 1,
    },
    name: {
        type: String,
        required: [true, "Pet name is required"],
        trim: true,
        minLength: 1,
    },
    speciesId: {
        type: Number,
        required: [true, "Pet species is required"],
        index: true,
    },
    breedId: {
        type: Number,
        required: [true, "Breed is required"],
        index: true,
    },
    microchipId: {
        type: String,
        required: false, // Make optional for testing
        unique: true,
        trim: true,
        sparse: true, // Allow multiple null values
    },
    color: {
        type: String,
        required: [true, "Pet color is required"],
        trim: true,
    },
    lifeStatus: {
        type: String,
        enum: ["alive", "deceased"],
        required: true,
    },
    gender: {
        type: String,
        enum: ["male", "female"],
        required: true,
    },
    dateOfBirth: {
        type: Date,
        required: [true, "Date of birth is required"],
    },
    weight: {
        type: Number,
        required: true,
        min: 0,
    },
    petStatus: {
        type: Number,
        enum: [0, 1, 2, 3, 4], // 0: Inactive, 1: Active, 2: Dormant, 3: Suspended, 4: Blocked
        default: 1, // Default to Active
    },

    // Clinic relationships - for quick access to clinics this pet has visited
    clinicRelationships: [{
        type: Number,
        ref: 'PetClinicRelationship'
    }],

    // Registration information
    registrationInfo: {
        registeredAt: {
            type: Number,
            ref: 'Clinic'
        },
        registeredBy: {
            type: Number,
            ref: 'Staff'
        },
        registrationDate: {
            type: Date,
            default: Date.now
        }
    },

    // Medical information that follows the pet across all clinics
    allergies: [{
        allergen: {
            type: String,
            required: true
        },
        severity: {
            type: String,
            enum: ['mild', 'moderate', 'severe'],
            default: 'moderate'
        },
        symptoms: String,
        diagnosedBy: {
            type: Number,
            ref: 'Staff'
        },
        diagnosedAt: {
            type: Number,
            ref: 'Clinic'
        },
        diagnosisDate: Date,
        notes: String
    }],

    // Chronic conditions that follow the pet across all clinics
    chronicConditions: [{
        condition: {
            type: String,
            required: true
        },
        diagnosedBy: {
            type: Number,
            ref: 'Staff'
        },
        diagnosedAt: {
            type: Number,
            ref: 'Clinic'
        },
        diagnosisDate: Date,
        status: {
            type: String,
            enum: ['active', 'managed', 'resolved'],
            default: 'active'
        },
        notes: String
    }],

    // Data sharing preferences
    dataSharingPreferences: {
        shareAllClinics: {
            type: Boolean,
            default: true,
            description: "Allow all clinics to see pet's medical history"
        },
        shareWithSpecificClinics: [{
            clinicId: {
                type: Number
            },
            shareBasicInfo: {
                type: Boolean,
                default: true
            },
            shareMedicalHistory: {
                type: Boolean,
                default: true
            }
        }]
    }
}, {timestamps: true});

// Pre-save middleware to ensure both petName and name fields are populated
petSchema.pre('save', function(next) {
    // If petName is provided but name is not, copy petName to name
    if (this.petName && !this.name) {
        this.name = this.petName;
    }
    // If name is provided but petName is not, copy name to petName
    else if (this.name && !this.petName) {
        this.petName = this.name;
    }
    next();
});

// Add auto-increment plugin
petSchema.plugin(AutoIncrement, { inc_field: 'petId', start_seq: 1001 });
petSchema.index({ petId: 1 }, { unique: true });

const Pet = mongoose.model('Pet', petSchema);

// Function to reset collection if schema changes
const resetCollectionIfSchemaChanged = async () => {
    try {
        if (!mongoose.connection.db) {
            console.log("Waiting for database connection...");
            return;
        }

        const collectionName = Pet.collection.name;
        const existingCollections = await mongoose.connection.db.listCollections().toArray();
        const collectionExists = existingCollections.some(col => col.name === collectionName);

        if (collectionExists) {
            console.log(`Dropping collection: ${collectionName} due to schema changes...`);
            await mongoose.connection.db.dropCollection(collectionName);
        }

        // Ensure collection is created again
        await Pet.init();
        console.log(`Recreated collection: ${collectionName}`);
    } catch (error) {
        console.error("Error resetting collection:", error);
    }
};

// Run after Mongoose connects to DB
// mongoose.connection.once("open", resetCollectionIfSchemaChanged);

export default Pet;
