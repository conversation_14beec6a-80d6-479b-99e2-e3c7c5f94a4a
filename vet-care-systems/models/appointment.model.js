import mongoose from 'mongoose';
import AutoIncrementFactory from 'mongoose-sequence';

const AutoIncrement = AutoIncrementFactory(mongoose);

const newAppointmentSchema = new mongoose.Schema({
    // Auto-increment ID
    appointmentId: {
        type: Number,
        index: true,
        unique: true,
    },

    // Core appointment fields with denormalized data for performance
    petId: {
        type: Number,
        required: true,
        index: true,
    },
    petName: {
        type: String,
        required: true,
        trim: true,
        index: true, // For quick pet name searches
    },
    petSpecies: {
        type: String,
        required: true,
        trim: true,
        index: true, // For filtering by species
    },
    petBreed: {
        type: String,
        required: true,
        trim: true,
    },
    petGender: {
        type: String,
        enum: ['male', 'female'],
        required: true,
    },
    petAge: {
        type: Number, // Age in months for easier calculations
        required: true,
    },
    petWeight: {
        type: Number,
        required: true,
        min: 0,
    },

    clientId: {
        type: Number,
        required: true,
        index: true,
    },
    clientName: {
        type: String,
        required: true,
        trim: true,
        index: true, // For quick client name searches
    },
    clientPhone: {
        type: String,
        required: true,
        trim: true,
        index: true, // For quick client contact
    },
    clientEmail: {
        type: String,
        required: true,
        lowercase: true,
        trim: true,
    },

    clinicId: {
        type: Number,
        required: true,
        index: true,
    },
    clinicName: {
        type: String,
        required: true,
        trim: true,
    },

    // Main staff in charge of the appointment
    staffInCharge: {
        type: Number,
        required: true,
        ref: 'Staff',
        index: true,
    },
    staffInChargeName: {
        type: String,
        required: true,
        trim: true,
        index: true, // For quick staff searches
    },
    staffInChargeJobTitle: {
        type: String,
        required: true,
        trim: true,
    },

    // Appointment scheduling
    appointmentDate: {
        type: Date,
        required: true,
        index: true,
        default: Date.now // Default to now (current appointment)
    },
    estimatedDuration: {
        type: Number, // in minutes
        default: 30
    },
    actualDuration: {
        type: Number // in minutes
    },

    // Appointment status - all appointments start as scheduled until tasks are assigned
    status: {
        type: String,
        enum: ['scheduled', 'in_progress', 'completed', 'cancelled', 'no_show'],
        default: 'scheduled', // Default to scheduled until tasks are assigned
        index: true
    },

    // Priority
    priority: {
        type: String,
        enum: ['low', 'normal', 'high', 'emergency'],
        default: 'normal',
        index: true
    },

    // Service Categories - each category contains services performed during the appointment
    appointmentCategories: [{
        appointmentCategoryId: {
            type: Number,
            required: true,
            ref: 'AppointmentCategory'
        },
        categoryName: {
            type: String,
            required: true
        },

        // Staff assigned to handle this category (who will perform the services)
        staffAssigned: {
            type: Number,
            ref: 'Staff'
        },
        staffAssignedName: {
            type: String
        },

        // Category-level details
        estimatedDuration: {
            type: Number, // in minutes
            default: 30
        },
        priority: {
            type: String,
            enum: ['low', 'normal', 'high', 'urgent'],
            default: 'normal'
        },
        requiresEquipment: {
            type: Boolean,
            default: false
        },
        requiresQualification: {
            type: Boolean,
            default: false
        },

        // Services within this category
        categoryServices: [{
            categoryServiceId: {
                type: Number,
                required: true,
                ref: 'CategoryService'
            },
            categoryServiceName: {
                type: String,
                required: true
            },
            price: {
                type: Number,
                required: true,
                min: 0
            },
            currency: {
                type: String,
                default: 'KES',
                enum: ['KES', 'USD', 'EUR', 'GBP']
            },

            // Service-specific details
            status: {
                type: String,
                enum: ['pending', 'in_progress', 'completed', 'cancelled'],
                default: 'pending'
            },

            // Service notes
            notes: {
                type: String,
                trim: true,
                maxLength: 2000
            },

            // Medications used in this service
            medicationsUsed: [{
                inventoryItemId: {
                    type: Number,
                    ref: 'Inventory',
                    required: true
                },
                medicationName: {
                    type: String,
                    required: true
                },
                quantity: {
                    type: Number,
                    required: true,
                    min: 0
                },
                unit: {
                    type: String,
                    required: true
                },
                dosage: String,
                frequency: String,
                duration: String,
                instructions: String,
                batchNumber: String,
                expiryDate: Date,
                unitCost: {
                    type: Number,
                    min: 0
                },
                totalCost: {
                    type: Number,
                    min: 0
                }
            }],

            // Timing
            startTime: Date,
            endTime: Date,
            duration: Number, // in minutes
        }],

        // Category-level tracking
        categoryStatus: {
            type: String,
            enum: ['not_started', 'in_progress', 'completed'],
            default: 'not_started'
        },

        // Category completion tracking
        isCompleted: {
            type: Boolean,
            default: false
        },
        completedAt: Date,

        // Category notes
        categoryNotes: {
            type: String,
            trim: true,
            maxLength: 1000
        }
    }],
    
    // General appointment notes and recommendations
    generalNotes: {
        type: String,
        trim: true,
        maxLength: 5000
    },

    recommendations: {
        type: String,
        trim: true,
        maxLength: 3000
    },

    // Overall completion tracking
    completionStatus: {
        type: String,
        enum: ['not_started', 'in_progress', 'completed', 'cancelled'],
        default: 'not_started'
    },

    // Billing summary
    billing: {
        totalAmount: {
            type: Number,
            min: 0,
            default: 0
        },
        currency: {
            type: String,
            default: 'KES',
            enum: ['KES', 'USD', 'EUR', 'GBP']
        },
        paymentStatus: {
            type: String,
            enum: ['pending', 'partially_paid', 'paid', 'refunded'],
            default: 'pending'
        }
    },

    // Follow-up appointment tracking
    followUp: {
        // Is this appointment a follow-up?
        isFollowUp: {
            type: Boolean,
            default: false,
            index: true
        },

        // Parent appointment ID (if this is a follow-up)
        parentAppointmentId: {
            type: Number,
            ref: 'Appointment',
            index: true
        },

        // Parent appointment date for reference
        parentAppointmentDate: {
            type: Date
        },

        // Reason for follow-up
        followUpReason: {
            type: String,
            trim: true,
            maxLength: 500
        },

        // Type of follow-up
        followUpType: {
            type: String,
            enum: [
                'routine_checkup',
                'medication_review',
                'surgery_followup',
                'vaccination_reminder',
                'treatment_progress',
                'lab_results',
                'behavioral_assessment',
                'dental_checkup',
                'emergency_followup',
                'other'
            ],
            default: 'routine_checkup'
        },

        // Does this appointment have scheduled follow-ups?
        hasScheduledFollowUps: {
            type: Boolean,
            default: false,
            index: true
        },

        // Next follow-up date (reminder)
        nextFollowUpDate: {
            type: Date,
            index: true
        },

        // Follow-up instructions
        followUpInstructions: {
            type: String,
            trim: true,
            maxLength: 1000
        },

        // Follow-up priority
        followUpPriority: {
            type: String,
            enum: ['low', 'normal', 'high', 'urgent'],
            default: 'normal'
        },

        // Child appointments (follow-ups created from this appointment)
        childAppointments: [{
            appointmentId: {
                type: Number,
                ref: 'Appointment'
            },
            scheduledDate: {
                type: Date
            },
            status: {
                type: String,
                enum: ['scheduled', 'completed', 'cancelled'],
                default: 'scheduled'
            },
            followUpType: {
                type: String,
                enum: [
                    'routine_checkup',
                    'medication_review',
                    'surgery_followup',
                    'vaccination_reminder',
                    'treatment_progress',
                    'lab_results',
                    'behavioral_assessment',
                    'dental_checkup',
                    'emergency_followup',
                    'other'
                ],
                default: 'routine_checkup'
            }
        }]
    },

    // Tracking
    createdBy: {
        type: Number,
        required: true,
        index: true
    },
    updatedBy: {
        type: Number,
        index: true
    }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});

// Add auto-increment plugin
newAppointmentSchema.plugin(AutoIncrement, { inc_field: 'appointmentId', id: 'new_appointment_id', start_seq: 3001 });
newAppointmentSchema.index({ appointmentId: 1 }, { unique: true });

// Indexes for efficient queries - including denormalized fields
newAppointmentSchema.index({ petId: 1, appointmentDate: -1 });
newAppointmentSchema.index({ clientId: 1, appointmentDate: -1 });
newAppointmentSchema.index({ clinicId: 1, appointmentDate: -1 });
newAppointmentSchema.index({ staffInCharge: 1, appointmentDate: -1 });
newAppointmentSchema.index({ status: 1, appointmentDate: 1 });
newAppointmentSchema.index({ priority: 1, status: 1 });
newAppointmentSchema.index({ completionStatus: 1 });

// Denormalized field indexes for fast searches and filtering
newAppointmentSchema.index({ petName: 'text', clientName: 'text' }); // Text search
newAppointmentSchema.index({ clientPhone: 1 }); // Quick client lookup by phone
newAppointmentSchema.index({ petSpecies: 1, appointmentDate: -1 }); // Filter by species
newAppointmentSchema.index({ staffInChargeName: 1, appointmentDate: -1 }); // Staff workload
newAppointmentSchema.index({ clinicId: 1, petSpecies: 1 }); // Clinic-specific species stats
newAppointmentSchema.index({ clinicId: 1, status: 1, appointmentDate: -1 }); // Clinic dashboard

// Follow-up appointment indexes
newAppointmentSchema.index({ 'followUp.isFollowUp': 1, appointmentDate: -1 }); // Find follow-up appointments
newAppointmentSchema.index({ 'followUp.parentAppointmentId': 1 }); // Find children of parent appointment
newAppointmentSchema.index({ 'followUp.hasScheduledFollowUps': 1, 'followUp.nextFollowUpDate': 1 }); // Follow-up reminders
newAppointmentSchema.index({ petId: 1, 'followUp.isFollowUp': 1, appointmentDate: -1 }); // Pet follow-up history
newAppointmentSchema.index({ clinicId: 1, 'followUp.nextFollowUpDate': 1 }); // Clinic follow-up dashboard
newAppointmentSchema.index({ 'followUp.followUpType': 1, appointmentDate: -1 }); // Follow-up type analytics

// Virtual for total services count
newAppointmentSchema.virtual('totalServicesCount').get(function () {
    return this.appointmentCategories.reduce((total, category) => {
        return total + (category.categoryServices ? category.categoryServices.length : 0);
    }, 0);
});

// Virtual for completed services count
newAppointmentSchema.virtual('completedServicesCount').get(function () {
    return this.appointmentCategories.reduce((total, category) => {
        return total + (category.categoryServices ? category.categoryServices.filter(s => s.isCompleted).length : 0);
    }, 0);
});

// Virtual for completion percentage
newAppointmentSchema.virtual('completionPercentage').get(function () {
    const total = this.totalServicesCount;
    const completed = this.completedServicesCount;
    return total > 0 ? Math.round((completed / total) * 100) : 0;
});

// Virtual for pet data population
newAppointmentSchema.virtual('petData', {
    ref: 'Pet',
    localField: 'petId',
    foreignField: 'petId',
    justOne: true
});

// Virtual for client data population
newAppointmentSchema.virtual('clientData', {
    ref: 'Client',
    localField: 'clientId',
    foreignField: 'clientId',
    justOne: true
});

// Virtual for staff data population
newAppointmentSchema.virtual('staffData', {
    ref: 'Staff',
    localField: 'staffInCharge',
    foreignField: 'staffId',
    justOne: true
});

// Virtual for parent appointment data (if this is a follow-up)
newAppointmentSchema.virtual('parentAppointment', {
    ref: 'Appointment',
    localField: 'followUp.parentAppointmentId',
    foreignField: 'appointmentId',
    justOne: true
});

// Virtual for child appointments (follow-ups created from this appointment)
newAppointmentSchema.virtual('childAppointments', {
    ref: 'Appointment',
    localField: 'appointmentId',
    foreignField: 'followUp.parentAppointmentId'
});

// Virtual to get active follow-up count
newAppointmentSchema.virtual('activeFollowUpCount').get(function () {
    return this.followUp?.childAppointments?.filter(child =>
        child.status === 'scheduled'
    ).length || 0;
});

// Virtual to check if follow-up is overdue
newAppointmentSchema.virtual('isFollowUpOverdue').get(function () {
    if (!this.followUp?.nextFollowUpDate) return false;
    return new Date() > new Date(this.followUp.nextFollowUpDate);
});

// Pre-save middleware to update completion status and calculate denormalized fields
newAppointmentSchema.pre('save', async function (next) {
    // Calculate pet age from date of birth if petAge is not set
    if (this.isNew && !this.petAge && this.petId) {
        try {
            const Pet = mongoose.model('Pet');
            const pet = await Pet.findOne({ petId: this.petId });
            if (pet && pet.dateOfBirth) {
                const ageInMonths = Math.floor((new Date() - new Date(pet.dateOfBirth)) / (1000 * 60 * 60 * 24 * 30.44));
                this.petAge = ageInMonths;
            }
        } catch (error) {
            console.warn('Could not calculate pet age:', error.message);
        }
    }

    // Handle inventory deduction for medications when services are completed
    if (this.isModified('appointmentCategories')) {
        const Inventory = mongoose.model('Inventory');

        // Process each category
        for (const category of this.appointmentCategories) {
            // Process each service in the category
            for (const service of category.categoryServices) {
                // Check if service was just completed and has medications
                if (service.isCompleted && service.medicationsUsed && service.medicationsUsed.length > 0) {
                    // Process each medication used
                    for (const medication of service.medicationsUsed) {
                        try {
                            // Find the inventory item
                            const inventoryItem = await Inventory.findOne({
                                inventoryItemId: medication.inventoryItemId,
                                clinicId: this.clinicId
                            });

                            if (inventoryItem && inventoryItem.quantity >= medication.quantity) {
                                // Deduct from inventory
                                inventoryItem.quantity -= medication.quantity;
                                inventoryItem.lastUsedDate = new Date();

                                // Add transaction record
                                inventoryItem.transactions.push({
                                    type: 'sale',
                                    quantity: -medication.quantity,
                                    date: new Date(),
                                    performedBy: this.updatedBy || this.createdBy,
                                    notes: `Used in appointment ${this.appointmentId} - ${service.categoryServiceName}`,
                                    relatedRecord: {
                                        recordType: 'appointment',
                                        recordId: this.appointmentId
                                    }
                                });

                                await inventoryItem.save();
                            }
                        } catch (error) {
                            console.error(`Error updating inventory for medication ${medication.inventoryItemId}:`, error);
                        }
                    }
                }
            }
        }
    }

    // Update category completion status
    this.appointmentCategories.forEach(category => {
        const totalServices = category.categoryServices ? category.categoryServices.length : 0;
        const completedServices = category.categoryServices ? category.categoryServices.filter(s => s.isCompleted).length : 0;

        if (completedServices === 0) {
            category.categoryStatus = 'not_started';
            category.isCompleted = false;
        } else if (completedServices === totalServices) {
            category.categoryStatus = 'completed';
            category.isCompleted = true;
            if (!category.completedAt) {
                category.completedAt = new Date();
            }
        } else {
            category.categoryStatus = 'in_progress';
            category.isCompleted = false;
        }
    });

    // Update overall completion status
    const totalCategories = this.appointmentCategories.length;
    const completedCategories = this.appointmentCategories.filter(c => c.isCompleted).length;

    if (completedCategories === 0) {
        this.completionStatus = 'not_started';
    } else if (completedCategories === totalCategories) {
        this.completionStatus = 'completed';
        this.status = 'completed';
    } else {
        this.completionStatus = 'in_progress';
        this.status = 'in_progress';
    }

    // Calculate total billing amount
    this.billing.totalAmount = this.appointmentCategories.reduce((total, category) => {
        return total + (category.categoryServices ? category.categoryServices.reduce((catTotal, service) => {
            return catTotal + (service.price || 0);
        }, 0) : 0);
    }, 0);

    next();
});

const Appointment = mongoose.model('Appointment', newAppointmentSchema);

export default Appointment;
