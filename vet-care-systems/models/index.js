// Import all models to ensure they are registered with Mongoose
import './user.model.js';
import './staff.model.js';
import './clinic.model.js';
import './client.model.js';
import './pet.model.js';
import './species.model.js';
import './breed.model.js';
import './appointment.model.js';
import './appointmentCategory.model.js';
import './clinicCategorySettings.model.js';
import './categoryService.model.js';
import './role.model.js';
import './permission.model.js';
import './healthRecord.model.js';
// Temporarily disabled - not actively used in current implementation
// import './clientClinicRelationship.model.js';
// import './petClinicRelationship.model.js';
// import './aiSuggestion.model.js';
import './inventory.model.js';
import './medicationDispensing.model.js';
import './invoice.model.js';
import './payment.model.js';
import './receipt.model.js';
import './discount.model.js';

console.log('All models loaded successfully');
