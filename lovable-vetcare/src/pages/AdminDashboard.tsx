/**
 * Admin Dashboard Component
 * 
 * Provides system administrators with comprehensive oversight of all clinics,
 * metrics, and system-wide management capabilities.
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Building2,
  Users,
  Activity,
  DollarSign,
  TrendingUp,
  AlertTriangle,
  Calendar,
  FileText,
  Shield,
  Search,
  Filter,
  Download,
  RefreshCw
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { AdminOnly } from '@/components/auth/PermissionGuard';
import { usePermissions } from '@/hooks/usePermissions';
import { useAuth } from '@/store';
import { ClinicSelectorModal } from '@/components/clinic/ClinicSelectorModal';

interface DashboardMetrics {
  totalClinics: number;
  totalStaff: number;
  totalClients: number;
  totalRevenue: number;
  activeAppointments: number;
  systemAlerts: number;
}

interface ClinicOverview {
  clinicId: number;
  clinicName: string;
  status: 'active' | 'inactive' | 'suspended';
  staffCount: number;
  clientCount: number;
  monthlyRevenue: number;
  lastActivity: string;
  owner: string;
}

const AdminDashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<DashboardMetrics>({
    totalClinics: 0,
    totalStaff: 0,
    totalClients: 0,
    totalRevenue: 0,
    activeAppointments: 0,
    systemAlerts: 0
  });
  
  const [clinics, setClinics] = useState<ClinicOverview[]>([]);
  const [selectedClinicId, setSelectedClinicId] = useState<number | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [clinicModalOpen, setClinicModalOpen] = useState(false);
  
  const { isSystemAdmin } = usePermissions();
  const { clinic: currentClinic } = useAuth();

  // Mock data - replace with actual API calls
  useEffect(() => {
    const fetchDashboardData = async () => {
      setLoading(true);
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        setMetrics({
          totalClinics: 12,
          totalStaff: 156,
          totalClients: 2847,
          totalRevenue: 485000,
          activeAppointments: 89,
          systemAlerts: 3
        });

        setClinics([
          {
            clinicId: 1001,
            clinicName: "Downtown Veterinary Clinic",
            status: 'active',
            staffCount: 15,
            clientCount: 450,
            monthlyRevenue: 85000,
            lastActivity: "2 hours ago",
            owner: "Dr. Sarah Johnson"
          },
          {
            clinicId: 1002,
            clinicName: "Pet Care Plus",
            status: 'active',
            staffCount: 8,
            clientCount: 320,
            monthlyRevenue: 62000,
            lastActivity: "1 day ago",
            owner: "Dr. Michael Chen"
          },
          {
            clinicId: 1003,
            clinicName: "Animal Hospital North",
            status: 'inactive',
            staffCount: 12,
            clientCount: 280,
            monthlyRevenue: 45000,
            lastActivity: "1 week ago",
            owner: "Dr. Emily Rodriguez"
          }
        ]);
      } catch (error) {
        console.error('Failed to fetch dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [selectedClinicId]);

  const filteredClinics = clinics.filter(clinic =>
    clinic.clinicName.toLowerCase().includes(searchQuery.toLowerCase()) ||
    clinic.owner.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'inactive': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'suspended': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const handleClinicSelect = (clinicId: number) => {
    setSelectedClinicId(clinicId);
    setClinicModalOpen(true);
  };

  if (!isSystemAdmin()) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Card className="w-96">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-red-500" />
              Access Denied
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 dark:text-gray-400">
              You don't have permission to access the admin dashboard.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <AdminOnly>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-7xl mx-auto space-y-6">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4"
          >
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
                Admin Dashboard
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                System-wide overview and management
              </p>
            </div>
            
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                onClick={() => setClinicModalOpen(true)}
                className="flex items-center gap-2"
              >
                <Building2 className="h-4 w-4" />
                Select Clinic Context
              </Button>
              
              <Button
                variant="outline"
                onClick={() => window.location.reload()}
                className="flex items-center gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                Refresh
              </Button>
            </div>
          </motion.div>

          {/* Current Context */}
          {currentClinic && (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800"
            >
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 dark:bg-blue-800 rounded-lg">
                  <Building2 className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h3 className="font-medium text-blue-900 dark:text-blue-100">
                    Current Context: {currentClinic.clinicName}
                  </h3>
                  <p className="text-sm text-blue-700 dark:text-blue-300">
                    Viewing data for this clinic
                  </p>
                </div>
              </div>
            </motion.div>
          )}

          {/* Metrics Cards */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4"
          >
            {[
              { title: 'Total Clinics', value: metrics.totalClinics, icon: Building2, color: 'text-blue-600' },
              { title: 'Total Staff', value: metrics.totalStaff, icon: Users, color: 'text-green-600' },
              { title: 'Total Clients', value: metrics.totalClients, icon: Activity, color: 'text-purple-600' },
              { title: 'Revenue', value: `$${metrics.totalRevenue.toLocaleString()}`, icon: DollarSign, color: 'text-yellow-600' },
              { title: 'Active Appointments', value: metrics.activeAppointments, icon: Calendar, color: 'text-indigo-600' },
              { title: 'System Alerts', value: metrics.systemAlerts, icon: AlertTriangle, color: 'text-red-600' }
            ].map((metric, index) => (
              <Card key={metric.title}>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">{metric.title}</p>
                      <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                        {metric.value}
                      </p>
                    </div>
                    <metric.icon className={`h-8 w-8 ${metric.color}`} />
                  </div>
                </CardContent>
              </Card>
            ))}
          </motion.div>

          {/* Main Content */}
          <Tabs defaultValue="clinics" className="space-y-6">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="clinics">Clinics</TabsTrigger>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
              <TabsTrigger value="system">System</TabsTrigger>
              <TabsTrigger value="reports">Reports</TabsTrigger>
            </TabsList>

            <TabsContent value="clinics" className="space-y-4">
              {/* Search and Filters */}
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search clinics by name or owner..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <Button variant="outline" className="flex items-center gap-2">
                  <Filter className="h-4 w-4" />
                  Filters
                </Button>
                <Button variant="outline" className="flex items-center gap-2">
                  <Download className="h-4 w-4" />
                  Export
                </Button>
              </div>

              {/* Clinics List */}
              <div className="grid gap-4">
                {filteredClinics.map((clinic) => (
                  <Card key={clinic.clinicId} className="hover:shadow-md transition-shadow">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <div className="p-3 bg-blue-100 dark:bg-blue-900 rounded-lg">
                            <Building2 className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-900 dark:text-gray-100">
                              {clinic.clinicName}
                            </h3>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                              Owner: {clinic.owner}
                            </p>
                            <div className="flex items-center gap-4 mt-2 text-sm text-gray-500">
                              <span>{clinic.staffCount} staff</span>
                              <span>{clinic.clientCount} clients</span>
                              <span>${clinic.monthlyRevenue.toLocaleString()}/month</span>
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-3">
                          <Badge className={getStatusColor(clinic.status)}>
                            {clinic.status}
                          </Badge>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleClinicSelect(clinic.clinicId)}
                          >
                            View Details
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="analytics" className="space-y-6">
              {/* Metrics Overview */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-gray-600 dark:text-gray-400">Total Revenue</p>
                        <p className="text-2xl font-bold text-green-600">$485,000</p>
                        <p className="text-xs text-green-500">+12% from last month</p>
                      </div>
                      <DollarSign className="h-8 w-8 text-green-600" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-gray-600 dark:text-gray-400">Active Appointments</p>
                        <p className="text-2xl font-bold text-blue-600">89</p>
                        <p className="text-xs text-blue-500">+5% from yesterday</p>
                      </div>
                      <Calendar className="h-8 w-8 text-blue-600" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-gray-600 dark:text-gray-400">New Clients</p>
                        <p className="text-2xl font-bold text-purple-600">24</p>
                        <p className="text-xs text-purple-500">+8% this week</p>
                      </div>
                      <Users className="h-8 w-8 text-purple-600" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-gray-600 dark:text-gray-400">System Health</p>
                        <p className="text-2xl font-bold text-green-600">98.5%</p>
                        <p className="text-xs text-green-500">All systems operational</p>
                      </div>
                      <Activity className="h-8 w-8 text-green-600" />
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Charts and Analytics */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Revenue Trends</CardTitle>
                    <CardDescription>Monthly revenue across all clinics</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <p className="text-gray-500">Revenue chart placeholder</p>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Clinic Performance</CardTitle>
                    <CardDescription>Top performing clinics this month</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {[
                        { name: 'Downtown Veterinary Clinic', revenue: 85000, growth: 15 },
                        { name: 'Pet Care Plus', revenue: 62000, growth: 8 },
                        { name: 'Animal Hospital North', revenue: 45000, growth: -2 }
                      ].map((clinic, index) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                          <div>
                            <p className="font-medium">{clinic.name}</p>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                              ${clinic.revenue.toLocaleString()} revenue
                            </p>
                          </div>
                          <Badge className={clinic.growth >= 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                            {clinic.growth >= 0 ? '+' : ''}{clinic.growth}%
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="system" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {/* System Status */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Activity className="h-5 w-5" />
                      System Status
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Database</span>
                      <Badge className="bg-green-100 text-green-800">Online</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">API Server</span>
                      <Badge className="bg-green-100 text-green-800">Healthy</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Cache Service</span>
                      <Badge className="bg-green-100 text-green-800">Active</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">File Storage</span>
                      <Badge className="bg-yellow-100 text-yellow-800">Warning</Badge>
                    </div>
                  </CardContent>
                </Card>

                {/* Quick Actions */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Settings className="h-5 w-5" />
                      Quick Actions
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <Button variant="outline" className="w-full justify-start">
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Refresh System Cache
                    </Button>
                    <Button variant="outline" className="w-full justify-start">
                      <Download className="h-4 w-4 mr-2" />
                      Export System Logs
                    </Button>
                    <Button variant="outline" className="w-full justify-start">
                      <Shield className="h-4 w-4 mr-2" />
                      Security Audit
                    </Button>
                    <Button variant="outline" className="w-full justify-start">
                      <FileText className="h-4 w-4 mr-2" />
                      Generate Report
                    </Button>
                  </CardContent>
                </Card>

                {/* System Alerts */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <AlertTriangle className="h-5 w-5" />
                      System Alerts
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
                      <p className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                        Storage Warning
                      </p>
                      <p className="text-xs text-yellow-700 dark:text-yellow-300">
                        Disk usage at 85%
                      </p>
                    </div>
                    <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                      <p className="text-sm font-medium text-blue-800 dark:text-blue-200">
                        Update Available
                      </p>
                      <p className="text-xs text-blue-700 dark:text-blue-300">
                        System update v2.1.0
                      </p>
                    </div>
                    <div className="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                      <p className="text-sm font-medium text-green-800 dark:text-green-200">
                        Backup Complete
                      </p>
                      <p className="text-xs text-green-700 dark:text-green-300">
                        Daily backup successful
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="reports">
              <Card>
                <CardHeader>
                  <CardTitle>System Reports</CardTitle>
                  <CardDescription>
                    Generate and download system-wide reports
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 dark:text-gray-400">
                    Reporting system coming soon...
                  </p>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Clinic Selector Modal */}
        <ClinicSelectorModal 
          open={clinicModalOpen} 
          onOpenChange={setClinicModalOpen} 
        />
      </div>
    </AdminOnly>
  );
};

export default AdminDashboard;
