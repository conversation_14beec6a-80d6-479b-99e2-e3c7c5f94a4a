import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Plus, Edit2, Trash2, DollarSign, Percent, Settings, Check, X, Search } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/components/ui/use-toast';
import { api } from '@/services/api';

interface ClinicCategory {
  appointmentCategoryId: number;
  name: string;
  description: string;
  defaultCharge: number;
  defaultDiscountPercentage: number;
  estimatedDuration: number;
  isActive: boolean;
  clinicCustomization?: {
    clinicAppointmentCategoryId: number;
    customName?: string;
    customDescription?: string;
    clinicCharge?: number;
    clinicDiscountPercentage?: number;
    customDuration?: number;
    displayOrder?: number;
    isActive: boolean;
  };
  effectiveValues: {
    name: string;
    description: string;
    charge: number;
    discountPercentage: number;
    duration: number;
    currency: string;
  };
}

const ClinicCategoriesManagement: React.FC = () => {
  const { clinicId } = useParams<{ clinicId: string }>();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<ClinicCategory | null>(null);
  const [selectedCategoryIds, setSelectedCategoryIds] = useState<number[]>([]);

  // Fetch clinic categories
  const { data: clinicCategoriesResponse, isLoading } = useQuery({
    queryKey: ['clinicCategories', clinicId],
    queryFn: async () => {
      const response = await api.get(`/clinic-categories/clinic/${clinicId}`);
      return response.data;
    },
    enabled: !!clinicId
  });

  // Fetch all global categories for adding
  const { data: globalCategoriesResponse } = useQuery({
    queryKey: ['globalCategories'],
    queryFn: async () => {
      const response = await api.get('/categories?isActive=true');
      return response.data;
    }
  });

  const clinicCategories: ClinicCategory[] = clinicCategoriesResponse?.data || [];
  const globalCategories = globalCategoriesResponse?.data || [];

  // Add category to clinic mutation
  const addCategoryMutation = useMutation({
    mutationFn: async (categoryData: any) => {
      const response = await api.post(`/clinic-categories/clinic/${clinicId}`, categoryData);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['clinicCategories', clinicId] });
      toast({
        title: "Success",
        description: "Category added to clinic successfully",
      });
      setShowAddModal(false);
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to add category",
        variant: "destructive"
      });
    }
  });

  // Bulk add categories mutation
  const bulkAddMutation = useMutation({
    mutationFn: async (categoryIds: number[]) => {
      const response = await api.post(`/clinic-categories/clinic/${clinicId}/bulk`, {
        appointmentCategoryIds: categoryIds
      });
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['clinicCategories', clinicId] });
      toast({
        title: "Success",
        description: "Categories added to clinic successfully",
      });
      setSelectedCategoryIds([]);
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to add categories",
        variant: "destructive"
      });
    }
  });

  // Update category mutation
  const updateCategoryMutation = useMutation({
    mutationFn: async ({ clinicAppointmentCategoryId, data }: { clinicAppointmentCategoryId: number, data: any }) => {
      const response = await api.put(`/clinic-categories/clinic/${clinicId}/${clinicAppointmentCategoryId}`, data);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['clinicCategories', clinicId] });
      toast({
        title: "Success",
        description: "Category updated successfully",
      });
      setShowEditModal(false);
      setSelectedCategory(null);
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to update category",
        variant: "destructive"
      });
    }
  });

  // Remove category mutation
  const removeCategoryMutation = useMutation({
    mutationFn: async (clinicAppointmentCategoryId: number) => {
      const response = await api.delete(`/clinic-categories/clinic/${clinicId}/${clinicAppointmentCategoryId}`);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['clinicCategories', clinicId] });
      toast({
        title: "Success",
        description: "Category removed from clinic successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to remove category",
        variant: "destructive"
      });
    }
  });

  const filteredCategories = clinicCategories.filter(category =>
    category.effectiveValues.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    category.effectiveValues.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const availableGlobalCategories = globalCategories.filter((gc: any) =>
    !clinicCategories.some(cc => cc.appointmentCategoryId === gc.appointmentCategoryId)
  );

  const handleBulkAdd = () => {
    if (selectedCategoryIds.length === 0) {
      toast({
        title: "No Selection",
        description: "Please select at least one category to add",
        variant: "destructive"
      });
      return;
    }
    bulkAddMutation.mutate(selectedCategoryIds);
  };

  if (isLoading) {
    return <div className="flex justify-center items-center h-64">Loading...</div>;
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Clinic Categories Management</h1>
          <p className="text-gray-600">Manage appointment categories and services for this clinic</p>
        </div>
        <div className="flex gap-2">
          <Dialog open={showAddModal} onOpenChange={setShowAddModal}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Add Categories
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Add Categories to Clinic</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <p className="text-sm text-gray-600">
                    Select categories to add to your clinic. You can customize them later.
                  </p>
                  <Button
                    onClick={handleBulkAdd}
                    disabled={selectedCategoryIds.length === 0 || bulkAddMutation.isPending}
                  >
                    Add Selected ({selectedCategoryIds.length})
                  </Button>
                </div>
                <div className="grid gap-3 max-h-96 overflow-y-auto">
                  {availableGlobalCategories.map((category: any) => (
                    <div key={category.appointmentCategoryId} className="flex items-center space-x-3 p-3 border rounded-lg">
                      <Checkbox
                        checked={selectedCategoryIds.includes(category.appointmentCategoryId)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setSelectedCategoryIds([...selectedCategoryIds, category.appointmentCategoryId]);
                          } else {
                            setSelectedCategoryIds(selectedCategoryIds.filter(id => id !== category.appointmentCategoryId));
                          }
                        }}
                      />
                      <div className="flex-1">
                        <h4 className="font-medium">{category.name}</h4>
                        <p className="text-sm text-gray-600">{category.description}</p>
                        <div className="flex items-center gap-4 mt-1">
                          <span className="text-xs text-green-600">
                            KES {category.defaultCharge?.toLocaleString() || 0}
                          </span>
                          <span className="text-xs text-gray-500">
                            {category.estimatedDuration || 30} min
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Search */}
      <div className="flex items-center gap-4">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search categories..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Badge variant="outline">
          {filteredCategories.length} categories
        </Badge>
      </div>

      {/* Categories Grid */}
      <div className="grid gap-4">
        {filteredCategories.map((category) => (
          <Card key={category.appointmentCategoryId} className="hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h3 className="text-lg font-semibold text-gray-900">
                      {category.effectiveValues.name}
                    </h3>
                    {category.clinicCustomization && (
                      <Badge className="bg-blue-100 text-blue-800">Customized</Badge>
                    )}
                    <Badge
                      variant={category.clinicCustomization?.isActive !== false ? "default" : "secondary"}
                    >
                      {category.clinicCustomization?.isActive !== false ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                  <p className="text-gray-600 mb-3">{category.effectiveValues.description}</p>
                  
                  <div className="flex items-center gap-6 text-sm">
                    <div className="flex items-center gap-1">
                      <DollarSign className="h-4 w-4 text-green-600" />
                      <span className="font-medium">
                        {category.effectiveValues.currency} {category.effectiveValues.charge.toLocaleString()}
                      </span>
                      {category.effectiveValues.discountPercentage > 0 && (
                        <span className="text-orange-600">
                          ({category.effectiveValues.discountPercentage}% off)
                        </span>
                      )}
                    </div>
                    <div className="text-gray-500">
                      Duration: {category.effectiveValues.duration} min
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  {category.clinicCustomization && (
                    <>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          setSelectedCategory(category);
                          setShowEditModal(true);
                        }}
                      >
                        <Edit2 className="h-3 w-3" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          if (category.clinicCustomization?.clinicAppointmentCategoryId) {
                            removeCategoryMutation.mutate(category.clinicCustomization.clinicAppointmentCategoryId);
                          }
                        }}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </>
                  )}
                  {!category.clinicCustomization && (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => {
                        addCategoryMutation.mutate({
                          appointmentCategoryId: category.appointmentCategoryId
                        });
                      }}
                    >
                      <Plus className="h-3 w-3 mr-1" />
                      Customize
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredCategories.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500">No categories found. Add some categories to get started.</p>
        </div>
      )}

      {/* Edit Category Modal */}
      <Dialog open={showEditModal} onOpenChange={setShowEditModal}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Customize Category</DialogTitle>
          </DialogHeader>
          {selectedCategory && (
            <EditCategoryForm
              category={selectedCategory}
              onSave={(data) => {
                if (selectedCategory.clinicCustomization?.clinicAppointmentCategoryId) {
                  updateCategoryMutation.mutate({
                    clinicAppointmentCategoryId: selectedCategory.clinicCustomization.clinicAppointmentCategoryId,
                    data
                  });
                }
              }}
              onCancel={() => {
                setShowEditModal(false);
                setSelectedCategory(null);
              }}
              isLoading={updateCategoryMutation.isPending}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

// Edit Category Form Component
interface EditCategoryFormProps {
  category: ClinicCategory;
  onSave: (data: any) => void;
  onCancel: () => void;
  isLoading: boolean;
}

const EditCategoryForm: React.FC<EditCategoryFormProps> = ({
  category,
  onSave,
  onCancel,
  isLoading
}) => {
  const [formData, setFormData] = useState({
    customName: category.clinicCustomization?.customName || '',
    customDescription: category.clinicCustomization?.customDescription || '',
    clinicCharge: category.clinicCustomization?.clinicCharge || category.defaultCharge || 0,
    clinicDiscountPercentage: category.clinicCustomization?.clinicDiscountPercentage || category.defaultDiscountPercentage || 0,
    customDuration: category.clinicCustomization?.customDuration || category.estimatedDuration || 30,
    displayOrder: category.clinicCustomization?.displayOrder || 0,
    isActive: category.clinicCustomization?.isActive !== false,
    clinicNotes: ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="customName">Custom Name (optional)</Label>
          <Input
            id="customName"
            value={formData.customName}
            onChange={(e) => setFormData({ ...formData, customName: e.target.value })}
            placeholder={category.name}
          />
          <p className="text-xs text-gray-500 mt-1">Leave empty to use default: {category.name}</p>
        </div>
        <div>
          <Label htmlFor="displayOrder">Display Order</Label>
          <Input
            id="displayOrder"
            type="number"
            value={formData.displayOrder}
            onChange={(e) => setFormData({ ...formData, displayOrder: parseInt(e.target.value) || 0 })}
          />
        </div>
      </div>

      <div>
        <Label htmlFor="customDescription">Custom Description (optional)</Label>
        <Textarea
          id="customDescription"
          value={formData.customDescription}
          onChange={(e) => setFormData({ ...formData, customDescription: e.target.value })}
          placeholder={category.description}
          rows={3}
        />
      </div>

      <div className="grid grid-cols-3 gap-4">
        <div>
          <Label htmlFor="clinicCharge">Clinic Charge (KES)</Label>
          <Input
            id="clinicCharge"
            type="number"
            min="0"
            step="0.01"
            value={formData.clinicCharge}
            onChange={(e) => setFormData({ ...formData, clinicCharge: parseFloat(e.target.value) || 0 })}
          />
        </div>
        <div>
          <Label htmlFor="clinicDiscountPercentage">Discount (%)</Label>
          <Input
            id="clinicDiscountPercentage"
            type="number"
            min="0"
            max="100"
            step="0.1"
            value={formData.clinicDiscountPercentage}
            onChange={(e) => setFormData({ ...formData, clinicDiscountPercentage: parseFloat(e.target.value) || 0 })}
          />
        </div>
        <div>
          <Label htmlFor="customDuration">Duration (minutes)</Label>
          <Input
            id="customDuration"
            type="number"
            min="5"
            max="480"
            value={formData.customDuration}
            onChange={(e) => setFormData({ ...formData, customDuration: parseInt(e.target.value) || 30 })}
          />
        </div>
      </div>

      <div>
        <Label htmlFor="clinicNotes">Clinic Notes</Label>
        <Textarea
          id="clinicNotes"
          value={formData.clinicNotes}
          onChange={(e) => setFormData({ ...formData, clinicNotes: e.target.value })}
          placeholder="Any special notes for this category in your clinic..."
          rows={2}
        />
      </div>

      <div className="flex items-center space-x-2">
        <Checkbox
          id="isActive"
          checked={formData.isActive}
          onCheckedChange={(checked) => setFormData({ ...formData, isActive: checked as boolean })}
        />
        <Label htmlFor="isActive">Active in this clinic</Label>
      </div>

      <div className="flex justify-end gap-2 pt-4">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" disabled={isLoading}>
          {isLoading ? 'Saving...' : 'Save Changes'}
        </Button>
      </div>
    </form>
  );
};

export default ClinicCategoriesManagement;
