import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  ArrowLeft, 
  Settings, 
  Check, 
  X, 
  Plus,
  Edit2,
  Save,
  DollarSign,
  Clock,
  Users
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import {
  getAvailableCategories,
  getClinicCategories,
  updateClinicCategoryStatus,
  updateClinicCategorySettings,
  bulkUpdateClinicCategories,
  type AvailableCategory
} from '@/services/clinicCategorySettings';

const ClinicCategoryManagement: React.FC = () => {
  const { clinicId } = useParams<{ clinicId: string }>();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [editingCategory, setEditingCategory] = useState<number | null>(null);
  const [editForm, setEditForm] = useState<any>({});

  // Fetch available categories
  const { data: availableCategoriesResponse, isLoading: loadingAvailable } = useQuery({
    queryKey: ['availableCategories', clinicId],
    queryFn: () => getAvailableCategories(parseInt(clinicId!)),
    enabled: !!clinicId
  });

  // Fetch clinic categories
  const { data: clinicCategoriesResponse, isLoading: loadingClinic } = useQuery({
    queryKey: ['clinicCategories', clinicId],
    queryFn: () => getClinicCategories(parseInt(clinicId!)),
    enabled: !!clinicId
  });

  // Update category status mutation
  const updateStatusMutation = useMutation({
    mutationFn: ({ categoryId, isEnabled }: { categoryId: number; isEnabled: boolean }) =>
      updateClinicCategoryStatus(parseInt(clinicId!), categoryId, isEnabled),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['availableCategories', clinicId] });
      queryClient.invalidateQueries({ queryKey: ['clinicCategories', clinicId] });
      toast({
        title: "Success",
        description: "Category status updated successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update category status",
        variant: "destructive",
      });
    }
  });

  // Update category settings mutation
  const updateSettingsMutation = useMutation({
    mutationFn: ({ categoryId, settings }: { categoryId: number; settings: any }) =>
      updateClinicCategorySettings(parseInt(clinicId!), categoryId, settings),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['clinicCategories', clinicId] });
      setEditingCategory(null);
      setEditForm({});
      toast({
        title: "Success",
        description: "Category settings updated successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update category settings",
        variant: "destructive",
      });
    }
  });

  const availableCategories = availableCategoriesResponse?.data || [];
  const clinicCategories = clinicCategoriesResponse?.data || [];

  const handleStatusToggle = (categoryId: number, currentStatus: boolean) => {
    updateStatusMutation.mutate({
      categoryId,
      isEnabled: !currentStatus
    });
  };

  const handleEditCategory = (category: any) => {
    setEditingCategory(category.appointmentCategoryId);
    setEditForm({
      customName: category.customName || '',
      customDescription: category.customDescription || '',
      customCharge: category.customCharge || '',
      customDiscountPercentage: category.customDiscountPercentage || 0,
      customDuration: category.customDuration || '',
      clinicNotes: category.clinicNotes || ''
    });
  };

  const handleSaveSettings = () => {
    if (editingCategory) {
      updateSettingsMutation.mutate({
        categoryId: editingCategory,
        settings: editForm
      });
    }
  };

  const handleCancelEdit = () => {
    setEditingCategory(null);
    setEditForm({});
  };

  if (loadingAvailable || loadingClinic) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading categories...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <div className="flex items-center gap-4 mb-6">
        <Button variant="outline" size="sm" onClick={() => window.history.back()}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Category Management</h1>
          <p className="text-gray-600">Manage appointment categories for this clinic</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Available Categories */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Plus className="h-5 w-5" />
              Available Categories
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {availableCategories.map((category: AvailableCategory) => (
              <div
                key={category.appointmentCategoryId}
                className="flex items-center justify-between p-4 border rounded-lg"
              >
                <div className="flex-1">
                  <div className="flex items-center gap-3">
                    <div
                      className="w-4 h-4 rounded"
                      style={{ backgroundColor: category.color || '#3B82F6' }}
                    />
                    <div>
                      <h3 className="font-medium">{category.name}</h3>
                      <p className="text-sm text-gray-600">{category.description}</p>
                      <div className="flex items-center gap-4 mt-1">
                        <span className="text-xs text-gray-500 flex items-center gap-1">
                          <DollarSign className="h-3 w-3" />
                          {category.defaultCharge} {category.currency}
                        </span>
                        <span className="text-xs text-gray-500 flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          {category.estimatedDuration}min
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Switch
                    checked={category.isEnabledForClinic}
                    onCheckedChange={() => handleStatusToggle(
                      category.appointmentCategoryId,
                      category.isEnabledForClinic
                    )}
                    disabled={updateStatusMutation.isPending}
                  />
                  <Badge variant={category.isEnabledForClinic ? "default" : "secondary"}>
                    {category.isEnabledForClinic ? "Enabled" : "Disabled"}
                  </Badge>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Clinic Categories with Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Clinic Category Settings
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {clinicCategories.map((category: any) => (
              <div
                key={category.appointmentCategoryId}
                className="border rounded-lg p-4"
              >
                {editingCategory === category.appointmentCategoryId ? (
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="customName">Custom Name</Label>
                        <Input
                          id="customName"
                          value={editForm.customName}
                          onChange={(e) => setEditForm({ ...editForm, customName: e.target.value })}
                          placeholder={category.name}
                        />
                      </div>
                      <div>
                        <Label htmlFor="customCharge">Custom Charge</Label>
                        <Input
                          id="customCharge"
                          type="number"
                          value={editForm.customCharge}
                          onChange={(e) => setEditForm({ ...editForm, customCharge: parseFloat(e.target.value) })}
                          placeholder={category.charge?.toString()}
                        />
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="customDuration">Custom Duration (min)</Label>
                        <Input
                          id="customDuration"
                          type="number"
                          value={editForm.customDuration}
                          onChange={(e) => setEditForm({ ...editForm, customDuration: parseInt(e.target.value) })}
                          placeholder={category.duration?.toString()}
                        />
                      </div>
                      <div>
                        <Label htmlFor="customDiscountPercentage">Discount %</Label>
                        <Input
                          id="customDiscountPercentage"
                          type="number"
                          min="0"
                          max="100"
                          value={editForm.customDiscountPercentage}
                          onChange={(e) => setEditForm({ ...editForm, customDiscountPercentage: parseFloat(e.target.value) })}
                        />
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="customDescription">Custom Description</Label>
                      <Textarea
                        id="customDescription"
                        value={editForm.customDescription}
                        onChange={(e) => setEditForm({ ...editForm, customDescription: e.target.value })}
                        placeholder={category.description}
                      />
                    </div>
                    <div>
                      <Label htmlFor="clinicNotes">Clinic Notes</Label>
                      <Textarea
                        id="clinicNotes"
                        value={editForm.clinicNotes}
                        onChange={(e) => setEditForm({ ...editForm, clinicNotes: e.target.value })}
                        placeholder="Internal notes for this category..."
                      />
                    </div>
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        onClick={handleSaveSettings}
                        disabled={updateSettingsMutation.isPending}
                      >
                        <Save className="h-4 w-4 mr-2" />
                        Save
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={handleCancelEdit}
                      >
                        <X className="h-4 w-4 mr-2" />
                        Cancel
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-medium">{category.name}</h3>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleEditCategory(category)}
                      >
                        <Edit2 className="h-4 w-4 mr-2" />
                        Edit
                      </Button>
                    </div>
                    <p className="text-sm text-gray-600 mb-2">{category.description}</p>
                    <div className="flex items-center gap-4 text-xs text-gray-500">
                      <span className="flex items-center gap-1">
                        <DollarSign className="h-3 w-3" />
                        {category.charge} {category.currency}
                      </span>
                      <span className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        {category.duration}min
                      </span>
                      {category.customizations?.clinicNotes && (
                        <Badge variant="outline" className="text-xs">
                          Has Notes
                        </Badge>
                      )}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ClinicCategoryManagement;
