
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Pet } from "@/store/types";
import { useQuery } from "@tanstack/react-query";
import { getPets } from "@/services/pets";
import LoadingPage from "@/components/common/LoadingPage";
import { format } from "date-fns";

interface ViewPetsModalProps {
  isOpen: boolean;
  onClose: () => void;
  ownerId: string;
}

const ViewPetsModal = ({ isOpen, onClose, ownerId }: ViewPetsModalProps) => {
  const { data: response, isLoading } = useQuery({
    queryKey: ["pets", ownerId],
    // Use getPets with proper parameter
    queryFn: () => getPets({ clientId: ownerId, limit: 1000 }),
    enabled: isOpen,
  });

  const pets = response?.success ? (response?.data?.data || []) : [];
  const pagination = response?.data?.pagination;
  const totalPages = pagination?.totalPages || 1;
  const currentPage = pagination?.page || 1;

  if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Pets List</DialogTitle>
        </DialogHeader>
        
        {isLoading ? (
          <LoadingPage className="h-40" />
        ) : pets.length === 0 ? (
          <div className="text-center py-8 text-gray-500">No pets found for this client</div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4">
            {pets.map((pet) => (
              <div
                key={pet.petId}
                className="border rounded-lg p-4 hover:shadow-md transition-shadow"
              >
                <h3 className="font-semibold text-lg mb-2">{pet.name}</h3>
                <div className="space-y-2 text-sm">
                  <p><span className="font-medium">Species:</span> {pet.species?.name || pet.species?.speciesName || 'N/A'}</p>
                  <p><span className="font-medium">Breed:</span> {pet.breed?.breedName || 'N/A'}</p>
                  <p><span className="font-medium">Gender:</span> {pet.gender}</p>
                  <p><span className="font-medium">Date of Birth:</span> {pet.dateOfBirth ? format(new Date(pet.dateOfBirth), "MMM dd, yyyy") : 'N/A'}</p>
                  <p>
                    <span className="font-medium">Status:</span>
                    <span className={`ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      pet.lifeStatus === "alive"
                        ? "bg-green-100 text-green-800"
                        : "bg-red-100 text-red-800"
                    }`}>
                      {pet.lifeStatus}
                    </span>
                  </p>
                </div>
              </div>
            ))}
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default ViewPetsModal;
