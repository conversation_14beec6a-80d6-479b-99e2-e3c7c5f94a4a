import { useState, useEffect } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { createPet } from "@/services/pets";
import { useSpeciesQuery, useBreedsBySpeciesQuery } from "@/hooks/useReferenceDataQuery";
import { useToast } from "@/components/ui/use-toast";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Client } from "@/store/types";
import { Upload, X } from "lucide-react";

interface AddPetModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  client?: Client;
  preselectedClient?: Client;
  onSuccess?: () => void;
}

// Define form validation schema
const formSchema = z.object({
  name: z.string().min(1, "Pet name is required"),
  speciesId: z.string().min(1, "Species is required"),
  breedId: z.string().min(1, "Breed is required"),
  color: z.string().min(1, "Color is required"),
  lifeStatus: z.enum(["alive", "deceased"]),
  gender: z.enum(["male", "female"]),
  microchipId: z.string().min(1, "Microchip ID is required"),
  weight: z.number().min(0, "Weight must be positive"),
  dateOfBirth: z.string().min(1, "Date of birth is required"),
  clientId: z.string().min(1, "Client is required"),
  petStatus: z.number().default(1)
});

type FormValues = z.infer<typeof formSchema>;

const AddPetModal = ({ open, onOpenChange, client, preselectedClient, onSuccess }: AddPetModalProps) => {
  const selectedClient = client || preselectedClient;
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string>("");
  const [selectedSpecies, setSelectedSpecies] = useState<string>("");

  // Initialize form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      speciesId: "",
      breedId: "",
      color: "",
      lifeStatus: "alive",
      gender: "male",
      microchipId: `MC${Date.now()}`, // Generate default microchip ID
      weight: 0,
      dateOfBirth: "",
      clientId: selectedClient?.clientId?.toString() || "",
      petStatus: 1
    }
  });

  // Update clientId when client changes
  useEffect(() => {
    if (client?.clientId) {
      form.setValue("clientId", client.clientId.toString());
    }
  }, [client, form]);

  // Fetch species using centralized reference data
  const { data: species, isLoading: isLoadingSpecies } = useSpeciesQuery();

  // Fetch breeds based on selected species using centralized reference data
  const { data: breeds, isLoading: isLoadingBreeds } = useBreedsBySpeciesQuery(selectedSpecies || null);

  // Watch for species changes
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === "speciesId") {
        console.log("Species changed:", value.speciesId);
        setSelectedSpecies(value.speciesId || "");
        form.setValue("breedId", ""); // Reset breed when species changes
      }
    });
    return () => subscription.unsubscribe();
  }, [form]);

  const mutation = useMutation({
    mutationFn: async (data: FormValues) => {
      // Convert the form data to match backend expectations
      const petData = {
        name: data.name, // Backend now uses 'name' field
        speciesId: parseInt(data.speciesId), // Convert to number for query efficiency
        breedId: parseInt(data.breedId), // Convert to number for query efficiency
        color: data.color,
        lifeStatus: data.lifeStatus,
        gender: data.gender,
        microchipId: data.microchipId,
        weight: data.weight,
        dateOfBirth: new Date(data.dateOfBirth),
        clientId: parseInt(data.clientId), // Convert to number for consistency
        petStatus: data.petStatus
      };
      // If we have a file, we need to handle it differently
      // For now, just create the pet without the image
      return createPet(petData);
    },
    onSuccess: async (data) => {
      if (data?.status === 200 || data?.status === 201) {
        toast({
          title: "Success",
          description: "Pet added successfully",
        });
        await queryClient.invalidateQueries({ queryKey: ["pets"] });
        resetForm();
        onOpenChange(false);
        if (onSuccess) onSuccess();
      } else {
        toast({
          title: "Error",
          description: data?.message || "Something went wrong",
          variant: "destructive",
          duration: 2500,
        });
      }
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to add pet",
        variant: "destructive",
        duration: 2500,
      });
    }
  });

  const resetForm = () => {
    form.reset({
      name: "",
      speciesId: "",
      breedId: "",
      color: "",
      lifeStatus: "alive",
      gender: "male",
      microchipId: `MC${Date.now()}`, // Generate new microchip ID
      weight: 0,
      dateOfBirth: "",
      clientId: selectedClient?.clientId?.toString() || "",
      petStatus: 1
    });
    setSelectedFile(null);
    setPreviewUrl("");
    setSelectedSpecies("");
  };

  const onSubmit = (values: FormValues) => {
    mutation.mutate(values);
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setSelectedFile(file);
      setPreviewUrl(URL.createObjectURL(file));
    }
  };

  const handleRemoveImage = () => {
    setSelectedFile(null);
    setPreviewUrl("");
  };

  // Debug logging
  console.log("AddPetModal Debug:", {
    open,
    selectedSpecies,
    speciesCount: species.length,
    breedsCount: breeds.length,
    isLoadingSpecies,
    isLoadingBreeds
  });

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Add New Pet</DialogTitle>
          <DialogDescription>
            {selectedClient
              ? `Add a new pet for ${selectedClient.firstName} ${selectedClient.lastName}`
              : "Add a new pet to the system"
            }
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Pet Name *</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Enter pet name" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="speciesId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Species *</FormLabel>
                    <FormControl>
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select species" />
                        </SelectTrigger>
                        <SelectContent>
                          {isLoadingSpecies ? (
                            <SelectItem value="loading" disabled>Loading species...</SelectItem>
                          ) : species.length === 0 ? (
                            <SelectItem value="no-data" disabled>No species available</SelectItem>
                          ) : (
                            species.filter((item: any) => item.speciesId).map((item: any) => (
                              <SelectItem key={item.speciesId} value={item.speciesId.toString()}>
                                {item.displayName || item.name || item.speciesName}
                              </SelectItem>
                            ))
                          )}
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="breedId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Breed *</FormLabel>
                    <FormControl>
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                        disabled={!selectedSpecies}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder={selectedSpecies ? "Select breed" : "Select species first"} />
                        </SelectTrigger>
                        <SelectContent>
                          {isLoadingBreeds ? (
                            <SelectItem value="loading" disabled>Loading breeds...</SelectItem>
                          ) : breeds.length === 0 ? (
                            <SelectItem value="no-data" disabled>
                              {selectedSpecies ? "No breeds available for this species" : "Select species first"}
                            </SelectItem>
                          ) : (
                            breeds.filter((breed: any) => breed.breedId).map((breed: any) => (
                              <SelectItem key={breed.breedId} value={breed.breedId.toString()}>
                                {breed.breedName || breed.name}
                              </SelectItem>
                            ))
                          )}
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="color"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Color *</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Enter color" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="gender"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Gender *</FormLabel>
                    <FormControl>
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select gender" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="male">Male</SelectItem>
                          <SelectItem value="female">Female</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="lifeStatus"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Life Status *</FormLabel>
                    <FormControl>
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="alive">Alive</SelectItem>
                          <SelectItem value="deceased">Deceased</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="microchipId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Microchip ID *</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Enter microchip ID" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="weight"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Weight (kg) *</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        placeholder="Enter weight"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value))}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="dateOfBirth"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Date of Birth *</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="space-y-2">
              <Label>Pet Image</Label>
              <div className="border-2 border-dashed rounded-md p-4 text-center">
                {previewUrl ? (
                  <div className="relative">
                    <img
                      src={previewUrl}
                      alt="Preview"
                      className="max-h-40 object-contain mx-auto"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="absolute top-0 right-0 bg-white rounded-full"
                      onClick={handleRemoveImage}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ) : (
                  <Label
                    htmlFor="petImage"
                    className="cursor-pointer flex flex-col items-center justify-center gap-2"
                  >
                    <Upload className="h-8 w-8 text-gray-400" />
                    <span>Click to upload a pet image</span>
                    <Input
                      id="petImage"
                      type="file"
                      accept="image/*"
                      className="hidden"
                      onChange={handleFileChange}
                    />
                  </Label>
                )}
              </div>
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  resetForm();
                  onOpenChange(false);
                }}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={mutation.isPending}>
                {mutation.isPending ? "Adding..." : "Add Pet"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default AddPetModal;
