import { useState, useEffect } from "react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { updateBreed } from "@/services/breeds";
import { getSpecies } from "@/services/api";
import { useToast } from "@/components/ui/use-toast";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import LoadingSpinner from "@/components/common/LoadingSpinner.tsx";

interface EditBreedModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  breed: any;
}

const EditBreedModal = ({ open, onOpenChange, breed }: EditBreedModalProps) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [formData, setFormData] = useState({
    speciesId: "",
    breedName: "",
    commonColour: "",
    origin: "",
    sizeCategory: "medium" as "small" | "medium" | "large",
    lifespan: 0,
    temperament: "",
  });

  useEffect(() => {
    if (breed) {
      setFormData({
        speciesId: breed.speciesId || "",
        breedName: breed.breedName || "",
        commonColour: breed.commonColour || "",
        origin: breed.origin || "",
        sizeCategory: breed.sizeCategory || "medium",
        lifespan: breed.lifespan || 0,
        temperament: breed.temperament || "",
      });
    }
  }, [breed]);

  // Fetch species
  const { data: speciesResponse, isLoading: isLoadingSpecies } = useQuery({
    queryKey: ["species"],
    queryFn: async () => getSpecies(),
  });

  const mutation = useMutation({
    mutationFn: (data: any) => updateBreed(breed._id, data),
    onSuccess: async (response) => {
      if (response.status === 200 || response.status === 201) {
        toast({
          title: "Success",
          description: "Breed updated successfully",
        });
        await queryClient.invalidateQueries({ queryKey: ["breeds"] });
        onOpenChange(false);
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to update breed",
          variant: "destructive",
        });
      }
    },
    onError: (error) => {
      console.error("Update breed error:", error);
      toast({
        title: "Error",
        description: "Failed to update breed",
        variant: "destructive",
      });
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields before submitting
    if (!formData.speciesId) {
      toast({
        title: "Validation Error",
        description: "Species is required",
        variant: "destructive",
      });
      return;
    }

    if (!formData.breedName) {
      toast({
        title: "Validation Error",
        description: "Breed name is required",
        variant: "destructive",
      });
      return;
    }

    // Convert speciesId to number before submitting
    const submitData = {
      ...formData,
      speciesId: parseInt(formData.speciesId) // Convert to number for query efficiency
    };

    // Proceed with mutation if validation passes
    mutation.mutate(submitData);
  };

  const species = speciesResponse?.data?.data || [];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Edit Breed</DialogTitle>
          <DialogDescription>
            Update the details for this animal breed.
          </DialogDescription>
        </DialogHeader>

        {isLoadingSpecies ? (
          <div className="flex justify-center py-8">
            <LoadingSpinner />
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="speciesId">Species *</Label>
              <Select
                value={formData.speciesId}
                onValueChange={(value: string) => setFormData({ ...formData, speciesId: value })}
                required
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select Species" />
                </SelectTrigger>
                <SelectContent>
                  {species.map((type) => (
                    <SelectItem key={type._id} value={type._id}>
                      {type.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="breedName">Breed Name *</Label>
              <Input
                id="breedName"
                value={formData.breedName}
                onChange={(e) => setFormData({ ...formData, breedName: e.target.value })}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="commonColour">Common Color *</Label>
              <Input
                id="commonColour"
                value={formData.commonColour}
                onChange={(e) => setFormData({ ...formData, commonColour: e.target.value })}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="origin">Origin *</Label>
              <Input
                id="origin"
                value={formData.origin}
                onChange={(e) => setFormData({ ...formData, origin: e.target.value })}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="sizeCategory">Size Category *</Label>
              <Select
                value={formData.sizeCategory}
                onValueChange={(value: "small" | "medium" | "large") =>
                  setFormData({ ...formData, sizeCategory: value })
                }
                required
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select Size" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="small">Small</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="large">Large</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="lifespan">Lifespan (years) *</Label>
              <Input
                id="lifespan"
                type="number"
                min="1"
                value={formData.lifespan || ''}
                onChange={(e) => setFormData({ ...formData, lifespan: parseInt(e.target.value) || 0 })}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="temperament">Temperament</Label>
              <Textarea
                id="temperament"
                value={formData.temperament}
                onChange={(e) => setFormData({ ...formData, temperament: e.target.value })}
              />
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={mutation.isPending}>
                {mutation.isPending ? "Updating..." : "Update Breed"}
              </Button>
            </DialogFooter>
          </form>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default EditBreedModal;