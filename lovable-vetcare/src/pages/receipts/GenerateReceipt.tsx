import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery, useMutation } from '@tanstack/react-query';
import { format } from 'date-fns';
import { 
  ArrowLeft, 
  Receipt, 
  Calendar,
  User,
  PawPrint,
  Building,
  CreditCard,
  CheckCircle,
  AlertCircle,
  FileText,
  Loader2
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/components/ui/use-toast';
import { getAppointmentById } from '@/services/appointments';
import { api } from '@/services/api';

const GenerateReceipt = () => {
  const { appointmentId } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [isGenerating, setIsGenerating] = useState(false);

  // Fetch appointment details
  const { data: appointmentResponse, isLoading, error } = useQuery({
    queryKey: ['appointment', appointmentId],
    queryFn: () => getAppointmentById(parseInt(appointmentId!)),
    enabled: !!appointmentId
  });

  const appointment = appointmentResponse?.data?.data;

  // Generate receipt mutation
  const generateReceiptMutation = useMutation({
    mutationFn: async () => {
      const response = await api.post(`/receipts/generate/${appointmentId}`);
      return response.data;
    },
    onSuccess: (data) => {
      toast({
        title: "Success",
        description: "Receipt generated successfully",
      });
      // Navigate to the receipt details page
      navigate(`/receipts/${data.data.receiptId}`);
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to generate receipt",
        variant: "destructive",
      });
    }
  });

  const handleGenerateReceipt = () => {
    setIsGenerating(true);
    generateReceiptMutation.mutate();
  };

  if (isLoading) {
    return (
      <div className="p-8">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (error || !appointment) {
    return (
      <div className="p-8">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Failed to load appointment details. Please try again.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="p-8 max-w-4xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate(`/appointments/${appointmentId}`)}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Appointment
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Generate Receipt</h1>
            <p className="text-gray-600">
              Appointment #{appointment.appointmentId}
            </p>
          </div>
        </div>
      </div>

      {/* Appointment Summary */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Appointment Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Client & Pet Info */}
            <div className="space-y-4">
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Client Information</h3>
                <div className="space-y-1 text-sm">
                  <p className="flex items-center gap-2">
                    <User className="h-4 w-4" />
                    {appointment.clientName || 'Unknown Client'}
                  </p>
                  <p className="flex items-center gap-2">
                    <PawPrint className="h-4 w-4" />
                    {appointment.petName || 'Unknown Pet'}
                  </p>
                </div>
              </div>

              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Appointment Details</h3>
                <div className="space-y-1 text-sm">
                  <p className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    {format(new Date(appointment.dateTime), 'PPp')}
                  </p>
                  <p className="flex items-center gap-2">
                    <Building className="h-4 w-4" />
                    Clinic ID: {appointment.clinicId}
                  </p>
                </div>
              </div>
            </div>

            {/* Status & Types */}
            <div className="space-y-4">
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Status</h3>
                <Badge className={
                  appointment.status === 'completed' 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-orange-100 text-orange-800'
                }>
                  {appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}
                </Badge>
              </div>

              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Appointment Types</h3>
                <div className="flex flex-wrap gap-2">
                  {appointment.appointmentTypes?.map((type: any, index: number) => (
                    <Badge key={index} variant="outline">
                      {type.name || type.appointmentTypeName}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Services Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Services Summary
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Appointment Categories */}
          {appointment.appointmentCategories && appointment.appointmentCategories.length > 0 && (
            <div>
              <h4 className="font-medium text-gray-700 mb-3">Appointment Categories:</h4>
              <div className="space-y-3">
                {appointment.appointmentCategories.map((category: any, index: number) => {
                  const categoryServices = category.categoryServices || [];
                  const totalCategoryPrice = categoryServices.reduce((sum: number, service: any) => sum + (service.price || 0), 0);

                  return (
                    <div key={index} className="border border-purple-200 rounded-lg p-3 bg-purple-50">
                      <div className="flex justify-between items-center mb-2">
                        <span className="font-medium text-purple-900">Category #{category.appointmentCategoryId}</span>
                        <Badge className={`text-xs ${
                          category.categoryStatus === 'completed' ? 'bg-green-100 text-green-800' :
                          category.categoryStatus === 'in_progress' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {category.categoryStatus || 'not_started'}
                        </Badge>
                      </div>

                      {categoryServices.length > 0 && (
                        <div className="space-y-1 mb-2">
                          {categoryServices.map((service: any, serviceIndex: number) => (
                            <div key={serviceIndex} className="flex justify-between text-sm">
                              <span className="text-purple-700">• {service.categoryServiceName}</span>
                              <span className="font-medium text-purple-800">
                                {service.currency || 'KES'} {service.price || 0}
                              </span>
                            </div>
                          ))}
                        </div>
                      )}

                      <div className="flex justify-between items-center pt-2 border-t border-purple-200">
                        <span className="font-medium text-purple-900">Category Total:</span>
                        <span className="font-bold text-purple-900">KES {totalCategoryPrice}</span>
                      </div>

                      {category.categoryNotes && (
                        <div className="mt-2 p-2 bg-purple-100 rounded text-sm">
                          <span className="font-medium text-purple-800">Notes: </span>
                          <span className="text-purple-700">{category.categoryNotes}</span>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {/* Appointment Types */}
          {appointment.appointmentTypes && appointment.appointmentTypes.length > 0 && (
            <div>
              <h4 className="font-medium text-gray-700 mb-2">Appointment Types:</h4>
              <div className="space-y-2">
                {appointment.appointmentTypes.map((type: any, index: number) => (
                  <div key={index} className="flex justify-between items-center p-2 bg-blue-50 rounded">
                    <span className="text-blue-900">{type.categoryName || type.name}</span>
                    <span className="font-medium text-blue-800">
                      {type.currency || 'KES'} {type.price || 0}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Services */}
          {appointment.services && appointment.services.length > 0 && (
            <div>
              <h4 className="font-medium text-gray-700 mb-2">Additional Services:</h4>
              <div className="space-y-2">
                {appointment.services.map((service: any, index: number) => (
                  <div key={index} className="flex justify-between items-center p-2 bg-green-50 rounded">
                    <div>
                      <span className="text-green-900">{service.serviceName}</span>
                      {service.notes && (
                        <p className="text-sm text-green-700">{service.notes}</p>
                      )}
                    </div>
                    <span className="font-medium text-green-800">
                      {service.currency} {service.price}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {(!appointment.appointmentCategories?.length && !appointment.appointmentTypes?.length && !appointment.services?.length) && (
            <div className="text-center py-8">
              <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-500">No services, categories, or appointment types found</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Receipt Generation */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Receipt className="h-5 w-5" />
            Receipt Generation
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Prerequisites Check */}
          <div className="space-y-3">
            <h3 className="font-medium">Prerequisites</h3>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span className="text-sm">Appointment exists</span>
              </div>
              <div className="flex items-center gap-2">
                {appointment.status === 'completed' ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <AlertCircle className="h-4 w-4 text-orange-500" />
                )}
                <span className="text-sm">
                  Appointment status: {appointment.status}
                  {appointment.status !== 'completed' && (
                    <span className="text-orange-600 ml-1">(Should be completed)</span>
                  )}
                </span>
              </div>
            </div>
          </div>

          {/* Generation Actions */}
          <div className="space-y-4">
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                A receipt will be generated based on the invoice and payment records for this appointment.
                Make sure an invoice has been generated and payment has been processed before generating the receipt.
              </AlertDescription>
            </Alert>

            <div className="flex gap-4">
              <Button
                onClick={handleGenerateReceipt}
                disabled={generateReceiptMutation.isPending || isGenerating}
                className="bg-green-600 hover:bg-green-700"
              >
                {generateReceiptMutation.isPending || isGenerating ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Generating...
                  </>
                ) : (
                  <>
                    <Receipt className="h-4 w-4 mr-2" />
                    Generate Receipt
                  </>
                )}
              </Button>

              <Button
                variant="outline"
                onClick={() => navigate(`/invoices/appointment/${appointmentId}`)}
              >
                <CreditCard className="h-4 w-4 mr-2" />
                View Invoice
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default GenerateReceipt;
