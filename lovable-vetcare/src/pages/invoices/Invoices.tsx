import { useState } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { 
  Search, 
  Plus, 
  FileText, 
  Calendar,
  User,
  DollarSign,
  Eye,
  Download,
  Send,
  CheckCircle,
  Clock,
  AlertCircle
} from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { useApiWithClinic } from "@/hooks/useApiWithClinic";

interface Invoice {
  invoiceId: number;
  appointmentId: number;
  petName: string;
  clientName: string;
  clientEmail: string;
  totalAmount: number;
  paidAmount: number;
  discountAmount: number;
  status: string;
  invoiceDate: string;
  dueDate: string;
  items: InvoiceItem[];
}

interface InvoiceItem {
  categoryName: string;
  serviceName: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  discount: number;
}

export default function Invoices() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedStatus, setSelectedStatus] = useState<string>("all");
  const [activeTab, setActiveTab] = useState("all-invoices");

  const { get, clinicId } = useApiWithClinic();

  // Fetch invoices
  const { data: invoicesData, isLoading } = useQuery({
    queryKey: ['invoices', clinicId],
    queryFn: () => get('/invoices'),
    enabled: !!clinicId
  });

  const invoices = invoicesData?.data || [];

  // Filter invoices
  const filteredInvoices = invoices.filter((invoice: Invoice) => {
    const matchesSearch = invoice.petName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         invoice.clientName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         invoice.invoiceId?.toString().includes(searchTerm);
    
    const matchesStatus = selectedStatus === "all" || invoice.status === selectedStatus;
    
    return matchesSearch && matchesStatus;
  });

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'paid': return <CheckCircle className="h-4 w-4" />;
      case 'pending': return <Clock className="h-4 w-4" />;
      case 'overdue': return <AlertCircle className="h-4 w-4" />;
      default: return <FileText className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'overdue': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const statuses = ['pending', 'paid', 'overdue'];

  // Calculate statistics
  const totalInvoices = invoices.length;
  const totalAmount = invoices.reduce((sum: number, invoice: Invoice) => sum + invoice.totalAmount, 0);
  const paidAmount = invoices.reduce((sum: number, invoice: Invoice) => sum + invoice.paidAmount, 0);
  const pendingAmount = totalAmount - paidAmount;

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <FileText className="h-6 w-6" />
          <h1 className="text-3xl font-bold">Invoices</h1>
        </div>
        <Button className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Create Invoice
        </Button>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <FileText className="h-8 w-8 text-blue-500" />
              <div>
                <p className="text-2xl font-bold">{totalInvoices}</p>
                <p className="text-sm text-gray-600">Total Invoices</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <DollarSign className="h-8 w-8 text-green-500" />
              <div>
                <p className="text-2xl font-bold">{formatCurrency(totalAmount)}</p>
                <p className="text-sm text-gray-600">Total Amount</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-8 w-8 text-green-500" />
              <div>
                <p className="text-2xl font-bold">{formatCurrency(paidAmount)}</p>
                <p className="text-sm text-gray-600">Paid Amount</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <Clock className="h-8 w-8 text-yellow-500" />
              <div>
                <p className="text-2xl font-bold">{formatCurrency(pendingAmount)}</p>
                <p className="text-sm text-gray-600">Pending Amount</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search by invoice ID, pet name, or client name..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="px-3 py-2 border rounded-md"
            >
              <option value="all">All Status</option>
              {statuses.map((status) => (
                <option key={status} value={status}>
                  {status.charAt(0).toUpperCase() + status.slice(1)}
                </option>
              ))}
            </select>
          </div>
        </CardContent>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="all-invoices">All Invoices ({filteredInvoices.length})</TabsTrigger>
          <TabsTrigger value="pending">Pending</TabsTrigger>
          <TabsTrigger value="overdue">Overdue</TabsTrigger>
          <TabsTrigger value="paid">Paid</TabsTrigger>
        </TabsList>

        {/* All Invoices */}
        <TabsContent value="all-invoices" className="space-y-4">
          {isLoading ? (
            <div className="text-center py-8">Loading invoices...</div>
          ) : filteredInvoices.length === 0 ? (
            <Card>
              <CardContent className="text-center py-8">
                <FileText className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <p className="text-gray-500">No invoices found.</p>
                <Button className="mt-4">
                  Create First Invoice
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {filteredInvoices.map((invoice: Invoice) => (
                <Card key={invoice.invoiceId} className="hover:shadow-md transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className={`p-2 rounded-lg ${getStatusColor(invoice.status)}`}>
                          {getStatusIcon(invoice.status)}
                        </div>
                        <div>
                          <CardTitle className="text-lg">Invoice #{invoice.invoiceId}</CardTitle>
                          <CardDescription className="flex items-center gap-4">
                            <span className="flex items-center gap-1">
                              <User className="h-4 w-4" />
                              {invoice.clientName} - {invoice.petName}
                            </span>
                            <span className="flex items-center gap-1">
                              <Calendar className="h-4 w-4" />
                              {new Date(invoice.invoiceDate).toLocaleDateString()}
                            </span>
                          </CardDescription>
                        </div>
                      </div>
                      <div className="text-right">
                        <Badge className={getStatusColor(invoice.status)}>
                          {invoice.status}
                        </Badge>
                        <p className="text-lg font-bold mt-1">{formatCurrency(invoice.totalAmount)}</p>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <h4 className="font-medium text-sm text-gray-600 mb-1">Total Amount</h4>
                        <p className="text-sm font-semibold">{formatCurrency(invoice.totalAmount)}</p>
                      </div>
                      <div>
                        <h4 className="font-medium text-sm text-gray-600 mb-1">Paid Amount</h4>
                        <p className="text-sm font-semibold text-green-600">{formatCurrency(invoice.paidAmount)}</p>
                      </div>
                      <div>
                        <h4 className="font-medium text-sm text-gray-600 mb-1">Balance Due</h4>
                        <p className="text-sm font-semibold text-red-600">
                          {formatCurrency(invoice.totalAmount - invoice.paidAmount)}
                        </p>
                      </div>
                    </div>
                    
                    {invoice.discountAmount > 0 && (
                      <div className="bg-green-50 p-2 rounded">
                        <p className="text-sm text-green-700">
                          Discount Applied: {formatCurrency(invoice.discountAmount)}
                        </p>
                      </div>
                    )}
                    
                    <div className="flex gap-2 pt-2 border-t">
                      <Button variant="outline" size="sm" className="flex items-center gap-1">
                        <Eye className="h-3 w-3" />
                        View
                      </Button>
                      <Button variant="outline" size="sm" className="flex items-center gap-1">
                        <Download className="h-3 w-3" />
                        Download
                      </Button>
                      <Button variant="outline" size="sm" className="flex items-center gap-1">
                        <Send className="h-3 w-3" />
                        Send
                      </Button>
                      {invoice.status === 'pending' && (
                        <Button size="sm" className="flex items-center gap-1">
                          <CheckCircle className="h-3 w-3" />
                          Mark Paid
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        {/* Status-specific tabs */}
        {statuses.map((status) => (
          <TabsContent key={status} value={status} className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="capitalize">{status} Invoices</CardTitle>
                <CardDescription>
                  Invoices with {status} status
                </CardDescription>
              </CardHeader>
              <CardContent>
                {invoices.filter((invoice: Invoice) => invoice.status === status).length === 0 ? (
                  <p className="text-center text-gray-500 py-8">No {status} invoices found.</p>
                ) : (
                  <p className="text-center text-gray-500 py-8">
                    {status.charAt(0).toUpperCase() + status.slice(1)} invoices view coming soon...
                  </p>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
}
