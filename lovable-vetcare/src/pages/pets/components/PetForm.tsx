import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useSpeciesQuery } from "@/hooks/useReferenceDataQuery";
import { Loader } from "lucide-react";

const petFormSchema = z.object({
    name: z.string().min(1, "Pet name is required"),
    species: z.string().min(1, "Species is required"),
    breed: z.string().min(1, "Breed is required"),
    color: z.string().min(1, "Color is required"),
    status: z.enum(["alive", "deceased"]),
    gender: z.enum(["male", "female"]),
    microchipId: z.string().optional(),
    weight: z.number().min(0, "Weight must be positive"),
    dateOfBirth: z.string().min(1, "Date of birth is required"),
    ownerProfileId: z.string().min(1, "Owner is required"),
});

type PetFormValues = z.infer<typeof petFormSchema>;

interface PetFormProps {
    initialValues?: Partial<PetFormValues>;
    onSubmit: (data: PetFormValues) => void;
    isLoading?: boolean;
}

const PetForm = ({ initialValues, onSubmit, isLoading }: PetFormProps) => {
    const form = useForm<PetFormValues>({
        resolver: zodResolver(petFormSchema),
        defaultValues: {
            name: "",
            species: "",
            breed: "",
            color: "",
            status: "alive",
            gender: "male",
            microchipId: "",
            weight: 0,
            dateOfBirth: "",
            ownerProfileId: "",
            ...initialValues,
        },
    });

    const { data: species, isLoading: isLoadingSpecies } = useSpeciesQuery();

    return (
        <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                        control={form.control}
                        name="name"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Pet Name</FormLabel>
                                <FormControl>
                                    <Input placeholder="Enter pet name" {...field} />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="species"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Species</FormLabel>
                                <Select onValueChange={field.onChange} value={field.value}>
                                    <FormControl>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select species" />
                                        </SelectTrigger>
                                    </FormControl>
                                    <SelectContent>
                                        {isLoadingSpecies ? (
                                            <div className="flex items-center justify-center p-4">
                                                <Loader className="h-4 w-4 animate-spin" />
                                            </div>
                                        ) : (
                                            species.map((type: any) => (
                                                <SelectItem key={type.speciesId} value={type.speciesName}>
                                                    {type.speciesName}
                                                </SelectItem>
                                            ))
                                        )}
                                    </SelectContent>
                                </Select>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="breed"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Breed</FormLabel>
                                <FormControl>
                                    <Input placeholder="Enter breed" {...field} />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="color"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Color</FormLabel>
                                <FormControl>
                                    <Input placeholder="Enter color" {...field} />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="status"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Status</FormLabel>
                                <Select
                                    onValueChange={field.onChange}
                                    defaultValue={field.value}
                                >
                                    <FormControl>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select status" />
                                        </SelectTrigger>
                                    </FormControl>
                                    <SelectContent>
                                        <SelectItem value="alive">Alive</SelectItem>
                                        <SelectItem value="deceased">Deceased</SelectItem>
                                    </SelectContent>
                                </Select>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="gender"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Gender</FormLabel>
                                <Select
                                    onValueChange={field.onChange}
                                    defaultValue={field.value}
                                >
                                    <FormControl>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select gender" />
                                        </SelectTrigger>
                                    </FormControl>
                                    <SelectContent>
                                        <SelectItem value="male">Male</SelectItem>
                                        <SelectItem value="female">Female</SelectItem>
                                    </SelectContent>
                                </Select>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="microchipId"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Microchip ID (Optional)</FormLabel>
                                <FormControl>
                                    <Input placeholder="Enter microchip ID" {...field} />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="weight"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Weight (kg)</FormLabel>
                                <FormControl>
                                    <Input
                                        type="number"
                                        step="0.01"
                                        placeholder="Enter weight"
                                        {...field}
                                        onChange={(e) => field.onChange(parseFloat(e.target.value))}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                </div>

                <div className="col-span-2 flex justify-end space-x-2">
                    <Button type="submit" disabled={isLoading}>
                        {isLoading ? <Loader className="h-4 w-4 animate-spin mr-2" /> : null}
                        Save Pet
                    </Button>
                </div>
            </form>
        </Form>
    );
};

export default PetForm;
