import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { 
  Search, 
  Plus, 
  Filter, 
  Heart, 
  Calendar,
  MapPin,
  Phone,
  Mail,
  PawPrint,
  User
} from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { getPets } from "@/services/pets";
import { getClients } from "@/services/clients";
import { useApiWithClinic } from "@/hooks/useApiWithClinic";
import AddPetModal from "@/pages/clients/components/AddPetModal";
import { Pet, Client } from "@/store/types";

export default function UnifiedPets() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedSpecies, setSelectedSpecies] = useState<string>("all");
  const [selectedStatus, setSelectedStatus] = useState<string>("all");
  const [activeTab, setActiveTab] = useState("all-pets");
  const [showAddPetModal, setShowAddPetModal] = useState(false);
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);

  const { clinicId } = useApiWithClinic();

  // Fetch pets
  const { data: petsData, isLoading: petsLoading, refetch: refetchPets, error: petsError } = useQuery({
    queryKey: ['pets', clinicId],
    queryFn: () => getPets({ clinicId, limit: 1000 }),
    enabled: !!clinicId
  });

  // Fetch clients
  const { data: clientsData, isLoading: clientsLoading, error: clientsError } = useQuery({
    queryKey: ['clients', clinicId],
    queryFn: () => getClients({ clinicId, limit: 1000 }),
    enabled: !!clinicId
  });

  const pets = petsData?.success ? (petsData?.data?.data || []) : [];
  const clients = clientsData?.success ? (clientsData?.data?.data || []) : [];

  // Filter pets based on search and filters
  const filteredPets = pets.filter((pet: Pet) => {
    const matchesSearch = pet.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         pet.client?.firstName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         pet.client?.lastName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         pet.species?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         pet.species?.speciesName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         pet.breed?.breedName?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesSpecies = selectedSpecies === "all" || pet.species?.name === selectedSpecies || pet.species?.speciesName === selectedSpecies;
    const matchesStatus = selectedStatus === "all" || pet.lifeStatus === selectedStatus;

    return matchesSearch && matchesSpecies && matchesStatus;
  });

  // Get unique species for filter
  const uniqueSpecies = [...new Set(pets.map((pet: Pet) => pet.species?.name || pet.species?.speciesName).filter(Boolean))];

  const handleAddPet = (client?: Client) => {
    setSelectedClient(client || null);
    setShowAddPetModal(true);
  };

  const handlePetAdded = () => {
    refetchPets();
    setShowAddPetModal(false);
    setSelectedClient(null);
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <PawPrint className="h-6 w-6" />
          <h1 className="text-3xl font-bold">Pets Management</h1>
        </div>
        <Button onClick={() => handleAddPet()} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Add New Pet
        </Button>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search pets by name, owner, species, or breed..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select
                value={selectedSpecies}
                onChange={(e) => setSelectedSpecies(e.target.value)}
                className="px-3 py-2 border rounded-md"
              >
                <option value="all">All Species</option>
                {uniqueSpecies.map((species) => (
                  <option key={species} value={species}>{species}</option>
                ))}
              </select>
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="px-3 py-2 border rounded-md"
              >
                <option value="all">All Status</option>
                <option value="alive">Alive</option>
                <option value="deceased">Deceased</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="all-pets">All Pets ({filteredPets.length})</TabsTrigger>
          <TabsTrigger value="by-clients">By Clients ({clients.length})</TabsTrigger>
          <TabsTrigger value="statistics">Statistics</TabsTrigger>
        </TabsList>

        {/* All Pets View */}
        <TabsContent value="all-pets" className="space-y-4">
          {petsLoading ? (
            <div className="text-center py-8">Loading pets...</div>
          ) : petsError || (petsData && !petsData.success) ? (
            <Card>
              <CardContent className="text-center py-8">
                <p className="text-red-500">Error loading pets: {petsError?.message || petsData?.message || 'Unknown error'}</p>
              </CardContent>
            </Card>
          ) : filteredPets.length === 0 ? (
            <Card>
              <CardContent className="text-center py-8">
                <PawPrint className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <p className="text-gray-500">No pets found matching your criteria.</p>
                <Button onClick={() => handleAddPet()} className="mt-4">
                  Add First Pet
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredPets.map((pet: Pet) => (
                <Card key={pet.petId} className="hover:shadow-md transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">{pet.name}</CardTitle>
                      <Badge variant={pet.lifeStatus === 'alive' ? 'default' : 'secondary'}>
                        {pet.lifeStatus}
                      </Badge>
                    </div>
                    <CardDescription>
                      {pet.species?.name || pet.species?.speciesName} • {pet.breed?.breedName} • {pet.gender}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div>
                        <span className="font-medium">DOB:</span> {pet.dateOfBirth ? new Date(pet.dateOfBirth).toLocaleDateString() : 'N/A'}
                      </div>
                      <div>
                        <span className="font-medium">Weight:</span> {pet.weight} kg
                      </div>
                      {pet.color && (
                        <div>
                          <span className="font-medium">Color:</span> {pet.color}
                        </div>
                      )}
                      {pet.microchipId && (
                        <div>
                          <span className="font-medium">Chip:</span> {pet.microchipId}
                        </div>
                      )}
                    </div>

                    {pet.client && (
                      <div className="border-t pt-3">
                        <div className="flex items-center gap-2 text-sm">
                          <User className="h-4 w-4" />
                          <span className="font-medium">{pet.client.firstName} {pet.client.lastName}</span>
                        </div>
                        {pet.client.phoneNumber && (
                          <div className="flex items-center gap-2 text-sm text-gray-600">
                            <Phone className="h-4 w-4" />
                            <span>{pet.client.phoneNumber}</span>
                          </div>
                        )}
                      </div>
                    )}
                    
                    <div className="flex gap-2 pt-2">
                      <Button variant="outline" size="sm" className="flex-1">
                        View Details
                      </Button>
                      <Button variant="outline" size="sm" className="flex-1">
                        Medical History
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        {/* By Clients View */}
        <TabsContent value="by-clients" className="space-y-4">
          {clientsLoading ? (
            <div className="text-center py-8">Loading clients...</div>
          ) : clientsError || (clientsData && !clientsData.success) ? (
            <Card>
              <CardContent className="text-center py-8">
                <p className="text-red-500">Error loading clients: {clientsError?.message || clientsData?.message || 'Unknown error'}</p>
              </CardContent>
            </Card>
          ) : clients.length === 0 ? (
            <Card>
              <CardContent className="text-center py-8">
                <User className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <p className="text-gray-500">No clients found.</p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {clients.map((client: Client) => {
                const clientPets = pets.filter((pet: Pet) => pet.clientId === client.clientId);
                return (
                  <Card key={client.clientId}>
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <div>
                          <CardTitle>{client.firstName} {client.lastName}</CardTitle>
                          <CardDescription className="flex items-center gap-4">
                            <span className="flex items-center gap-1">
                              <Mail className="h-4 w-4" />
                              {client.email}
                            </span>
                            <span className="flex items-center gap-1">
                              <Phone className="h-4 w-4" />
                              {client.phoneNumber}
                            </span>
                          </CardDescription>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline">
                            {clientPets.length} pet{clientPets.length !== 1 ? 's' : ''}
                          </Badge>
                          <Button
                            size="sm"
                            onClick={() => handleAddPet(client)}
                            className="flex items-center gap-1"
                          >
                            <Plus className="h-3 w-3" />
                            Add Pet
                          </Button>
                        </div>
                      </div>
                    </CardHeader>
                    {clientPets.length > 0 && (
                      <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                          {clientPets.map((pet: Pet) => (
                            <div key={pet.petId} className="border rounded-lg p-3">
                              <div className="flex items-center justify-between mb-2">
                                <h4 className="font-medium">{pet.name}</h4>
                                <Badge
                                  variant={pet.lifeStatus === 'alive' ? 'default' : 'secondary'}
                                  className="text-xs"
                                >
                                  {pet.lifeStatus}
                                </Badge>
                              </div>
                              <p className="text-sm text-gray-600">
                                {pet.species?.name || pet.species?.speciesName} • {pet.breed?.breedName} • {pet.dateOfBirth ? new Date(pet.dateOfBirth).toLocaleDateString() : 'N/A'}
                              </p>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    )}
                  </Card>
                );
              })}
            </div>
          )}
        </TabsContent>

        {/* Statistics View */}
        <TabsContent value="statistics" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-2">
                  <PawPrint className="h-8 w-8 text-blue-500" />
                  <div>
                    <p className="text-2xl font-bold">{pets.length}</p>
                    <p className="text-sm text-gray-600">Total Pets</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-2">
                  <User className="h-8 w-8 text-green-500" />
                  <div>
                    <p className="text-2xl font-bold">{clients.length}</p>
                    <p className="text-sm text-gray-600">Total Clients</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-2">
                  <Heart className="h-8 w-8 text-red-500" />
                  <div>
                    <p className="text-2xl font-bold">
                      {pets.filter((pet: Pet) => pet.lifeStatus === 'alive').length}
                    </p>
                    <p className="text-sm text-gray-600">Active Pets</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-2">
                  <Filter className="h-8 w-8 text-purple-500" />
                  <div>
                    <p className="text-2xl font-bold">{uniqueSpecies.length}</p>
                    <p className="text-sm text-gray-600">Species Types</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
          
          {/* Species breakdown */}
          <Card>
            <CardHeader>
              <CardTitle>Species Distribution</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {uniqueSpecies.map((species) => {
                  const count = pets.filter((pet: Pet) => pet.species?.name === species || pet.species?.speciesName === species).length;
                  const percentage = pets.length > 0 ? ((count / pets.length) * 100).toFixed(1) : '0';
                  return (
                    <div key={species} className="flex items-center justify-between">
                      <span className="font-medium">{species}</span>
                      <div className="flex items-center gap-2">
                        <div className="w-24 bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-500 h-2 rounded-full"
                            style={{ width: `${percentage}%` }}
                          />
                        </div>
                        <span className="text-sm text-gray-600">{count} ({percentage}%)</span>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Add Pet Modal */}
      {showAddPetModal && (
        <AddPetModal
          open={showAddPetModal}
          onOpenChange={setShowAddPetModal}
          onSuccess={handlePetAdded}
          preselectedClient={selectedClient || undefined}
        />
      )}
    </div>
  );
}
