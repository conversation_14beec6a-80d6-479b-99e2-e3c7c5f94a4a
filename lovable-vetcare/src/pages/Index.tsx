
import { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import {
  Activity,
  Users,
  Calendar,
  DollarSign,
  TrendingUp,
  TrendingDown,
  Clock,
  AlertCircle,
  CheckCircle,
  Heart,
  Stethoscope,
  PawPrint,
  Bell,
  ArrowRight
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { useSpring, animated } from "@react-spring/web";

// Enhanced stats with more detailed information
const stats = [
  {
    name: "Total Patients",
    value: "2,345",
    change: "+12.3%",
    icon: Users,
    trend: "up",
    color: "from-blue-500 to-blue-600",
    description: "Active patients this month",
    target: 2500,
    current: 2345
  },
  {
    name: "Appointments Today",
    value: "24",
    change: "+4.5%",
    icon: Calendar,
    trend: "up",
    color: "from-green-500 to-green-600",
    description: "Scheduled for today",
    target: 30,
    current: 24
  },
  {
    name: "Active Cases",
    value: "45",
    change: "-2.3%",
    icon: Activity,
    trend: "down",
    color: "from-orange-500 to-orange-600",
    description: "Ongoing treatments",
    target: 50,
    current: 45
  },
  {
    name: "Revenue",
    value: "$12,345",
    change: "+8.4%",
    icon: DollarSign,
    trend: "up",
    color: "from-purple-500 to-purple-600",
    description: "This month's earnings",
    target: 15000,
    current: 12345
  },
];

// Recent activities data
const recentActivities = [
  {
    id: 1,
    type: "appointment",
    title: "New appointment scheduled",
    description: "Max (Golden Retriever) - Vaccination",
    time: "2 minutes ago",
    icon: Calendar,
    color: "text-blue-500"
  },
  {
    id: 2,
    type: "treatment",
    title: "Treatment completed",
    description: "Luna (Persian Cat) - Dental cleaning",
    time: "15 minutes ago",
    icon: CheckCircle,
    color: "text-green-500"
  },
  {
    id: 3,
    type: "alert",
    title: "Medication reminder",
    description: "Charlie needs follow-up medication",
    time: "1 hour ago",
    icon: AlertCircle,
    color: "text-orange-500"
  },
  {
    id: 4,
    type: "payment",
    title: "Payment received",
    description: "$250 from Sarah Johnson",
    time: "2 hours ago",
    icon: DollarSign,
    color: "text-purple-500"
  }
];

// Upcoming appointments
const upcomingAppointments = [
  {
    id: 1,
    petName: "Buddy",
    ownerName: "John Smith",
    time: "10:30 AM",
    service: "Routine Checkup",
    status: "confirmed"
  },
  {
    id: 2,
    petName: "Whiskers",
    ownerName: "Emily Davis",
    time: "11:15 AM",
    service: "Vaccination",
    status: "pending"
  },
  {
    id: 3,
    petName: "Rocky",
    ownerName: "Mike Wilson",
    time: "2:00 PM",
    service: "Surgery Consultation",
    status: "confirmed"
  }
];

// Animated counter hook
const useAnimatedCounter = (end: number, duration: number = 2000) => {
  const [count, setCount] = useState(0);

  useEffect(() => {
    let startTime: number;
    let animationFrame: number;

    const animate = (currentTime: number) => {
      if (!startTime) startTime = currentTime;
      const progress = Math.min((currentTime - startTime) / duration, 1);

      setCount(Math.floor(progress * end));

      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate);
      }
    };

    animationFrame = requestAnimationFrame(animate);

    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };
  }, [end, duration]);

  return count;
};

// Enhanced stat card component
const StatCard = ({ stat, index }: { stat: any; index: number }) => {
  const [ref, inView] = useInView({
    threshold: 0.1,
    triggerOnce: true
  });

  const animatedValue = useAnimatedCounter(
    parseInt(stat.value.replace(/[^0-9]/g, '')),
    2000
  );

  const springProps = useSpring({
    from: { opacity: 0, transform: 'translateY(50px)' },
    to: {
      opacity: inView ? 1 : 0,
      transform: inView ? 'translateY(0px)' : 'translateY(50px)'
    },
    delay: index * 100,
    config: { tension: 280, friction: 60 }
  });

  const progressPercentage = (stat.current / stat.target) * 100;

  return (
    <animated.div ref={ref} style={springProps}>
      <Card className="relative overflow-hidden backdrop-blur-md bg-white/90 dark:bg-gray-900/90 border-0 shadow-lg hover:shadow-xl transition-all duration-300 group">
        <div className={`absolute inset-0 bg-gradient-to-br ${stat.color} opacity-5 group-hover:opacity-10 transition-opacity duration-300`} />

        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <div>
            <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">
              {stat.name}
            </CardTitle>
            <CardDescription className="text-xs text-gray-500">
              {stat.description}
            </CardDescription>
          </div>
          <div className={`p-2 rounded-lg bg-gradient-to-br ${stat.color}`}>
            <stat.icon className="h-4 w-4 text-white" />
          </div>
        </CardHeader>

        <CardContent className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="text-2xl font-bold">
              {stat.name === "Revenue" ? `$${animatedValue.toLocaleString()}` : animatedValue.toLocaleString()}
            </div>
            <div className={`flex items-center text-xs font-medium ${
              stat.trend === "up" ? "text-green-600" : "text-red-600"
            }`}>
              {stat.trend === "up" ? <TrendingUp className="h-3 w-3 mr-1" /> : <TrendingDown className="h-3 w-3 mr-1" />}
              {stat.change}
            </div>
          </div>

          <div className="space-y-1">
            <div className="flex justify-between text-xs text-gray-500">
              <span>Progress</span>
              <span>{Math.round(progressPercentage)}%</span>
            </div>
            <Progress value={progressPercentage} className="h-2" />
          </div>
        </CardContent>
      </Card>
    </animated.div>
  );
};

const Index = () => {
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
      <div className="p-8">
        {/* Header Section */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Dashboard
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-2">
                Welcome back! Here's what's happening at your clinic today.
              </p>
            </div>
            <div className="mt-4 md:mt-0 text-right">
              <div className="text-2xl font-semibold text-gray-800 dark:text-gray-200">
                {currentTime.toLocaleTimeString()}
              </div>
              <div className="text-sm text-gray-500">
                {currentTime.toLocaleDateString('en-US', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </div>
            </div>
          </div>
        </motion.div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => (
            <StatCard key={stat.name} stat={stat} index={index} />
          ))}
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Recent Activities */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="lg:col-span-2"
          >
            <Card className="backdrop-blur-md bg-white/90 dark:bg-gray-900/90 border-0 shadow-lg">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <Activity className="h-5 w-5 text-blue-500" />
                      Recent Activities
                    </CardTitle>
                    <CardDescription>
                      Latest updates from your clinic
                    </CardDescription>
                  </div>
                  <Button variant="outline" size="sm">
                    View All
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <AnimatePresence>
                    {recentActivities.map((activity, index) => (
                      <motion.div
                        key={activity.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        transition={{ duration: 0.3, delay: index * 0.1 }}
                        className="flex items-start gap-4 p-4 rounded-lg bg-gray-50 dark:bg-gray-800/50 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200"
                      >
                        <div className={`p-2 rounded-full bg-white dark:bg-gray-700 shadow-sm`}>
                          <activity.icon className={`h-4 w-4 ${activity.color}`} />
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="font-medium text-gray-900 dark:text-gray-100">
                            {activity.title}
                          </p>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {activity.description}
                          </p>
                          <p className="text-xs text-gray-500 mt-1">
                            {activity.time}
                          </p>
                        </div>
                      </motion.div>
                    ))}
                  </AnimatePresence>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Upcoming Appointments */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <Card className="backdrop-blur-md bg-white/90 dark:bg-gray-900/90 border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5 text-green-500" />
                  Today's Schedule
                </CardTitle>
                <CardDescription>
                  Upcoming appointments
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {upcomingAppointments.map((appointment, index) => (
                    <motion.div
                      key={appointment.id}
                      initial={{ opacity: 0, scale: 0.95 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.3, delay: index * 0.1 }}
                      className="p-4 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600 transition-colors duration-200"
                    >
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <PawPrint className="h-4 w-4 text-blue-500" />
                          <span className="font-medium text-gray-900 dark:text-gray-100">
                            {appointment.petName}
                          </span>
                        </div>
                        <Badge
                          variant={appointment.status === 'confirmed' ? 'default' : 'secondary'}
                          className="text-xs"
                        >
                          {appointment.status}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {appointment.ownerName}
                      </p>
                      <p className="text-sm text-gray-500 mt-1">
                        {appointment.time} • {appointment.service}
                      </p>
                    </motion.div>
                  ))}
                </div>
                <Button className="w-full mt-4" variant="outline">
                  <Calendar className="h-4 w-4 mr-2" />
                  View Full Schedule
                </Button>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="mt-8"
        >
          <Card className="backdrop-blur-md bg-white/90 dark:bg-gray-900/90 border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Heart className="h-5 w-5 text-red-500" />
                Quick Actions
              </CardTitle>
              <CardDescription>
                Common tasks and shortcuts
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {[
                  { icon: Calendar, label: "New Appointment", color: "from-blue-500 to-blue-600" },
                  { icon: Users, label: "Add Client", color: "from-green-500 to-green-600" },
                  { icon: PawPrint, label: "Register Pet", color: "from-purple-500 to-purple-600" },
                  { icon: Stethoscope, label: "Start Consultation", color: "from-orange-500 to-orange-600" }
                ].map((action, index) => (
                  <motion.div
                    key={action.label}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="cursor-pointer"
                  >
                    <div className={`p-6 rounded-lg bg-gradient-to-br ${action.color} text-white text-center hover:shadow-lg transition-all duration-300`}>
                      <action.icon className="h-8 w-8 mx-auto mb-2" />
                      <p className="text-sm font-medium">{action.label}</p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
};

export default Index;
