import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { 
  Search, 
  Plus, 
  FileText, 
  Calendar,
  User,
  PawPrint,
  Stethoscope,
  Syringe,
  Scissors,
  FlaskConical
} from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { useApiWithClinic } from "@/hooks/useApiWithClinic";

interface MedicalRecord {
  healthRecordId: number;
  petId: number;
  petName: string;
  clientName: string;
  recordType: string;
  diagnosis: string;
  treatment: string;
  medications: string;
  notes: string;
  performedBy: string;
  recordDate: string;
  appointmentId?: number;
  status: string;
}

export default function MedicalRecords() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedType, setSelectedType] = useState<string>("all");
  const [activeTab, setActiveTab] = useState("all-records");

  const { get, clinicId } = useApiWithClinic();

  // Fetch medical records
  const { data: recordsData, isLoading } = useQuery({
    queryKey: ['medical-records', clinicId],
    queryFn: () => get('/health-records'),
    enabled: !!clinicId
  });

  const records = recordsData?.data || [];

  // Filter records
  const filteredRecords = records.filter((record: MedicalRecord) => {
    const matchesSearch = record.petName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         record.clientName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         record.diagnosis?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         record.treatment?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesType = selectedType === "all" || record.recordType === selectedType;
    
    return matchesSearch && matchesType;
  });

  const getRecordIcon = (type: string) => {
    switch (type) {
      case 'consultation': return <Stethoscope className="h-4 w-4" />;
      case 'vaccination': return <Syringe className="h-4 w-4" />;
      case 'surgery': return <Scissors className="h-4 w-4" />;
      case 'laboratory': return <FlaskConical className="h-4 w-4" />;
      default: return <FileText className="h-4 w-4" />;
    }
  };

  const getRecordColor = (type: string) => {
    switch (type) {
      case 'consultation': return 'bg-blue-100 text-blue-800';
      case 'vaccination': return 'bg-green-100 text-green-800';
      case 'surgery': return 'bg-red-100 text-red-800';
      case 'laboratory': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const recordTypes = ['consultation', 'vaccination', 'surgery', 'laboratory'];

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <FileText className="h-6 w-6" />
          <h1 className="text-3xl font-bold">Medical Records</h1>
        </div>
        <Button className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          New Record
        </Button>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search by pet name, client, diagnosis, or treatment..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <select
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value)}
              className="px-3 py-2 border rounded-md"
            >
              <option value="all">All Types</option>
              {recordTypes.map((type) => (
                <option key={type} value={type}>
                  {type.charAt(0).toUpperCase() + type.slice(1)}
                </option>
              ))}
            </select>
          </div>
        </CardContent>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="all-records">All Records ({filteredRecords.length})</TabsTrigger>
          <TabsTrigger value="recent">Recent</TabsTrigger>
          <TabsTrigger value="by-type">By Type</TabsTrigger>
          <TabsTrigger value="statistics">Statistics</TabsTrigger>
        </TabsList>

        {/* All Records */}
        <TabsContent value="all-records" className="space-y-4">
          {isLoading ? (
            <div className="text-center py-8">Loading medical records...</div>
          ) : filteredRecords.length === 0 ? (
            <Card>
              <CardContent className="text-center py-8">
                <FileText className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <p className="text-gray-500">No medical records found.</p>
                <Button className="mt-4">
                  Create First Record
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {filteredRecords.map((record: MedicalRecord) => (
                <Card key={record.healthRecordId} className="hover:shadow-md transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className={`p-2 rounded-lg ${getRecordColor(record.recordType)}`}>
                          {getRecordIcon(record.recordType)}
                        </div>
                        <div>
                          <CardTitle className="text-lg">{record.petName}</CardTitle>
                          <CardDescription className="flex items-center gap-4">
                            <span className="flex items-center gap-1">
                              <User className="h-4 w-4" />
                              {record.clientName}
                            </span>
                            <span className="flex items-center gap-1">
                              <Calendar className="h-4 w-4" />
                              {new Date(record.recordDate).toLocaleDateString()}
                            </span>
                          </CardDescription>
                        </div>
                      </div>
                      <Badge className={getRecordColor(record.recordType)}>
                        {record.recordType}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <h4 className="font-medium text-sm text-gray-600 mb-1">Diagnosis</h4>
                        <p className="text-sm">{record.diagnosis || 'Not specified'}</p>
                      </div>
                      <div>
                        <h4 className="font-medium text-sm text-gray-600 mb-1">Treatment</h4>
                        <p className="text-sm">{record.treatment || 'Not specified'}</p>
                      </div>
                      {record.medications && (
                        <div>
                          <h4 className="font-medium text-sm text-gray-600 mb-1">Medications</h4>
                          <p className="text-sm">{record.medications}</p>
                        </div>
                      )}
                      <div>
                        <h4 className="font-medium text-sm text-gray-600 mb-1">Performed By</h4>
                        <p className="text-sm">{record.performedBy}</p>
                      </div>
                    </div>
                    
                    {record.notes && (
                      <div>
                        <h4 className="font-medium text-sm text-gray-600 mb-1">Notes</h4>
                        <p className="text-sm text-gray-700">{record.notes}</p>
                      </div>
                    )}
                    
                    <div className="flex gap-2 pt-2 border-t">
                      <Button variant="outline" size="sm">
                        View Details
                      </Button>
                      <Button variant="outline" size="sm">
                        Edit Record
                      </Button>
                      {record.appointmentId && (
                        <Button variant="outline" size="sm">
                          View Appointment
                        </Button>
                      )}
                      <Button variant="outline" size="sm">
                        Print Record
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        {/* Recent Records */}
        <TabsContent value="recent" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Medical Records</CardTitle>
              <CardDescription>Records from the last 30 days</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-center text-gray-500 py-8">Recent records view coming soon...</p>
            </CardContent>
          </Card>
        </TabsContent>

        {/* By Type */}
        <TabsContent value="by-type" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {recordTypes.map((type) => {
              const typeRecords = records.filter((record: MedicalRecord) => record.recordType === type);
              return (
                <Card key={type}>
                  <CardHeader>
                    <div className="flex items-center gap-2">
                      {getRecordIcon(type)}
                      <CardTitle className="capitalize">{type}</CardTitle>
                      <Badge variant="outline">{typeRecords.length}</Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    {typeRecords.length === 0 ? (
                      <p className="text-gray-500 text-sm">No {type} records found</p>
                    ) : (
                      <div className="space-y-2">
                        {typeRecords.slice(0, 3).map((record: MedicalRecord) => (
                          <div key={record.healthRecordId} className="border-l-2 border-gray-200 pl-3">
                            <p className="font-medium text-sm">{record.petName}</p>
                            <p className="text-xs text-gray-600">{record.clientName}</p>
                            <p className="text-xs text-gray-500">
                              {new Date(record.recordDate).toLocaleDateString()}
                            </p>
                          </div>
                        ))}
                        {typeRecords.length > 3 && (
                          <p className="text-xs text-gray-500">
                            +{typeRecords.length - 3} more records
                          </p>
                        )}
                      </div>
                    )}
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>

        {/* Statistics */}
        <TabsContent value="statistics" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-2">
                  <FileText className="h-8 w-8 text-blue-500" />
                  <div>
                    <p className="text-2xl font-bold">{records.length}</p>
                    <p className="text-sm text-gray-600">Total Records</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            {recordTypes.map((type) => {
              const count = records.filter((record: MedicalRecord) => record.recordType === type).length;
              return (
                <Card key={type}>
                  <CardContent className="p-6">
                    <div className="flex items-center gap-2">
                      {getRecordIcon(type)}
                      <div>
                        <p className="text-2xl font-bold">{count}</p>
                        <p className="text-sm text-gray-600 capitalize">{type}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
