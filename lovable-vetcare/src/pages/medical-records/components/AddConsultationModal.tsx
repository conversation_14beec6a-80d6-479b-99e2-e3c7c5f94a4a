import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import SearchOwner from "@/components/common/SearchOwner";
import { Client, Pet } from "@/store/types";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { getPets } from "@/services/pets";
import { useApiWithClinic } from "@/hooks/useApiWithClinic";
import { useToast } from "@/components/ui/use-toast";

interface AddConsultationModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const formSchema = z.object({
  petId: z.string().min(1, "Pet is required"),
  ownerId: z.string().min(1, "Owner is required"),
  consultationDate: z.string().min(1, "Date is required"),
  symptoms: z.string().min(1, "Symptoms are required"),
  diagnosis: z.string().min(1, "Diagnosis is required"),
  treatment: z.string().min(1, "Treatment is required"),
  notes: z.string().optional(),
});

const AddConsultationModal = ({ open, onOpenChange }: AddConsultationModalProps) => {
  const [selectedOwner, setSelectedOwner] = useState<Client | null>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { post } = useApiWithClinic();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      petId: "",
      ownerId: "",
      consultationDate: "",
      symptoms: "",
      diagnosis: "",
      treatment: "",
      notes: "",
    },
  });

  const { data: petsData } = useQuery({
    queryKey: ['pets', selectedOwner?.clientId],
    queryFn: () => getPets({ owner: selectedOwner?.clientId }),
    enabled: !!selectedOwner?.clientId,
  });

  const pets = petsData?.data || [];

  const createConsultationMutation = useMutation({
    mutationFn: async (data: z.infer<typeof formSchema>) => {
      return await post('/health-records', {
        petId: parseInt(data.petId),
        recordType: 'consultation',
        date: data.consultationDate,
        description: data.symptoms,
        diagnosis: data.diagnosis,
        treatment: data.treatment,
        notes: data.notes
      });
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Consultation record created successfully",
      });
      queryClient.invalidateQueries({ queryKey: ['medical-records'] });
      form.reset();
      setSelectedOwner(null);
      onOpenChange(false);
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to create consultation record",
        variant: "destructive",
      });
    }
  });

  const onSubmit = (data: z.infer<typeof formSchema>) => {
    createConsultationMutation.mutate(data);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Add New Consultation</DialogTitle>
        </DialogHeader>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="ownerId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Owner</FormLabel>
                  <FormControl>
                    <SearchOwner 
                      onSelectOwner={(owner) => {
                        setSelectedOwner(owner);
                        field.onChange(owner._id);
                      }}
                      selectedOwnerId={field.value}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            {/* More form fields */}
            
            <div className="flex justify-end gap-2">
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                Cancel
              </Button>
              <Button type="submit">Save Consultation</Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default AddConsultationModal;