import React from 'react';
import { useParams } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import FollowUpDashboard from '@/components/appointments/FollowUpDashboard';

const FollowUps: React.FC = () => {
  const navigate = useNavigate();
  const { clinicId } = useParams();

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => navigate('/appointments')}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Appointments
        </Button>
      </div>

      {/* Follow-up Dashboard */}
      <FollowUpDashboard clinicId={clinicId} />
    </div>
  );
};

export default FollowUps;
