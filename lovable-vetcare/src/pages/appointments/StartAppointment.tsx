import React, { useState, useEffect, useMemo } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { format } from 'date-fns';
import { ArrowLeft, Save, Plus, Trash2, Wand2, FileText, Stethoscope, Users, ExternalLink, Brain, Calendar, CheckCircle, Clock, User, Sparkles } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/components/ui/use-toast';
import { getAppointmentById } from '@/services/appointments';
import { getServices, createService } from '@/services/services';
import { getServicesByAppointmentTypes, calculateAppointmentCharges } from '@/services/appointmentTypeService';
import { getCategoryServices } from '@/services/appointmentCategories';
import { api } from '@/services/api';
import { cn } from '@/lib/utils';
import TaskAssignment from '@/components/appointments/TaskAssignment';
import AISuggestions from '@/components/appointments/AISuggestions';
import TaskStatus from '@/components/appointments/TaskStatus';
import AppointmentComplete from '@/components/appointments/AppointmentComplete';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';

interface Service {
  serviceId: number;
  serviceName: string;
  description: string;
  category: string;
  defaultPrice: number;
  currency: string;
  estimatedDuration: number;
}

interface AppointmentService {
  serviceId: number;
  serviceName: string;
  price: number;
  currency: string;
  notes?: string;
  assignedStaffId?: number;
  assignedStaffName?: string;
  isExternalReferral?: boolean;
  externalClinic?: {
    name: string;
    contact: string;
    address?: string;
  };
}

interface AppointmentNote {
  category: string;
  content: string;
  isAIGenerated: boolean;
}

const StartAppointment: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const [selectedServices, setSelectedServices] = useState<AppointmentService[]>([]);
  const [appointmentNotes, setAppointmentNotes] = useState<AppointmentNote[]>([]);
  const [localAppointmentCategories, setLocalAppointmentCategories] = useState<any[]>([]);
  const [selectedAppointmentType, setSelectedAppointmentType] = useState<number | null>(null);
  const [newServiceData, setNewServiceData] = useState({
    serviceName: '',
    description: '',
    defaultPrice: 0,
    estimatedDuration: 30
  });
  const [showCreateServiceModal, setShowCreateServiceModal] = useState(false);
  const [showAddServiceModal, setShowAddServiceModal] = useState(false);
  const [availableServices, setAvailableServices] = useState<any[]>([]);
  const [selectedServiceToAdd, setSelectedServiceToAdd] = useState<any>(null);
  const [isGeneratingNotes, setIsGeneratingNotes] = useState(false);
  const [generatingNotesFor, setGeneratingNotesFor] = useState<string>('');
  const [appointmentTypes, setAppointmentTypes] = useState<string[]>([]);
  const [showStaffAssignModal, setShowStaffAssignModal] = useState(false);
  const [selectedServiceForAssignment, setSelectedServiceForAssignment] = useState<number | null>(null);
  const [showExternalReferralModal, setShowExternalReferralModal] = useState(false);
  const [externalClinicData, setExternalClinicData] = useState({
    name: '',
    contact: '',
    address: ''
  });
  const [showCompletionOptions, setShowCompletionOptions] = useState(false);
  const [activeTab, setActiveTab] = useState('assignment');
  const [workflowStep, setWorkflowStep] = useState<'assignment' | 'services' | 'ai-suggestions' | 'completion'>('assignment');
  const [appointmentTypeServices, setAppointmentTypeServices] = useState<any>({});
  const [totalCharges, setTotalCharges] = useState<number>(0);
  const [selectedCategory, setSelectedCategory] = useState<string>('consultation');
  const [serviceNotes, setServiceNotes] = useState<{[key: string]: string}>({});
  const [categoryNotes, setCategoryNotes] = useState<{[key: string]: string}>({});

  // Fetch appointment data
  const { data: appointmentResponse, isLoading } = useQuery({
    queryKey: ['appointment', id],
    queryFn: () => getAppointmentById(parseInt(id!)),
    enabled: !!id
  });

  const appointment = appointmentResponse?.data;

  // Fetch services by appointment types
  const { data: servicesResponse, refetch: refetchServices } = useQuery({
    queryKey: ['services', appointmentTypes, appointment?.clinicId],
    queryFn: () => getServices({
      appointmentTypeIds: appointmentTypes.map(id => parseInt(id)),
      clinicId: appointment?.clinicId,
      limit: 100
    }),
    enabled: !!appointmentTypes.length && !!appointment?.clinicId
  });

  const services = servicesResponse?.data?.services || [];

  // Fetch services by appointment types
  const { data: appointmentTypeServicesResponse } = useQuery({
    queryKey: ['appointmentTypeServices', appointmentTypes, appointment?.clinicId],
    queryFn: () => {
      if (!appointmentTypes.length) return null;
      return getServicesByAppointmentTypes(
        appointmentTypes.map(id => parseInt(id)),
        appointment?.clinicId
      );
    },
    enabled: !!appointmentTypes.length && !!appointment?.clinicId
  });

  const servicesByType = appointmentTypeServicesResponse?.servicesByType || {};

  // Fetch clinic staff for assignments
  const { data: staffResponse } = useQuery({
    queryKey: ['clinic-staff', appointment?.clinicId],
    queryFn: async () => {
      if (!appointment?.clinicId) return { data: { data: [] } };
      return await api.get(`/staff?clinicId=${appointment.clinicId}&status=1&limit=100`);
    },
    enabled: !!appointment?.clinicId
  });

  const clinicStaff = staffResponse?.data?.data || [];

  // Add service to appointment mutation
  const addServiceMutation = useMutation({
    mutationFn: async (serviceData: any) => {
      const response = await api.post(`/appointments/${id}/services`, serviceData);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['appointment', id] });
      setShowAddServiceModal(false);
      setSelectedServiceToAdd(null);
      toast({
        title: "Success",
        description: "Service added to appointment successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to add service to appointment",
        variant: "destructive",
      });
    }
  });

  // Update service status mutation
  const updateServiceStatusMutation = useMutation({
    mutationFn: async ({ categoryId, serviceId, status }: { categoryId: number; serviceId: number; status: string }) => {
      const response = await api.put(`/appointments/${id}/categories/${categoryId}/services/${serviceId}`, {
        status,
        isCompleted: status === 'completed',
        clinicId: appointment?.clinicId
      });
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['appointment', id] });
      toast({
        title: "Success",
        description: "Service status updated successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update service status",
        variant: "destructive",
      });
    }
  });

  // Update category status mutation
  const updateCategoryStatusMutation = useMutation({
    mutationFn: async ({ categoryId, status }: { categoryId: number; status: string }) => {
      const response = await api.put(`/appointments/${id}/categories/${categoryId}`, {
        categoryStatus: status,
        isCompleted: status === 'completed',
        clinicId: appointment?.clinicId
      });
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['appointment', id] });
      toast({
        title: "Success",
        description: "Category status updated successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update category status",
        variant: "destructive",
      });
    }
  });

  // Calculate comprehensive completion statistics
  const completionStats = useMemo(() => {
    if (!appointment?.appointmentCategories) {
      return {
        // Category stats
        totalCategories: 0,
        completedCategories: 0,
        inProgressCategories: 0,
        scheduledCategories: 0,
        categoryCompletionPercentage: 0,

        // Service stats
        totalServices: 0,
        completedServices: 0,
        inProgressServices: 0,
        scheduledServices: 0,
        serviceCompletionPercentage: 0,

        // Overall stats
        overallCompletionPercentage: 0,
        isFullyCompleted: false,

        // Duration stats
        totalEstimatedDuration: 0,
        totalActualDuration: 0,

        // Category breakdown
        categoryBreakdown: []
      };
    }

    const categories = appointment.appointmentCategories;
    const totalCategories = categories.length;

    // Calculate category-level statistics
    let completedCategories = 0;
    let inProgressCategories = 0;
    let scheduledCategories = 0;

    // Calculate service-level statistics
    let totalServices = 0;
    let completedServices = 0;
    let inProgressServices = 0;
    let scheduledServices = 0;

    let totalEstimatedDuration = 0;
    let totalActualDuration = 0;

    const categoryBreakdown = categories.map(category => {
      const services = category.categoryServices || [];
      const categoryTotalServices = services.length;
      const categoryCompletedServices = services.filter(s => s.status === 'completed' || s.isCompleted).length;
      const categoryInProgressServices = services.filter(s => s.status === 'in_progress').length;
      const categoryScheduledServices = services.filter(s => s.status === 'pending' || s.status === 'scheduled' || (!s.status && !s.isCompleted)).length;

      // Calculate category completion percentage
      const categoryCompletionPercentage = categoryTotalServices > 0
        ? Math.round((categoryCompletedServices / categoryTotalServices) * 100)
        : 0;

      // Determine category status
      let categoryStatus = 'scheduled';
      if (categoryCompletedServices === categoryTotalServices && categoryTotalServices > 0) {
        categoryStatus = 'completed';
        completedCategories++;
      } else if (categoryCompletedServices > 0 || categoryInProgressServices > 0) {
        categoryStatus = 'in_progress';
        inProgressCategories++;
      } else {
        scheduledCategories++;
      }

      // Add to totals
      totalServices += categoryTotalServices;
      completedServices += categoryCompletedServices;
      inProgressServices += categoryInProgressServices;
      scheduledServices += categoryScheduledServices;

      totalEstimatedDuration += category.estimatedDuration || 0;
      totalActualDuration += services.reduce((sum, service) => sum + (service.duration || service.estimatedDuration || 0), 0);

      return {
        categoryId: category.appointmentCategoryId,
        categoryName: category.categoryName,
        totalServices: categoryTotalServices,
        completedServices: categoryCompletedServices,
        inProgressServices: categoryInProgressServices,
        scheduledServices: categoryScheduledServices,
        completionPercentage: categoryCompletionPercentage,
        status: categoryStatus
      };
    });

    // Calculate overall percentages
    const categoryCompletionPercentage = totalCategories > 0
      ? Math.round((completedCategories / totalCategories) * 100)
      : 0;

    const serviceCompletionPercentage = totalServices > 0
      ? Math.round((completedServices / totalServices) * 100)
      : 0;

    // Overall completion is weighted average of category and service completion
    const overallCompletionPercentage = totalCategories > 0 && totalServices > 0
      ? Math.round((categoryCompletionPercentage * 0.3) + (serviceCompletionPercentage * 0.7))
      : Math.max(categoryCompletionPercentage, serviceCompletionPercentage);

    return {
      // Category stats
      totalCategories,
      completedCategories,
      inProgressCategories,
      scheduledCategories,
      categoryCompletionPercentage,

      // Service stats
      totalServices,
      completedServices,
      inProgressServices,
      scheduledServices,
      serviceCompletionPercentage,

      // Overall stats
      overallCompletionPercentage,
      isFullyCompleted: completedCategories === totalCategories && totalCategories > 0,

      // Duration stats
      totalEstimatedDuration,
      totalActualDuration,

      // Category breakdown
      categoryBreakdown
    };
  }, [appointment]);

  // Extract appointment categories and set initial category when appointment loads
  useEffect(() => {
    if (appointment?.appointmentCategories && appointment.appointmentCategories.length > 0) {
      // Initialize local appointment categories with the current data
      setLocalAppointmentCategories([...appointment.appointmentCategories]);

      // Use appointment categories from the new structure
      const firstCategory = appointment.appointmentCategories[0];
      const categoryName = firstCategory.categoryName || 'Consultation';

      // Map category name to service category
      const categoryToServiceCategory: { [key: string]: string } = {
        'Vaccination': 'vaccination',
        'Consultation': 'consultation',
        'Laboratory': 'laboratory',
        'Surgery': 'surgery',
        'Grooming': 'grooming',
        'Dental': 'dental',
        'Emergency': 'emergency',
        'Wellness Check': 'wellness',
        'Physical Therapy': 'therapy',
        'X-ray/Imaging': 'imaging',
        'Behavioral Consultation': 'behavioral',
        'Nutrition Consultation': 'nutrition',
        'Day Care': 'daycare',
        'Boarding': 'boarding'
      };

      const category = categoryToServiceCategory[categoryName] || 'consultation';
      setSelectedCategory(category);
    } else if (appointment?.appointmentTypes) {
      // Fallback to old appointment types structure
      const types = appointment.appointmentTypes.map((at: any) =>
        (at.appointmentTypeId || at.categoryId)?.toString()
      ).filter(Boolean);
      setAppointmentTypes(types);

      if (appointment.appointmentTypes.length > 0) {
        const firstType = appointment.appointmentTypes[0];
        const typeName = firstType.categoryName || firstType.name || 'consultation';

        const typeToCategory: { [key: string]: string } = {
          'Vaccination': 'vaccination',
          'Consultation': 'consultation',
          'Laboratory': 'laboratory',
          'Surgery': 'surgery',
          'Grooming': 'grooming',
          'Dental': 'dental',
          'Emergency': 'emergency',
          'Wellness Check': 'wellness',
          'Physical Therapy': 'therapy',
          'X-ray/Imaging': 'imaging',
          'Behavioral Consultation': 'behavioral',
          'Nutrition Consultation': 'nutrition'
        };

        const category = typeToCategory[typeName] || 'consultation';
        setSelectedCategory(category);
      }
    }
  }, [appointment]);

  // Update appointment status to in_progress when component mounts
  useEffect(() => {
    if (appointment && appointment.status === 'scheduled') {
      const updateStatus = async () => {
        try {
          await api.put(`/appointments/${id}`, {
            status: 'in_progress',
            clinicId: appointment.clinicId
          });
          queryClient.invalidateQueries({ queryKey: ['appointment', id] });
        } catch (error) {
          console.error('Failed to update appointment status:', error);
        }
      };
      updateStatus();
    }
  }, [appointment, id, queryClient]);

  // Service categories based on appointment types
  const serviceCategories = [
    'consultation', 'vaccination', 'laboratory', 'surgery', 'grooming',
    'dental', 'emergency', 'medication', 'therapy', 'imaging', 'other'
  ];

  // Fetch available services for a category
  const fetchAvailableServices = async (appointmentCategoryId: number) => {
    try {
      const response = await getCategoryServices(appointmentCategoryId, {
        clinicId: appointment?.clinicId,
        isActive: true
      });

      if (response.success) {
        setAvailableServices(response.data);
      } else {
        setAvailableServices([]);
      }
    } catch (error) {
      console.error('Failed to fetch available services:', error);
      setAvailableServices([]);
    }
  };

  // Handle adding existing service to appointment
  const handleAddExistingService = (service: any) => {
    if (!selectedAppointmentType) {
      toast({
        title: "Error",
        description: "No category selected",
        variant: "destructive",
      });
      return;
    }

    const serviceData = {
      appointmentCategoryId: selectedAppointmentType,
      categoryServiceId: service.categoryServiceId,
      price: service.defaultPrice,
      notes: '',
      clinicId: appointment?.clinicId
    };

    addServiceMutation.mutate(serviceData);
  };

  // Handle service status update with specific status
  const handleServiceStatusUpdate = (categoryId: number, serviceId: number, newStatus: string) => {
    updateServiceStatusMutation.mutate({ categoryId, serviceId, status: newStatus });
  };

  // Handle category status update with specific status
  const handleCategoryStatusUpdate = (categoryId: number, newStatus: string) => {
    updateCategoryStatusMutation.mutate({ categoryId, status: newStatus });
  };

  // Get status color for badges (dark mode compatible)
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 border-green-200 dark:border-green-700';
      case 'in_progress':
        return 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 border-blue-200 dark:border-blue-700';
      case 'scheduled':
        return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300 border-yellow-200 dark:border-yellow-700';
      default:
        return 'bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-300 border-gray-200 dark:border-gray-600';
    }
  };

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-3 w-3" />;
      case 'in_progress':
        return <Clock className="h-3 w-3" />;
      case 'scheduled':
        return <Calendar className="h-3 w-3" />;
      default:
        return <Calendar className="h-3 w-3" />;
    }
  };

  // AI Note Generation
  const generateServiceNotes = async (service: any) => {
    const prompt = `Generate professional veterinary service notes for:
Service: ${service.categoryServiceName}
Description: ${service.description || 'No description provided'}
Duration: ${service.estimatedDuration || 'Not specified'} minutes
Price: ${service.currency || 'KES'} ${service.price || 0}

Please provide concise, professional notes that a veterinarian would write during or after performing this service.`;

    // Simulate AI generation (replace with actual AI service)
    return new Promise<string>((resolve) => {
      setTimeout(() => {
        resolve(`${service.categoryServiceName} completed successfully. Patient responded well to treatment. No adverse reactions observed. Recommended follow-up as needed.`);
      }, 1500);
    });
  };

  const generateCategoryNotes = async (category: any) => {
    const services = category.categoryServices || [];
    const serviceNames = services.map((s: any) => s.categoryServiceName).join(', ');
    const serviceNotes = services.map((s: any) => s.notes).filter(Boolean).join(' ');

    const prompt = `Generate a summary for the veterinary category "${category.categoryName}" based on:
Services performed: ${serviceNames}
Individual service notes: ${serviceNotes}
Staff assigned: ${category.staffAssignedName || 'Not assigned'}

Please provide a comprehensive category summary.`;

    // Simulate AI generation (replace with actual AI service)
    return new Promise<string>((resolve) => {
      setTimeout(() => {
        resolve(`${category.categoryName} category completed. All ${services.length} service(s) performed according to protocol. Patient showed good cooperation throughout the procedures. Overall assessment: satisfactory.`);
      }, 2000);
    });
  };

  // Note: Service creation is now handled locally and saved when user clicks "Save Progress"

  const handleAddService = (service: Service) => {
    const existingService = selectedServices.find(s => s.serviceId === service.serviceId);
    if (existingService) {
      toast({
        title: "Service already added",
        description: "This service is already in the appointment",
        variant: "destructive",
      });
      return;
    }

    const appointmentService: AppointmentService = {
      serviceId: service.serviceId,
      serviceName: service.serviceName,
      price: service.defaultPrice,
      currency: service.currency,
      notes: ''
    };

    const newServices = [...selectedServices, appointmentService];
    setSelectedServices(newServices);

    // Calculate total charges
    const total = newServices.reduce((sum, s) => sum + s.price, 0);
    setTotalCharges(total);

    toast({
      title: "Service Added",
      description: `${service.serviceName} has been added to the appointment`,
    });
  };

  const handleRemoveSelectedService = (serviceId: number) => {
    const newServices = selectedServices.filter(s => s.serviceId !== serviceId);
    setSelectedServices(newServices);

    // Recalculate total charges
    const total = newServices.reduce((sum, s) => sum + s.price, 0);
    setTotalCharges(total);
  };

  const handleUpdateServiceNotes = async (categoryId: number, serviceId: number, notes: string) => {
    try {
      await api.put(`/appointments/${id}/categories/${categoryId}/services/${serviceId}`, {
        notes,
        clinicId: appointment?.clinicId
      });
      // Update local state immediately for better UX
      queryClient.invalidateQueries({ queryKey: ['appointment', id] });
    } catch (error) {
      console.error('Failed to update service notes:', error);
    }
  };

  const handleUpdateCategoryNotes = async (categoryId: number, notes: string) => {
    try {
      await api.put(`/appointments/${id}/categories/${categoryId}`, {
        categoryNotes: notes,
        clinicId: appointment?.clinicId
      });
      // Update local state immediately for better UX
      queryClient.invalidateQueries({ queryKey: ['appointment', id] });
    } catch (error) {
      console.error('Failed to update category notes:', error);
    }
  };

  const handleCreateService = () => {
    if (!newServiceData.serviceName || !selectedAppointmentType) {
      toast({
        title: "Error",
        description: "Please fill in service name and select a category first",
        variant: "destructive",
      });
      return;
    }

    // Find the appointment category in local state
    const categoryIndex = localAppointmentCategories.findIndex(
      (cat: any) => cat.appointmentCategoryId === selectedAppointmentType
    );

    if (categoryIndex === -1) {
      toast({
        title: "Error",
        description: "Selected category not found in appointment",
        variant: "destructive",
      });
      return;
    }

    // Create new service object
    const newService = {
      categoryServiceId: Date.now(), // Temporary ID until saved
      categoryServiceName: newServiceData.serviceName,
      price: newServiceData.defaultPrice,
      currency: 'KES',
      status: 'pending',
      performedBy: 1020, // This should come from auth context
      performedByName: 'Current User', // This should come from auth context
      notes: '',
      isCompleted: false,
      estimatedDuration: newServiceData.estimatedDuration,
      description: newServiceData.description
    };

    // Add service to the local category
    const updatedCategories = [...localAppointmentCategories];
    if (!updatedCategories[categoryIndex].categoryServices) {
      updatedCategories[categoryIndex].categoryServices = [];
    }
    updatedCategories[categoryIndex].categoryServices.push(newService);

    // Update local state
    setLocalAppointmentCategories(updatedCategories);

    // Close modal and reset form
    setShowCreateServiceModal(false);
    setNewServiceData({
      serviceName: '',
      description: '',
      defaultPrice: 0,
      estimatedDuration: 30
    });
    setSelectedAppointmentType(null);

    toast({
      title: "Service Added",
      description: `${newServiceData.serviceName} has been added to the category. Click "Save Progress" to persist changes.`,
    });
  };

  // Remove category from appointment
  const handleRemoveCategory = async (categoryId: number) => {
    try {
      await api.delete(`/appointments/${id}/categories/${categoryId}`);
      queryClient.invalidateQueries({ queryKey: ['appointment', id] });
      toast({
        title: "Success",
        description: "Category removed from appointment",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to remove category",
        variant: "destructive",
      });
    }
  };

  // Remove service from category (local state)
  const handleRemoveService = (categoryId: number, serviceId: number) => {
    const categoryIndex = localAppointmentCategories.findIndex(
      (cat: any) => cat.appointmentCategoryId === categoryId
    );

    if (categoryIndex === -1) {
      toast({
        title: "Error",
        description: "Category not found",
        variant: "destructive",
      });
      return;
    }

    const updatedCategories = [...localAppointmentCategories];
    const category = updatedCategories[categoryIndex];

    if (category.categoryServices) {
      category.categoryServices = category.categoryServices.filter(
        (service: any) => service.categoryServiceId !== serviceId
      );
    }

    setLocalAppointmentCategories(updatedCategories);

    toast({
      title: "Service Removed",
      description: "Service removed from category. Click 'Save Progress' to persist changes.",
    });
  };

  // Save appointment progress
  const handleSaveProgress = async () => {
    try {
      // Check if all categories are complete
      const allCategoriesComplete = localAppointmentCategories.every((cat: any) =>
        cat.categoryServices?.length > 0 && cat.categoryNotes?.trim()
      );

      const updateData = {
        appointmentCategories: localAppointmentCategories,
        status: allCategoriesComplete ? 'completed' : 'in_progress',
        clinicId: appointment?.clinicId
      };

      await api.put(`/appointments/${id}`, updateData);
      queryClient.invalidateQueries({ queryKey: ['appointment', id] });

      toast({
        title: "Progress Saved",
        description: allCategoriesComplete
          ? "Appointment completed! All categories have services and notes."
          : "Appointment progress has been saved",
      });

      if (allCategoriesComplete) {
        setShowCompletionOptions(true);
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save progress",
        variant: "destructive",
      });
    }
  };



  const handleAssignStaff = (serviceId: number, staffId: number, staffName: string) => {
    setSelectedServices(selectedServices.map(s =>
      s.serviceId === serviceId
        ? { ...s, assignedStaffId: staffId, assignedStaffName: staffName, isExternalReferral: false }
        : s
    ));
    setShowStaffAssignModal(false);
    setSelectedServiceForAssignment(null);
  };

  const handleExternalReferral = (serviceId: number) => {
    if (!externalClinicData.name || !externalClinicData.contact) {
      toast({
        title: "Error",
        description: "Please fill in clinic name and contact",
        variant: "destructive",
      });
      return;
    }

    setSelectedServices(selectedServices.map(s =>
      s.serviceId === serviceId
        ? {
            ...s,
            isExternalReferral: true,
            externalClinic: { ...externalClinicData },
            assignedStaffId: undefined,
            assignedStaffName: undefined
          }
        : s
    ));
    setShowExternalReferralModal(false);
    setSelectedServiceForAssignment(null);
    setExternalClinicData({ name: '', contact: '', address: '' });
  };

  const generateAINotes = async (category: string, aiOption?: string) => {
    setIsGeneratingNotes(true);
    try {
      // Simulate AI note generation based on services, category, and AI option
      const categoryServices = selectedServices.filter(s =>
        services.find(service => service.serviceId === s.serviceId)?.category === category
      );

      let aiNotes = '';

      // Generate notes based on AI option
      switch (aiOption) {
        case 'Standard Assessment':
          aiNotes = `Standard ${category} assessment completed.\n\nFindings:\n- Patient alert and responsive\n- No immediate concerns noted\n- Vital signs within normal range\n\nRecommendations:\n- Continue current care plan\n- Monitor for any changes`;
          break;
        case 'Detailed Examination':
          aiNotes = `Comprehensive ${category} examination performed.\n\nDetailed Findings:\n- Thorough physical examination completed\n- All systems evaluated\n- Diagnostic tests reviewed\n\nClinical Assessment:\n- Patient condition stable\n- No abnormalities detected\n- Treatment plan effective`;
          break;
        case 'Treatment Summary':
          aiNotes = `${category} treatment summary:\n\nTreatments Administered:\n`;
          categoryServices.forEach(service => {
            aiNotes += `- ${service.serviceName}: Successfully completed\n`;
          });
          aiNotes += `\nPatient Response:\n- Tolerated treatment well\n- No adverse reactions\n- Expected recovery timeline`;
          break;
        case 'Follow-up Instructions':
          aiNotes = `${category} follow-up instructions:\n\n1. Monitor patient for 24-48 hours\n2. Continue prescribed medications as directed\n3. Return if symptoms worsen\n4. Schedule follow-up appointment in 1-2 weeks\n\nEmergency Contact:\nCall clinic immediately if any concerning symptoms develop.`;
          break;
        case 'Medication Notes':
          aiNotes = `${category} medication notes:\n\nPrescribed Medications:\n- Review current medications\n- Dosage adjustments made as needed\n- Patient education provided\n\nCompliance:\n- Patient understands instructions\n- No known drug allergies\n- Monitoring plan established`;
          break;
        case 'Behavioral Observations':
          aiNotes = `${category} behavioral observations:\n\nBehavior Assessment:\n- Patient cooperative during examination\n- Normal interaction patterns observed\n- Stress levels manageable\n\nRecommendations:\n- Continue positive reinforcement\n- Environmental enrichment suggested\n- Behavioral monitoring ongoing`;
          break;
        default:
          // Default generation
          if (categoryServices.length > 0) {
            aiNotes = `Services performed in ${category}:\n`;
            categoryServices.forEach(service => {
              aiNotes += `- ${service.serviceName}: Completed successfully\n`;
            });
            aiNotes += `\nPatient responded well to treatment. No adverse reactions observed.`;
          } else {
            aiNotes = `${category.charAt(0).toUpperCase() + category.slice(1)} assessment completed. Patient condition stable.`;
          }
      }

      const newNote: AppointmentNote = {
        category,
        content: aiNotes,
        isAIGenerated: true
      };

      setAppointmentNotes([...appointmentNotes, newNote]);

      toast({
        title: "AI Notes Generated",
        description: `${aiOption || 'Standard'} notes generated for ${category} category`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to generate AI notes",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingNotes(false);
    }
  };

  const handleAddManualNote = (category: string, content: string) => {
    if (!content.trim()) return;

    const newNote: AppointmentNote = {
      category,
      content: content.trim(),
      isAIGenerated: false
    };

    setAppointmentNotes([...appointmentNotes, newNote]);
  };

  const handleSaveAppointmentProgress = async () => {
    try {
      // Check if all categories have notes
      const categoriesWithNotes = [...new Set(appointmentNotes.map(note => note.category))];
      const allCategoriesComplete = serviceCategories.every(category =>
        categoriesWithNotes.includes(category) || !selectedServices.some(s =>
          services.find(service => service.serviceId === s.serviceId)?.category === category
        )
      );

      // Transform services to match backend expectations
      const transformedServices = selectedServices.map(service => ({
        ...service,
        categoryServiceName: service.serviceName, // Map serviceName to categoryServiceName for backend
        categoryServiceId: service.serviceId
      }));

      // Save services and notes to appointment
      const updateData = {
        appointmentCategories: appointment?.appointmentCategories?.map(cat => {
          const categoryServices = transformedServices.filter(service =>
            service.appointmentCategoryId === cat.appointmentCategoryId
          );
          return {
            ...cat,
            categoryServices,
            isCompleted: categoryServices.length > 0 && categoryServices.every(s => s.isCompleted),
            categoryStatus: categoryServices.length > 0 && categoryServices.every(s => s.isCompleted) ? 'completed' : 'in_progress'
          };
        }),
        generalNotes: appointmentNotes,
        status: allCategoriesComplete ? 'completed' : 'in_progress',
        clinicId: appointment?.clinicId
      };

      await api.put(`/appointments/${id}`, updateData);

      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ['appointment', id] });

      toast({
        title: "Progress Saved",
        description: allCategoriesComplete
          ? "Appointment completed! All categories have notes."
          : "Appointment progress has been saved",
      });

      if (allCategoriesComplete) {
        // Show completion options
        setShowCompletionOptions(true);
      }
    } catch (error) {
      console.error('Save progress error:', error);
      toast({
        title: "Error",
        description: "Failed to save appointment progress",
        variant: "destructive",
      });
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading appointment...</p>
        </div>
      </div>
    );
  }

  if (!appointment) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-red-600">Appointment not found</p>
          <Button onClick={() => navigate('/appointments')} className="mt-4">
            Back to Appointments
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-4 max-w-7xl">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate(`/appointments/${id}`)}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Details
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Advanced Appointment Workflow</h1>
            <p className="text-gray-600 text-lg">
              {appointment.petName || 'Hitler'} ({appointment.petSpecies || 'Dog'}, {appointment.petBreed || 'Mixed Breed'}) - {appointment.clientName || 'Lisa Garcia'}
            </p>
            <p className="text-sm text-gray-500">
              {(() => {
                try {
                  const dateValue = appointment.dateTime || (appointment as any).appointmentDate;
                  if (!dateValue) return "Invalid Date";
                  const date = new Date(dateValue);
                  if (isNaN(date.getTime())) return "Invalid Date";
                  return format(date, 'PPp');
                } catch (error) {
                  return "Invalid Date";
                }
              })()} • Status: {appointment.status} • Clinic: {appointment.clinicName || 'Adolf Clinic'}
            </p>
            {appointment.appointmentCategories && appointment.appointmentCategories.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-2">
                {appointment.appointmentCategories.map((category: any, index: number) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {category.categoryName || `Category #${category.appointmentCategoryId}`}
                    {category.categoryStatus === 'completed' && ' ✓'}
                  </Badge>
                ))}
              </div>
            )}
          </div>
        </div>
        <div className="flex items-center gap-3">
          <Badge
            variant={appointment.status === 'in_progress' ? 'default' : 'secondary'}
            className="px-3 py-1"
          >
            {appointment.status === 'in_progress' ? 'In Progress' : appointment.status}
          </Badge>
          <Button onClick={handleSaveAppointmentProgress} className="bg-green-600 hover:bg-green-700">
            <Save className="h-4 w-4 mr-2" />
            Save Progress
          </Button>
        </div>
      </div>

      {/* Progress Indicator */}
      {completionStats.totalCategories > 0 && (
        <Card className="mb-6 bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-semibold text-blue-900">Appointment Progress</h3>
              <div className="text-sm font-bold text-blue-900">
                {completionStats.overallCompletionPercentage}% Complete
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4 mb-4">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-blue-700">Categories</span>
                  <span className="font-medium text-blue-900">
                    {completionStats.completedCategories}/{completionStats.totalCategories} ({completionStats.categoryCompletionPercentage}%)
                  </span>
                </div>
                <div className="w-full bg-blue-100 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${completionStats.categoryCompletionPercentage}%` }}
                  ></div>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-green-700">Services</span>
                  <span className="font-medium text-green-900">
                    {completionStats.completedServices}/{completionStats.totalServices} ({completionStats.serviceCompletionPercentage}%)
                  </span>
                </div>
                <div className="w-full bg-green-100 rounded-full h-2">
                  <div
                    className="bg-green-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${completionStats.serviceCompletionPercentage}%` }}
                  ></div>
                </div>
              </div>
            </div>

            <div className="w-full bg-gray-200 rounded-full h-3">
              <div
                className="bg-gradient-to-r from-blue-500 to-green-500 h-3 rounded-full transition-all duration-300"
                style={{ width: `${completionStats.overallCompletionPercentage}%` }}
              ></div>
            </div>
          </CardContent>
        </Card>
      )}



      {/* Main Workflow Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="assignment" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Task Assignment
          </TabsTrigger>
          <TabsTrigger value="services" className="flex items-center gap-2">
            <Stethoscope className="h-4 w-4" />
            Services & Notes
          </TabsTrigger>
          <TabsTrigger value="ai-suggestions" className="flex items-center gap-2">
            <Brain className="h-4 w-4" />
            AI Suggestions
          </TabsTrigger>
          <TabsTrigger value="completion" className="flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            Complete
          </TabsTrigger>
        </TabsList>

        {/* Task Assignment Tab */}
        <TabsContent value="assignment" className="mt-6">
          <TaskAssignment
            appointmentId={parseInt(id!)}
            selectedAppointmentTypes={appointment?.appointmentTypes || []}
            appointmentData={appointment} // Pass the full appointment data
            clinicId={appointment?.clinicId}
            onAssignmentComplete={() => setActiveTab('services')}
          />
        </TabsContent>

        {/* Services & Notes Tab */}
        <TabsContent value="services" className="mt-6">
          <div className="w-full space-y-4">
            {/* Services Section */}
            <div className="w-full space-y-4">
              {/* Appointment Categories Management */}
              <Card>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="flex items-center gap-2">
                        <Stethoscope className="h-5 w-5" />
                        Appointment Categories & Services
                      </CardTitle>
                      <p className="text-sm text-gray-600">
                        Manage categories and services for this appointment
                      </p>
                    </div>
                    <Button
                      size="sm"
                      onClick={() => setActiveTab('assignment')}
                      variant="outline"
                      className="flex items-center gap-2"
                    >
                      <Plus className="h-4 w-4" />
                      Manage Categories
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Show services by appointment categories */}
                  {localAppointmentCategories?.length > 0 ? (
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <Label>Selected Appointment Categories</Label>
                        <div className="text-sm text-blue-600">
                          {localAppointmentCategories?.length || 0} categories selected
                        </div>
                      </div>

                      {/* Services grouped by selected appointment categories */}
                      {localAppointmentCategories.map((appointmentCategory: any) => {
                        const categoryId = appointmentCategory.appointmentCategoryId;
                        const categoryName = appointmentCategory.categoryName;

                        // Get services for this appointment category
                        const categoryServices = appointmentCategory.categoryServices || [];

                        return (
                          <div key={categoryId} className="border rounded-lg bg-white dark:bg-gray-800 shadow-sm border-gray-200 dark:border-gray-700">
                            {/* Category Header */}
                            <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border-b border-gray-200 dark:border-gray-700">
                              <div className="flex items-center justify-between mb-2">
                                <div className="flex items-center gap-3">
                                  <div className="p-2 bg-blue-100 rounded-lg">
                                    <Stethoscope className="h-4 w-4 text-blue-600" />
                                  </div>
                                  <div>
                                    <div className="flex items-center gap-2">
                                      <h4 className="font-semibold text-lg text-gray-900 dark:text-gray-100">{categoryName}</h4>
                                      {/* Category Status Buttons */}
                                      <div className="flex gap-1">
                                        <Button
                                          size="sm"
                                          variant={appointmentCategory.categoryStatus === 'scheduled' ? 'default' : 'outline'}
                                          onClick={() => handleCategoryStatusUpdate(categoryId, 'scheduled')}
                                          className={cn("text-xs h-6 px-2",
                                            appointmentCategory.categoryStatus === 'scheduled' ? 'bg-yellow-500 hover:bg-yellow-600 text-white' : 'hover:bg-yellow-50 dark:hover:bg-yellow-900/20'
                                          )}
                                          disabled={updateCategoryStatusMutation.isPending}
                                        >
                                          <Calendar className="h-3 w-3 mr-1" />
                                          Scheduled
                                        </Button>
                                        <Button
                                          size="sm"
                                          variant={appointmentCategory.categoryStatus === 'in_progress' ? 'default' : 'outline'}
                                          onClick={() => handleCategoryStatusUpdate(categoryId, 'in_progress')}
                                          className={cn("text-xs h-6 px-2",
                                            appointmentCategory.categoryStatus === 'in_progress' ? 'bg-blue-500 hover:bg-blue-600 text-white' : 'hover:bg-blue-50 dark:hover:bg-blue-900/20'
                                          )}
                                          disabled={updateCategoryStatusMutation.isPending}
                                        >
                                          <Clock className="h-3 w-3 mr-1" />
                                          In Progress
                                        </Button>
                                        <Button
                                          size="sm"
                                          variant={appointmentCategory.categoryStatus === 'completed' ? 'default' : 'outline'}
                                          onClick={() => handleCategoryStatusUpdate(categoryId, 'completed')}
                                          className={cn("text-xs h-6 px-2",
                                            appointmentCategory.categoryStatus === 'completed' ? 'bg-green-500 hover:bg-green-600 text-white' : 'hover:bg-green-50 dark:hover:bg-green-900/20'
                                          )}
                                          disabled={updateCategoryStatusMutation.isPending}
                                        >
                                          <CheckCircle className="h-3 w-3 mr-1" />
                                          Completed
                                        </Button>
                                      </div>
                                    </div>
                                    <p className="text-sm text-gray-600">
                                      {appointmentCategory.staffAssignedName ?
                                        `Assigned to: ${appointmentCategory.staffAssignedName}` :
                                        'No staff assigned'
                                      }
                                    </p>
                                  </div>
                                </div>
                                <div className="flex items-center gap-2">
                                  <Badge variant="secondary" className="text-xs">
                                    {categoryServices.length} service{categoryServices.length !== 1 ? 's' : ''}
                                  </Badge>
                                  <div className="flex gap-1">
                                    <Button
                                      size="sm"
                                      variant="outline"
                                      onClick={() => {
                                        setSelectedAppointmentType(categoryId);
                                        fetchAvailableServices(categoryId);
                                        setShowAddServiceModal(true);
                                      }}
                                      className="bg-white hover:bg-gray-50"
                                    >
                                      <Plus className="h-3 w-3 mr-1" />
                                      Add Service
                                    </Button>
                                    <Button
                                      size="sm"
                                      variant="outline"
                                      onClick={() => {
                                        setSelectedAppointmentType(categoryId);
                                        setShowCreateServiceModal(true);
                                      }}
                                      className="bg-blue-50 hover:bg-blue-100 text-blue-600 border-blue-200"
                                    >
                                      <Plus className="h-3 w-3 mr-1" />
                                      Create New
                                    </Button>
                                  </div>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => handleRemoveCategory(categoryId)}
                                    className="bg-red-50 hover:bg-red-100 text-red-600 border-red-200"
                                  >
                                    <Trash2 className="h-3 w-3" />
                                  </Button>
                                </div>
                              </div>
                            </div>

                            {/* Category Services */}
                            <div className="p-4">
                              {categoryServices.length > 0 ? (
                                <div className="space-y-3">
                                  <h5 className="font-medium text-sm text-gray-700 mb-3">Services in this category:</h5>
                                  <div className="grid grid-cols-1 gap-3">
                                    {categoryServices.map((service: any) => (
                                      <div
                                        key={service.categoryServiceId}
                                        className="p-4 border rounded-lg bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors border-gray-200 dark:border-gray-600"
                                      >
                                        <div className="flex justify-between items-start mb-3">
                                          <div className="flex-1">
                                            <h6 className="font-semibold text-base text-gray-900 dark:text-gray-100">{service.categoryServiceName || service.serviceName}</h6>
                                            {service.description && (
                                              <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">{service.description}</p>
                                            )}
                                          </div>
                                          <div className="flex items-center gap-2 ml-4">
                                            <Badge variant="outline" className="text-sm font-medium">
                                              {service.currency} {service.price}
                                            </Badge>
                                            <Button
                                              size="sm"
                                              variant="ghost"
                                              onClick={() => handleRemoveService(categoryId, service.categoryServiceId)}
                                              className="h-6 w-6 p-0 text-red-600 hover:bg-red-50"
                                            >
                                              <Trash2 className="h-3 w-3" />
                                            </Button>
                                          </div>
                                        </div>

                                        <div className="flex items-center gap-2 mb-3">
                                          {/* Status Buttons */}
                                          <div className="flex gap-1">
                                            <Button
                                              size="sm"
                                              variant={service.status === 'scheduled' ? 'default' : 'outline'}
                                              onClick={() => handleServiceStatusUpdate(categoryId, service.categoryServiceId, 'scheduled')}
                                              className={cn("text-xs h-7 px-2",
                                                service.status === 'scheduled' ? 'bg-yellow-500 hover:bg-yellow-600 text-white' : 'hover:bg-yellow-50 dark:hover:bg-yellow-900/20'
                                              )}
                                              disabled={updateServiceStatusMutation.isPending}
                                            >
                                              <Calendar className="h-3 w-3 mr-1" />
                                              Scheduled
                                            </Button>
                                            <Button
                                              size="sm"
                                              variant={service.status === 'in_progress' ? 'default' : 'outline'}
                                              onClick={() => handleServiceStatusUpdate(categoryId, service.categoryServiceId, 'in_progress')}
                                              className={cn("text-xs h-7 px-2",
                                                service.status === 'in_progress' ? 'bg-blue-500 hover:bg-blue-600 text-white' : 'hover:bg-blue-50 dark:hover:bg-blue-900/20'
                                              )}
                                              disabled={updateServiceStatusMutation.isPending}
                                            >
                                              <Clock className="h-3 w-3 mr-1" />
                                              In Progress
                                            </Button>
                                            <Button
                                              size="sm"
                                              variant={service.status === 'completed' ? 'default' : 'outline'}
                                              onClick={() => handleServiceStatusUpdate(categoryId, service.categoryServiceId, 'completed')}
                                              className={cn("text-xs h-7 px-2",
                                                service.status === 'completed' ? 'bg-green-500 hover:bg-green-600 text-white' : 'hover:bg-green-50 dark:hover:bg-green-900/20'
                                              )}
                                              disabled={updateServiceStatusMutation.isPending}
                                            >
                                              <CheckCircle className="h-3 w-3 mr-1" />
                                              Completed
                                            </Button>
                                          </div>

                                          {/* Service Info Badges */}
                                          <div className="flex gap-1 ml-auto">
                                            {service.performedByName && (
                                              <Badge variant="secondary" className="text-xs">
                                                <User className="h-3 w-3 mr-1" />
                                                {service.performedByName}
                                              </Badge>
                                            )}
                                            {service.estimatedDuration && (
                                              <Badge variant="outline" className="text-xs">
                                                <Clock className="h-3 w-3 mr-1" />
                                                {service.estimatedDuration}min
                                              </Badge>
                                            )}
                                          </div>
                                        </div>

                                        {/* Service Notes with AI Generation */}
                                        <div className="mt-2 space-y-2">
                                          <div className="flex items-center justify-between">
                                            <Label className="text-xs font-medium text-gray-600 dark:text-gray-400">Service Notes</Label>
                                            <Button
                                              size="sm"
                                              variant="outline"
                                              onClick={async () => {
                                                const noteKey = `service-${service.categoryServiceId}`;
                                                setGeneratingNotesFor(noteKey);
                                                setIsGeneratingNotes(true);
                                                try {
                                                  const aiNotes = await generateServiceNotes(service);
                                                  await handleUpdateServiceNotes(categoryId, service.categoryServiceId, aiNotes);
                                                  toast({
                                                    title: "AI Notes Generated",
                                                    description: "Service notes have been generated and saved",
                                                  });
                                                } catch (error) {
                                                  toast({
                                                    title: "Error",
                                                    description: "Failed to generate AI notes",
                                                    variant: "destructive",
                                                  });
                                                } finally {
                                                  setIsGeneratingNotes(false);
                                                  setGeneratingNotesFor('');
                                                }
                                              }}
                                              disabled={isGeneratingNotes}
                                              className="text-xs h-6 px-2 bg-purple-50 hover:bg-purple-100 dark:bg-purple-900/20 dark:hover:bg-purple-900/30 text-purple-700 dark:text-purple-300 border-purple-200 dark:border-purple-700"
                                            >
                                              {isGeneratingNotes && generatingNotesFor === `service-${service.categoryServiceId}` ? (
                                                <>
                                                  <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-purple-600 mr-1"></div>
                                                  Generating...
                                                </>
                                              ) : (
                                                <>
                                                  <Sparkles className="h-3 w-3 mr-1" />
                                                  Generate AI Notes
                                                </>
                                              )}
                                            </Button>
                                          </div>
                                          <Textarea
                                            placeholder="Add notes for this service..."
                                            value={service.notes || ''}
                                            onChange={(e) => handleUpdateServiceNotes(categoryId, service.categoryServiceId, e.target.value)}
                                            className="text-xs min-h-[60px] bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700"
                                            rows={2}
                                          />
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              ) : (
                                <div className="text-center py-8 border-2 border-dashed border-gray-200 rounded-lg bg-gray-50">
                                  <div className="flex flex-col items-center">
                                    <div className="p-3 bg-gray-100 rounded-full mb-3">
                                      <Plus className="h-6 w-6 text-gray-400" />
                                    </div>
                                    <h6 className="font-medium text-gray-900 mb-1">No services added yet</h6>
                                    <p className="text-sm text-gray-500 mb-4">
                                      Add services to track progress for {categoryName}
                                    </p>
                                    <div className="flex gap-2">
                                      <Button
                                        size="sm"
                                        onClick={() => {
                                          setSelectedAppointmentType(categoryId);
                                          fetchAvailableServices(categoryId);
                                          setShowAddServiceModal(true);
                                        }}
                                        className="bg-blue-600 hover:bg-blue-700"
                                      >
                                        <Plus className="h-4 w-4 mr-2" />
                                        Add Service to {categoryName}
                                      </Button>
                                      <Button
                                        size="sm"
                                        variant="outline"
                                        onClick={() => {
                                          setSelectedAppointmentType(categoryId);
                                          setShowCreateServiceModal(true);
                                        }}
                                        className="bg-white hover:bg-gray-50"
                                      >
                                        <Plus className="h-4 w-4 mr-2" />
                                        Create New Service
                                      </Button>
                                    </div>
                                  </div>
                                </div>
                              )}

                              {/* Category Notes with AI Generation */}
                              <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                                <div className="flex items-center justify-between mb-2">
                                  <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">Category Notes</Label>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={async () => {
                                      const noteKey = `category-${categoryId}`;
                                      setGeneratingNotesFor(noteKey);
                                      setIsGeneratingNotes(true);
                                      try {
                                        const aiNotes = await generateCategoryNotes(appointmentCategory);
                                        await handleUpdateCategoryNotes(categoryId, aiNotes);
                                        toast({
                                          title: "AI Category Notes Generated",
                                          description: "Category summary has been generated and saved",
                                        });
                                      } catch (error) {
                                        toast({
                                          title: "Error",
                                          description: "Failed to generate AI category notes",
                                          variant: "destructive",
                                        });
                                      } finally {
                                        setIsGeneratingNotes(false);
                                        setGeneratingNotesFor('');
                                      }
                                    }}
                                    disabled={isGeneratingNotes}
                                    className="text-xs h-6 px-2 bg-indigo-50 hover:bg-indigo-100 dark:bg-indigo-900/20 dark:hover:bg-indigo-900/30 text-indigo-700 dark:text-indigo-300 border-indigo-200 dark:border-indigo-700"
                                  >
                                    {isGeneratingNotes && generatingNotesFor === `category-${categoryId}` ? (
                                      <>
                                        <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-indigo-600 mr-1"></div>
                                        Generating...
                                      </>
                                    ) : (
                                      <>
                                        <Sparkles className="h-3 w-3 mr-1" />
                                        Generate AI Summary
                                      </>
                                    )}
                                  </Button>
                                </div>
                                <Textarea
                                  placeholder={`Add notes for ${categoryName} category...`}
                                  value={appointmentCategory.categoryNotes || ''}
                                  onChange={(e) => handleUpdateCategoryNotes(categoryId, e.target.value)}
                                  className="text-sm min-h-[80px] bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700"
                                  rows={3}
                                />
                              </div>
                            </div>
                          </div>
                        );
                      })}

                      {/* Services Summary */}
                      <div className="mt-6 p-4 bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg">
                        <div className="flex items-center justify-between mb-3">
                          <h4 className="font-medium text-base text-gray-900 flex items-center gap-2">
                            <CheckCircle className="h-4 w-4 text-green-600" />
                            Services Summary
                          </h4>
                          <Button
                            onClick={handleSaveProgress}
                            className="bg-green-600 hover:bg-green-700"
                            size="sm"
                          >
                            <Save className="h-4 w-4 mr-2" />
                            Save Progress
                          </Button>
                        </div>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div className="flex flex-col">
                            <span className="text-gray-600 text-xs">Categories</span>
                            <span className="font-semibold text-lg text-blue-600">
                              {completionStats.completedCategories}/{completionStats.totalCategories}
                            </span>
                            <span className="text-xs text-blue-500">{completionStats.categoryCompletionPercentage}% complete</span>
                          </div>
                          <div className="flex flex-col">
                            <span className="text-gray-600 text-xs">Services</span>
                            <span className="font-semibold text-lg text-green-600">
                              {completionStats.completedServices}/{completionStats.totalServices}
                            </span>
                            <span className="text-xs text-green-500">{completionStats.serviceCompletionPercentage}% complete</span>
                          </div>
                          <div className="flex flex-col">
                            <span className="text-gray-600 text-xs">Overall Progress</span>
                            <span className="font-semibold text-lg text-purple-600">
                              {completionStats.overallCompletionPercentage}%
                            </span>
                            <span className="text-xs text-purple-500">
                              {completionStats.isFullyCompleted ? 'Completed' : 'In Progress'}
                            </span>
                          </div>
                          <div className="flex flex-col">
                            <span className="text-gray-600 text-xs">Estimated Charges</span>
                            <span className="font-semibold text-lg text-green-600">
                              KES {localAppointmentCategories?.reduce((total: number, cat: any) =>
                                total + (cat.categoryServices?.reduce((serviceTotal: number, service: any) =>
                                  serviceTotal + (service.price || 0), 0) || 0), 0).toLocaleString()}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-12 border-2 border-dashed border-gray-200 rounded-lg bg-gray-50">
                      <div className="flex flex-col items-center">
                        <div className="p-4 bg-gray-100 rounded-full mb-4">
                          <Stethoscope className="h-8 w-8 text-gray-400" />
                        </div>
                        <h4 className="font-medium text-gray-900 mb-2">No Categories Selected</h4>
                        <p className="text-sm text-gray-500 mb-4 max-w-md">
                          Please go back to the Task Assignment tab to select appointment categories before adding services.
                        </p>
                        <Button
                          variant="outline"
                          onClick={() => setActiveTab('assignment')}
                          className="flex items-center gap-2"
                        >
                          <ArrowLeft className="h-4 w-4" />
                          Go to Task Assignment
                        </Button>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>


          </div>
        </TabsContent>

        {/* AI Suggestions Tab */}
        <TabsContent value="ai-suggestions" className="mt-6">
          <AISuggestions
            appointmentId={parseInt(id!)}
            petData={appointment?.pet}
            appointmentData={appointment}
            clinicId={appointment?.clinicId}
          />
        </TabsContent>

        {/* Completion Tab */}
        <TabsContent value="completion" className="mt-6">
          <AppointmentComplete
            appointmentId={parseInt(id!)}
            appointmentData={appointment}
          />
        </TabsContent>
      </Tabs>

      {/* Staff Assignment Modal */}
      <Dialog open={showStaffAssignModal} onOpenChange={setShowStaffAssignModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Assign Staff Member</DialogTitle>
            <DialogDescription>
              Select a staff member to assign this service to.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            {clinicStaff.length === 0 ? (
              <p className="text-gray-500">No staff members available</p>
            ) : (
              <div className="grid grid-cols-1 gap-3 max-h-96 overflow-y-auto">
                {clinicStaff.map((staff: any) => (
                  <div
                    key={staff.staffId}
                    className="p-3 border rounded-lg hover:shadow-md cursor-pointer transition-all"
                    onClick={() => handleAssignStaff(
                      selectedServiceForAssignment!,
                      staff.staffId,
                      `${staff.firstName} ${staff.lastName}`
                    )}
                  >
                    <div className="flex justify-between items-center">
                      <div>
                        <h6 className="font-medium">{staff.firstName} {staff.lastName}</h6>
                        <p className="text-sm text-gray-600">{staff.jobTitle}</p>
                        <p className="text-xs text-gray-500">{staff.email}</p>
                      </div>
                      <Badge variant="outline">{staff.jobTitle}</Badge>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* External Referral Modal */}
      <Dialog open={showExternalReferralModal} onOpenChange={setShowExternalReferralModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>External Clinic Referral</DialogTitle>
            <DialogDescription>
              Refer this service to an external clinic or specialist.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label>Clinic/Specialist Name</Label>
              <Input
                value={externalClinicData.name}
                onChange={(e) => setExternalClinicData({...externalClinicData, name: e.target.value})}
                placeholder="Enter clinic or specialist name"
              />
            </div>
            <div>
              <Label>Contact Information</Label>
              <Input
                value={externalClinicData.contact}
                onChange={(e) => setExternalClinicData({...externalClinicData, contact: e.target.value})}
                placeholder="Phone number or email"
              />
            </div>
            <div>
              <Label>Address (Optional)</Label>
              <Input
                value={externalClinicData.address}
                onChange={(e) => setExternalClinicData({...externalClinicData, address: e.target.value})}
                placeholder="Clinic address"
              />
            </div>
            <Button
              onClick={() => handleExternalReferral(selectedServiceForAssignment!)}
              className="w-full"
            >
              Create Referral
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Completion Options Modal */}
      <Dialog open={showCompletionOptions} onOpenChange={setShowCompletionOptions}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Appointment Completed</DialogTitle>
            <DialogDescription>
              All categories have been documented. Choose your next action.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-1 gap-3">
              <Button
                onClick={() => navigate(`/invoices/appointment/${id}`)}
                className="w-full bg-green-600 hover:bg-green-700"
              >
                Generate Invoice
              </Button>
              <Button
                onClick={() => navigate(`/appointments/${id}`)}
                variant="outline"
                className="w-full"
              >
                View Appointment Details
              </Button>
              <Button
                onClick={() => navigate(`/receipts/appointment/${id}`)}
                variant="outline"
                className="w-full"
              >
                Generate Receipt
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Add Service Modal */}
      <Dialog open={showAddServiceModal} onOpenChange={setShowAddServiceModal}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Add Service to Category</DialogTitle>
            <DialogDescription>
              Select a service to add to this appointment category.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            {availableServices.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500">No services available for this category</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 gap-3 max-h-96 overflow-y-auto">
                {availableServices.map((service: any) => (
                  <div
                    key={service.categoryServiceId}
                    className="p-4 border rounded-lg hover:shadow-md cursor-pointer transition-all bg-white hover:bg-gray-50"
                    onClick={() => handleAddExistingService(service)}
                  >
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <h6 className="font-semibold text-base text-gray-900">{service.categoryServiceName}</h6>
                        {service.description && (
                          <p className="text-sm text-gray-600 mt-1">{service.description}</p>
                        )}
                        <div className="flex items-center gap-2 mt-2">
                          <Badge variant="outline" className="text-xs">
                            {service.currency} {service.defaultPrice}
                          </Badge>
                          {service.estimatedDuration && (
                            <Badge variant="secondary" className="text-xs">
                              {service.estimatedDuration} min
                            </Badge>
                          )}
                        </div>
                      </div>
                      <Button
                        size="sm"
                        disabled={addServiceMutation.isPending}
                        className="ml-4"
                      >
                        {addServiceMutation.isPending ? 'Adding...' : 'Add'}
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
            <div className="flex justify-end">
              <Button
                variant="outline"
                onClick={() => setShowAddServiceModal(false)}
              >
                Cancel
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Create Service Modal */}
      <Dialog open={showCreateServiceModal} onOpenChange={setShowCreateServiceModal}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Create New Service</DialogTitle>
            <DialogDescription>
              Create a new service for the selected appointment type.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="serviceName">Service Name *</Label>
              <Input
                id="serviceName"
                value={newServiceData.serviceName}
                onChange={(e) => setNewServiceData({...newServiceData, serviceName: e.target.value})}
                placeholder="Enter service name"
              />
            </div>
            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={newServiceData.description}
                onChange={(e) => setNewServiceData({...newServiceData, description: e.target.value})}
                placeholder="Enter service description"
                rows={3}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="defaultPrice">Price (KES) *</Label>
                <Input
                  id="defaultPrice"
                  type="number"
                  value={newServiceData.defaultPrice}
                  onChange={(e) => setNewServiceData({...newServiceData, defaultPrice: parseFloat(e.target.value) || 0})}
                  placeholder="0.00"
                  min="0"
                  step="0.01"
                />
              </div>
              <div>
                <Label htmlFor="estimatedDuration">Duration (min)</Label>
                <Input
                  id="estimatedDuration"
                  type="number"
                  value={newServiceData.estimatedDuration}
                  onChange={(e) => setNewServiceData({...newServiceData, estimatedDuration: parseInt(e.target.value) || 30})}
                  placeholder="30"
                  min="1"
                />
              </div>
            </div>
            <div className="flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => {
                  setShowCreateServiceModal(false);
                  setNewServiceData({
                    serviceName: '',
                    description: '',
                    defaultPrice: 0,
                    estimatedDuration: 30
                  });
                }}
              >
                Cancel
              </Button>
              <Button
                onClick={handleCreateService}
              >
                Create Service
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default StartAppointment;
