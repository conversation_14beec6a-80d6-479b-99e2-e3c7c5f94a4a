import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Calendar, 
  Clock, 
  DollarSign, 
  User, 
  PawPrint,
  TestTube,
  RefreshCw
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

// Import our new services and components
import { getAppointmentCategories, getCategoryServices } from '@/services/appointmentCategories';
import AppointmentCategorySelector from '@/components/appointments/AppointmentCategorySelector';
import AppointmentSummary from '@/components/appointments/AppointmentSummary';
import { useAuth } from '@/store';

const TestAppointmentCategories: React.FC = () => {
  const { toast } = useToast();
  const { clinic } = useAuth();
  
  const [selectedCategories, setSelectedCategories] = useState<Array<{
    appointmentCategoryId: number;
    categoryName: string;
    assignedStaff?: number;
    assignedStaffName?: string;
    estimatedDuration: number;
    categoryServices: Array<{
      categoryServiceId: number;
      serviceName: string;
      price: number;
      currency: string;
      notes?: string;
    }>;
    categoryNotes?: string;
  }>>([]);

  // Test fetching appointment categories
  const { data: categoriesResponse, isLoading: isLoadingCategories, refetch: refetchCategories } = useQuery({
    queryKey: ['appointmentCategories', clinic?.clinicId],
    queryFn: () => getAppointmentCategories({ 
      clinicId: clinic?.clinicId,
      isActive: true 
    }),
    enabled: !!clinic?.clinicId
  });

  const categories = categoriesResponse?.data || [];

  // Test fetching services for a specific category
  const [testCategoryId, setTestCategoryId] = useState<number | null>(null);
  const { data: servicesResponse, isLoading: isLoadingServices } = useQuery({
    queryKey: ['categoryServices', testCategoryId, clinic?.clinicId],
    queryFn: () => getCategoryServices(testCategoryId!, { 
      clinicId: clinic?.clinicId,
      isActive: true 
    }),
    enabled: !!testCategoryId && !!clinic?.clinicId
  });

  const services = servicesResponse?.data || [];

  // Calculate totals from selected categories
  const calculateTotals = () => {
    const totalDuration = selectedCategories.reduce((total, category) => {
      return total + category.estimatedDuration;
    }, 0);

    const totalPrice = selectedCategories.reduce((total, category) => {
      return total + category.categoryServices.reduce((categoryTotal, service) => {
        return categoryTotal + service.price;
      }, 0);
    }, 0);

    const currency = selectedCategories[0]?.categoryServices[0]?.currency || 'KES';

    return { totalDuration, totalPrice, currency };
  };

  const { totalDuration, totalPrice, currency } = calculateTotals();

  // Mock appointment data for testing AppointmentSummary
  const mockAppointment = {
    appointmentId: 1001,
    clientName: "John Doe",
    clientPhone: "+254712345678",
    clientEmail: "<EMAIL>",
    petName: "Buddy",
    petSpecies: "Dog",
    petBreed: "Golden Retriever",
    appointmentDate: new Date(),
    estimatedDuration: totalDuration || 60,
    status: "scheduled",
    priority: "normal",
    staffInChargeName: "Dr. Sarah Wilson",
    clinicName: "Happy Paws Veterinary Clinic",
    generalNotes: "Regular checkup and vaccination",
    recommendations: "Continue current diet and exercise routine",
    appointmentCategories: selectedCategories
  };

  const handleTestAPI = async () => {
    try {
      toast({
        title: "Testing API",
        description: "Fetching appointment categories...",
      });
      
      await refetchCategories();
      
      toast({
        title: "Success",
        description: `Loaded ${categories.length} appointment categories`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch appointment categories",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Appointment Categories Test Page</h1>
          <p className="text-gray-600">Test the new appointment categories and services system</p>
        </div>
        <Button onClick={handleTestAPI} className="flex items-center gap-2">
          <RefreshCw className="h-4 w-4" />
          Test API
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Categories Overview */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TestTube className="h-5 w-5" />
              Available Categories ({categories.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoadingCategories ? (
              <div className="flex items-center justify-center p-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            ) : (
              <div className="space-y-3">
                {categories.map((category) => (
                  <div
                    key={category.appointmentCategoryId}
                    className="p-3 border rounded-lg hover:bg-gray-50 cursor-pointer"
                    onClick={() => setTestCategoryId(category.appointmentCategoryId)}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">{category.name}</h4>
                        <p className="text-sm text-gray-600">{category.description}</p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-xs">
                          <Clock className="h-3 w-3 mr-1" />
                          {category.estimatedDuration}min
                        </Badge>
                        {category.requiresEquipment && (
                          <Badge variant="outline" className="text-xs">Equipment</Badge>
                        )}
                        {category.requiresQualification && (
                          <Badge variant="outline" className="text-xs">Qualified</Badge>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Services for Selected Category */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              Services {testCategoryId && `for Category ${testCategoryId}`}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {!testCategoryId ? (
              <p className="text-gray-500 text-center py-8">
                Click on a category to view its services
              </p>
            ) : isLoadingServices ? (
              <div className="flex items-center justify-center p-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            ) : (
              <div className="space-y-3">
                {services.map((service) => (
                  <div
                    key={service.categoryServiceId}
                    className="p-3 border rounded-lg"
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">{service.categoryServiceName}</h4>
                        <p className="text-sm text-gray-600">{service.description}</p>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold text-green-600">
                          {service.currency} {service.defaultPrice.toLocaleString()}
                        </p>
                        <p className="text-xs text-gray-500">
                          {service.estimatedDuration}min
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
                {services.length === 0 && (
                  <p className="text-gray-500 text-center py-4">
                    No services found for this category
                  </p>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <Separator />

      {/* Category Selector Component Test */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Category Selector Component Test
          </CardTitle>
        </CardHeader>
        <CardContent>
          <AppointmentCategorySelector
            selectedCategories={selectedCategories}
            onCategoriesChange={setSelectedCategories}
            disabled={false}
          />
        </CardContent>
      </Card>

      {/* Selection Summary */}
      {selectedCategories.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Selection Summary
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <h3 className="font-semibold text-blue-800">Categories</h3>
                <p className="text-2xl font-bold text-blue-600">{selectedCategories.length}</p>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <h3 className="font-semibold text-green-800">Total Duration</h3>
                <p className="text-2xl font-bold text-green-600">{totalDuration} min</p>
              </div>
              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <h3 className="font-semibold text-purple-800">Total Price</h3>
                <p className="text-2xl font-bold text-purple-600">{currency} {totalPrice.toLocaleString()}</p>
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="font-medium">Selected Categories:</h4>
              {selectedCategories.map((category) => (
                <div key={category.appointmentCategoryId} className="p-2 bg-gray-50 rounded">
                  <div className="flex items-center justify-between">
                    <span className="font-medium">{category.categoryName}</span>
                    <Badge variant="outline">
                      {category.categoryServices.length} services
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Appointment Summary Component Test */}
      {selectedCategories.length > 0 && (
        <div>
          <h2 className="text-2xl font-bold mb-4">Appointment Summary Component Test</h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-semibold mb-2">Full View</h3>
              <AppointmentSummary
                appointment={mockAppointment}
                showPricing={true}
                compact={false}
              />
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-2">Compact View</h3>
              <AppointmentSummary
                appointment={mockAppointment}
                showPricing={true}
                compact={true}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TestAppointmentCategories;
