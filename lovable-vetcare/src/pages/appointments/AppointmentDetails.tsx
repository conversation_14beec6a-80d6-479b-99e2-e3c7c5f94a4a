import React, { useState } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { format } from 'date-fns';
import { ArrowLeft, Calendar, Clock, User, Stethoscope, FileText, History, Receipt, Tag, CheckCircle2, Edit2, Save, X, DollarSign, Percent, Eye, Download, Plus, Minus, AlertCircle, CheckCircle, XCircle, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';

import { getAppointmentById, getAppointmentTypes, completeAppointment } from '@/services/appointments';
import { getHealthRecordsByPet } from '@/services/healthRecords';
import { getAppointmentCategories } from '@/services/appointmentCategories';
import { Appointment } from '@/store/types';
import { useToast } from '@/components/ui/use-toast';
import { api } from '@/services/api';
import CompleteAppointmentModal from './components/CompleteAppointmentModal';
import ReassignAppointment from '@/components/appointments/ReassignAppointment';
import FollowUpAppointment from '@/components/appointments/FollowUpAppointment';

const AppointmentDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [showCompleteModal, setShowCompleteModal] = useState(false);
  const [editingField, setEditingField] = useState<string | null>(null);
  const [editValues, setEditValues] = useState<any>({});
  const [showEditTimeModal, setShowEditTimeModal] = useState(false);
  const [showEditCategoriesModal, setShowEditCategoriesModal] = useState(false);
  const [showChargesModal, setShowChargesModal] = useState(false);
  const [selectedCategoryForCharges, setSelectedCategoryForCharges] = useState<any>(null);

  const { data: appointmentResponse, isLoading, error } = useQuery({
    queryKey: ['appointment', id],
    queryFn: () => {
      if (!id || id === 'undefined') {
        throw new Error('Invalid appointment ID');
      }
      return getAppointmentById(parseInt(id));
    },
    enabled: !!id && id !== 'undefined'
  });

  const { data: appointmentTypesResponse } = useQuery({
    queryKey: ['appointmentTypes'],
    queryFn: getAppointmentTypes
  });

  const { data: appointmentCategoriesResponse } = useQuery({
    queryKey: ['appointmentCategories'],
    queryFn: () => getAppointmentCategories({ isActive: true })
  });

  // Check for invoice status
  const { data: invoiceResponse } = useQuery({
    queryKey: ['invoice', 'appointment', id],
    queryFn: async () => {
      try {
        const response = await api.get(`/invoices/appointment/${id}`);
        return response.data;
      } catch (error) {
        return null;
      }
    },
    enabled: !!id
  });

  // Check for receipt status
  const { data: receiptResponse } = useQuery({
    queryKey: ['receipt', 'appointment', id],
    queryFn: async () => {
      try {
        const response = await api.get(`/receipts/appointment/${id}`);
        return response.data;
      } catch (error) {
        return null;
      }
    },
    enabled: !!id
  });

  // Check for medical record status
  const { data: medicalRecordResponse } = useQuery({
    queryKey: ['medicalRecord', 'appointment', id],
    queryFn: async () => {
      try {
        const response = await api.get(`/health-records/appointment/${id}`);
        return response.data;
      } catch (error) {
        return null;
      }
    },
    enabled: !!id
  });

  // Complete appointment mutation
  const completeAppointmentMutation = useMutation({
    mutationFn: () => completeAppointment(id!),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['appointment', id] });
      toast({
        title: "Success",
        description: "Appointment marked as completed successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to complete appointment",
        variant: "destructive",
      });
    }
  });

  // Early return for invalid appointment ID
  if (!id || id === 'undefined') {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center gap-4 mb-6">
          <Button variant="ghost" onClick={() => navigate('/appointments')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Appointments
          </Button>
        </div>
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <h2 className="text-xl font-semibold text-red-600 mb-2">Invalid Appointment ID</h2>
              <p className="text-gray-600">The appointment ID provided is not valid.</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const appointment = appointmentResponse?.data as Appointment;
  const appointmentTypes = appointmentTypesResponse?.data || [];
  const appointmentCategories = appointmentCategoriesResponse?.data || [];

  const getAppointmentTypeName = (appointmentTypeId: number) => {
    const type = appointmentTypes.find((t: any) => t.appointmentTypeId === appointmentTypeId);
    return type?.name || `Type #${appointmentTypeId}`;
  };

  const getAppointmentCategoryName = (appointmentCategoryId: number) => {
    const category = appointmentCategories.find((c: any) => c.appointmentCategoryId === appointmentCategoryId);
    return category?.name || `Category #${appointmentCategoryId}`;
  };

  const getAppointmentCategoryDetails = (appointmentCategoryId: number) => {
    return appointmentCategories.find((c: any) => c.appointmentCategoryId === appointmentCategoryId);
  };

  // Calculate total costs for the appointment
  const calculateAppointmentTotals = () => {
    if (!appointment.appointmentCategories || appointment.appointmentCategories.length === 0) {
      return {
        categoryTotal: 0,
        servicesTotal: 0,
        subtotal: 0
      };
    }

    const categoryTotal = appointment.appointmentCategories.reduce((total: number, category: any) => {
      const categoryDetails = appointmentCategories.find((c: any) => c.appointmentCategoryId === category.appointmentCategoryId) as any;
      const defaultCharge = (categoryDetails?.defaultCharge as number) || 0;
      const discount = (categoryDetails?.defaultDiscountPercentage as number) || 0;
      const finalCharge = defaultCharge - (defaultCharge * discount / 100);
      return total + finalCharge;
    }, 0);

    const servicesTotal = appointment.appointmentCategories.reduce((total: number, category: any) => {
      return total + (category.categoryServices?.reduce((serviceTotal: number, service: any) => {
        return serviceTotal + (service.price || 0);
      }, 0) || 0);
    }, 0);

    const subtotal = categoryTotal + servicesTotal;

    return {
      categoryTotal,
      servicesTotal,
      subtotal
    };
  };

  // Check if all appointment categories are complete
  const areAllCategoriesComplete = () => {
    if (!appointment?.appointmentCategories || appointment.appointmentCategories.length === 0) {
      return false;
    }
    return appointment.appointmentCategories.every((category: any) =>
      category.isCompleted || category.categoryStatus === 'completed'
    );
  };



  const getStatusBadgeClass = (status: string) => {
    const statusClasses = {
      scheduled: "bg-blue-100 text-blue-800",
      completed: "bg-green-100 text-green-800",
      cancelled: "bg-red-100 text-red-800",
      default: "bg-gray-100 text-gray-800"
    };
    return statusClasses[status as keyof typeof statusClasses] || statusClasses.default;
  };

  const formatDateTime = (appointment: Appointment) => {
    try {
      const dateValue = appointment.dateTime || (appointment as any).appointmentDate;
      if (!dateValue) {
        return { date: "Invalid Date", time: "Invalid Time" };
      }
      const date = new Date(dateValue);
      if (isNaN(date.getTime())) {
        return { date: "Invalid Date", time: "Invalid Time" };
      }
      return {
        date: format(date, "EEEE, MMMM d, yyyy"),
        time: format(date, "h:mm a")
      };
    } catch (error) {
      console.error("Error formatting date/time:", error);
      return { date: "Invalid Date", time: "Invalid Time" };
    }
  };

  const handleViewMedicalHistory = () => {
    if (appointment?.petId) {
      navigate(`/pets/${appointment.petId}/medical-history`);
    }
  };

  const handleCompleteAppointment = (healthRecord: any) => {
    // Navigate to the health record or show success message
    console.log('Appointment completed, health record created:', healthRecord);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading appointment details...</p>
        </div>
      </div>
    );
  }

  if (error || !appointment) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-red-600">Failed to load appointment details</p>
          <Button onClick={() => navigate('/appointments')} className="mt-4">
            Back to Appointments
          </Button>
        </div>
      </div>
    );
  }

  const { date, time } = formatDateTime(appointment);

  return (
    <div className="container mx-auto px-4 py-4 max-w-6xl">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/appointments')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Appointments
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Appointment Summary</h1>
            <p className="text-gray-600">Appointment #{appointment.appointmentId}</p>
          </div>
        </div>
        <Badge className={getStatusBadgeClass(appointment.status)}>
          {appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}
        </Badge>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Appointment Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Appointment Overview
                {appointment.status !== 'completed' && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setShowEditTimeModal(true)}
                    className="ml-auto"
                  >
                    <Edit2 className="h-3 w-3 mr-1" />
                    Edit Time
                  </Button>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Patient & Client Info */}
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-lg font-semibold text-gray-900">
                    {appointment.petName || 'Unknown Pet'}
                  </p>
                  <p className="text-sm text-gray-600">
                    Owner: {appointment.clientName || 'Unknown Client'}
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-sm">
                    {appointment.petSpecies || 'Pet'}
                  </Badge>
                  <Badge className={getStatusBadgeClass(appointment.status)} variant="secondary">
                    {appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}
                  </Badge>
                </div>
              </div>

              {/* Date, Time & Priority */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-6">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-gray-500" />
                    <span className="font-medium">{date}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-gray-500" />
                    <span className="font-medium">{time}</span>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="capitalize">
                    {appointment.priority || 'Normal'} Priority
                  </Badge>
                  <Badge variant="outline">
                    {appointment.duration || appointment.estimatedDuration || 30} min
                  </Badge>
                </div>
              </div>

              {/* Progress Summary */}
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <span className="text-sm font-medium text-gray-600">Overall Progress</span>
                  <span className="text-sm font-bold text-gray-900">{appointment.completionPercentage || 0}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2 mb-3">
                  <div
                    className="bg-gradient-to-r from-blue-500 to-green-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${appointment.completionPercentage || 0}%` }}
                  ></div>
                </div>
                <div className="flex items-center justify-between text-sm text-gray-600">
                  <span>
                    {appointment.appointmentCategories?.filter((cat: any) => cat.isCompleted).length || 0} / {appointment.appointmentCategories?.length || 0} categories completed
                  </span>
                  <span>
                    {appointment.completedServicesCount || 0} / {appointment.totalServicesCount || 0} services completed
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>



          {/* Visit Summary & Cost Breakdown */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Visit Summary & Costs
                {appointment.status !== 'completed' && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setShowEditCategoriesModal(true)}
                    className="ml-auto"
                  >
                    <Edit2 className="h-3 w-3 mr-1" />
                    Edit Categories
                  </Button>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Visit Details */}
              {(appointment.reason || appointment.generalNotes || appointment.recommendations) && (
                <div className="space-y-3">
                  {(appointment.reason || appointment.generalNotes) && (
                    <div>
                      <p className="text-sm font-medium text-gray-600 mb-1">Reason for Visit:</p>
                      <p className="text-gray-900">{appointment.reason || appointment.generalNotes || 'No reason specified'}</p>
                    </div>
                  )}

                  {appointment.recommendations && (
                    <div>
                      <p className="text-sm font-medium text-gray-600 mb-1">Recommendations:</p>
                      <p className="text-gray-900">{appointment.recommendations}</p>
                    </div>
                  )}
                </div>
              )}

              {/* Cost Breakdown */}
              {appointment.appointmentCategories && appointment.appointmentCategories.length > 0 && (
                <div>
                  <Separator className="my-4" />
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-2">
                      <DollarSign className="h-5 w-5 text-green-600" />
                      <label className="text-sm font-medium text-gray-600">Cost Breakdown</label>
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => navigate(`/invoices/appointment/${appointment.appointmentId}`)}
                    >
                      <Receipt className="h-3 w-3 mr-1" />
                      View Invoice
                    </Button>
                  </div>

                  <div className="bg-gray-50 rounded-lg p-4 space-y-4">
                    {/* Categories and Services */}
                    {appointment.appointmentCategories.map((category: any, index: number) => {
                      const categoryDetails = appointmentCategories.find((c: any) => c.appointmentCategoryId === category.appointmentCategoryId) as any;
                      const defaultCharge = (categoryDetails?.defaultCharge as number) || 0;
                      const discount = (categoryDetails?.defaultDiscountPercentage as number) || 0;
                      const finalCategoryCharge = defaultCharge - (defaultCharge * discount / 100);
                      const categoryName = getAppointmentCategoryName(category.appointmentCategoryId);

                      return (
                        <div key={index} className="border-b border-gray-200 pb-3 last:border-b-0">
                          {/* Category Header with Status */}
                          <div className="flex justify-between items-center mb-2">
                            <div className="flex items-center gap-2">
                              {category.categoryStatus === 'completed' ? (
                                <CheckCircle2 className="h-4 w-4 text-green-600" />
                              ) : category.categoryStatus === 'in_progress' ? (
                                <Clock className="h-4 w-4 text-yellow-600" />
                              ) : (
                                <Calendar className="h-4 w-4 text-gray-600" />
                              )}
                              <h6 className="font-medium text-gray-800">{categoryName}</h6>
                              <Badge
                                variant="outline"
                                className={`text-xs ${
                                  category.categoryStatus === 'completed' ? 'bg-green-100 text-green-800 border-green-300' :
                                  category.categoryStatus === 'in_progress' ? 'bg-yellow-100 text-yellow-800 border-yellow-300' :
                                  'bg-gray-100 text-gray-800 border-gray-300'
                                }`}
                              >
                                {category.categoryStatus || 'scheduled'}
                              </Badge>
                            </div>
                            {finalCategoryCharge > 0 && (
                              <span className="text-sm font-medium text-green-600">
                                KES {finalCategoryCharge.toLocaleString()}
                              </span>
                            )}
                          </div>

                          {/* Services under this category */}
                          {category.categoryServices && category.categoryServices.length > 0 && (
                            <div className="ml-6 space-y-1">
                              {category.categoryServices.map((service: any, serviceIndex: number) => (
                                <div key={serviceIndex} className="flex justify-between items-center text-sm">
                                  <span className="text-gray-600">{service.serviceName}</span>
                                  <span className="font-medium text-green-600">
                                    {service.currency || 'KES'} {(service.price || 0).toLocaleString()}
                                  </span>
                                </div>
                              ))}
                            </div>
                          )}

                          {/* Staff Assignment */}
                          <div className="ml-6 mt-2 space-y-1">
                            {(category.staffAssignedName || category.performedByName) ? (
                              <>
                                {category.staffAssignedName && (
                                  <div className="flex items-center gap-2">
                                    <User className="h-3 w-3 text-blue-500" />
                                    <span className="text-xs text-gray-600">
                                      <span className="font-medium">Assigned to:</span> {category.staffAssignedName}
                                    </span>
                                  </div>
                                )}
                                {category.performedByName && category.performedByName !== category.staffAssignedName && (
                                  <div className="flex items-center gap-2">
                                    <Stethoscope className="h-3 w-3 text-green-500" />
                                    <span className="text-xs text-gray-600">
                                      <span className="font-medium">Performed by:</span> {category.performedByName}
                                    </span>
                                  </div>
                                )}
                              </>
                            ) : (
                              <div className="flex items-center gap-2">
                                <User className="h-3 w-3 text-gray-400" />
                                <span className="text-xs text-gray-400 italic">No staff assigned</span>
                              </div>
                            )}
                          </div>
                        </div>
                      );
                    })}

                    {/* Grand Total */}
                    <div className="border-t border-gray-400 pt-3 mt-4">
                      <div className="flex justify-between items-center">
                        <span className="text-lg font-semibold text-gray-900">Grand Total:</span>
                        <span className="text-xl font-bold text-green-700">
                          KES {(() => {
                            const totals = calculateAppointmentTotals();
                            return totals.subtotal.toLocaleString();
                          })()}
                        </span>
                      </div>
                    </div>

                    {/* Completion Status */}
                    {areAllCategoriesComplete() && (
                      <div className="bg-green-50 border border-green-200 rounded-lg p-3 mt-4">
                        <div className="flex items-center gap-2">
                          <CheckCircle2 className="h-4 w-4 text-green-600" />
                          <span className="text-sm font-medium text-green-800">All categories completed</span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Staff in Charge */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Stethoscope className="h-5 w-5" />
                Staff in Charge
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <p className="font-medium">
                  {appointment.staffInChargeName || 'Not assigned'}
                </p>
                {appointment.status === 'scheduled' && appointment.staffInCharge && (
                  <ReassignAppointment
                    appointmentId={appointment.appointmentId}
                    currentStaffId={appointment.staffInCharge}
                    currentStaffName={appointment.staffInChargeName || 'Unknown Staff'}
                    onReassignSuccess={() => window.location.reload()}
                  />
                )}
              </div>
            </CardContent>
          </Card>





          {/* Appointment Details */}
          <Card>
            <CardHeader>
              <CardTitle>Appointment Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">ID:</span>
                <span className="font-medium">#{appointment.appointmentId}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Created:</span>
                <span className="font-medium">
                  {appointment.createdAt ? format(new Date(appointment.createdAt), "MMM d, yyyy") : 'Unknown'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Last Updated:</span>
                <span className="font-medium">
                  {appointment.updatedAt ? format(new Date(appointment.updatedAt), "MMM d, yyyy") : 'Unknown'}
                </span>
              </div>

              {/* Quick Actions */}
              <div className="pt-3 border-t space-y-2">
                <Button
                  onClick={handleViewMedicalHistory}
                  className="w-full flex items-center gap-2"
                  variant="outline"
                  size="sm"
                >
                  <History className="h-4 w-4" />
                  Medical History
                </Button>

                {appointment.status === 'completed' ? (
                  <Button
                    onClick={() => navigate(`/appointments/${appointment.appointmentId}/start`)}
                    className="w-full flex items-center gap-2 bg-gray-600 hover:bg-gray-700 text-white"
                    size="sm"
                  >
                    <Eye className="h-4 w-4" />
                    View Workflow
                  </Button>
                ) : (
                  <Button
                    onClick={() => navigate(`/appointments/${appointment.appointmentId}/start`)}
                    className="w-full flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white"
                    size="sm"
                  >
                    <Stethoscope className="h-4 w-4" />
                    Start Workflow
                  </Button>
                )}

                {appointment.status !== 'completed' && areAllCategoriesComplete() && (
                  <Button
                    onClick={() => completeAppointmentMutation.mutate()}
                    disabled={completeAppointmentMutation.isPending}
                    className="w-full flex items-center gap-2 bg-green-600 hover:bg-green-700 text-white"
                    size="sm"
                  >
                    <Check className="h-4 w-4" />
                    {completeAppointmentMutation.isPending ? 'Completing...' : 'Mark as Complete'}
                  </Button>
                )}

                {appointment.status === 'completed' && (
                  <div className="space-y-2">
                    <Button
                      onClick={() => navigate(`/invoices/appointment/${appointment.appointmentId}`)}
                      className="w-full flex items-center gap-2"
                      variant="outline"
                      size="sm"
                    >
                      <Receipt className="h-4 w-4" />
                      Generate/Update Invoice
                    </Button>
                    <Button
                      onClick={() => navigate(`/receipts/appointment/${appointment.appointmentId}`)}
                      className="w-full flex items-center gap-2"
                      variant="outline"
                      size="sm"
                    >
                      <FileText className="h-4 w-4" />
                      Generate/Update Receipt
                    </Button>
                    <Button
                      onClick={() => navigate(`/appointments/follow-up/${appointment.appointmentId}`)}
                      className="w-full flex items-center gap-2"
                      variant="outline"
                      size="sm"
                    >
                      <Calendar className="h-4 w-4" />
                      Create Follow-up
                    </Button>
                  </div>
                )}

                {/* Follow-up Information */}
                {(appointment.followUp?.nextFollowUpDate || appointment.followUp?.isFollowUp) && (
                  <Card className="mt-4">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2 text-sm">
                        <Calendar className="h-4 w-4" />
                        Follow-up Information
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      {appointment.followUp?.isFollowUp && (
                        <div>
                          <p className="text-xs text-gray-600">This is a follow-up appointment</p>
                          <p className="text-sm font-medium">
                            Type: {appointment.followUp.followUpType?.replace('_', ' ') || 'General'}
                          </p>
                          {appointment.followUp.followUpReason && (
                            <p className="text-xs text-gray-600 mt-1">
                              Reason: {appointment.followUp.followUpReason}
                            </p>
                          )}
                        </div>
                      )}
                      {appointment.followUp?.nextFollowUpDate && (
                        <div>
                          <p className="text-xs text-gray-600">Next follow-up scheduled</p>
                          <p className="text-sm font-medium">
                            {format(new Date(appointment.followUp.nextFollowUpDate), 'MMM dd, yyyy')}
                          </p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Follow-up Management */}
          <FollowUpAppointment
            appointment={appointment}
            onSuccess={() => {
              queryClient.invalidateQueries({ queryKey: ['appointment', id] });
              toast({
                title: "Success",
                description: "Follow-up action completed successfully",
              });
            }}
          />
        </div>
      </div>



      {/* Complete Appointment Modal */}
      {showCompleteModal && (
        <CompleteAppointmentModal
          appointment={appointment}
          isOpen={showCompleteModal}
          onClose={() => setShowCompleteModal(false)}
          onCompleted={handleCompleteAppointment}
        />
      )}

      {/* Edit Time Modal */}
      <Dialog open={showEditTimeModal} onOpenChange={setShowEditTimeModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Appointment Time</DialogTitle>
          </DialogHeader>
          <EditTimeForm
            appointment={appointment}
            onSave={async (timeData) => {
              try {
                const response = await api.put(`/appointments/${appointment.appointmentId}`, timeData);
                if (response.data.success) {
                  queryClient.invalidateQueries({ queryKey: ['appointment', id] });
                  toast({
                    title: "Success",
                    description: "Appointment time updated successfully",
                  });
                  setShowEditTimeModal(false);
                } else {
                  throw new Error(response.data.message);
                }
              } catch (error: any) {
                toast({
                  title: "Error",
                  description: error.message || "Failed to update appointment time",
                  variant: "destructive"
                });
              }
            }}
            onCancel={() => setShowEditTimeModal(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Categories Modal */}
      <Dialog open={showEditCategoriesModal} onOpenChange={setShowEditCategoriesModal}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Appointment Categories</DialogTitle>
          </DialogHeader>
          <EditCategoriesForm
            appointment={appointment}
            availableCategories={appointmentCategories}
            onSave={async (categoriesData) => {
              try {
                const response = await api.put(`/appointments/${appointment.appointmentId}/categories`, categoriesData);
                if (response.data.success) {
                  queryClient.invalidateQueries({ queryKey: ['appointment', id] });
                  toast({
                    title: "Success",
                    description: "Appointment categories updated successfully",
                  });
                  setShowEditCategoriesModal(false);
                } else {
                  throw new Error(response.data.message);
                }
              } catch (error: any) {
                toast({
                  title: "Error",
                  description: error.message || "Failed to update appointment categories",
                  variant: "destructive"
                });
              }
            }}
            onCancel={() => setShowEditCategoriesModal(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Category Charges Modal */}
      <Dialog open={showChargesModal} onOpenChange={setShowChargesModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Category Charges</DialogTitle>
          </DialogHeader>
          {selectedCategoryForCharges && (
            <EditCategoryChargesForm
              category={selectedCategoryForCharges}
              onSave={async (chargesData) => {
                try {
                  const response = await api.put(`/appointments/${appointment.appointmentId}/category-charges`, {
                    appointmentCategoryId: selectedCategoryForCharges.appointmentCategoryId,
                    ...chargesData
                  });
                  if (response.data.success) {
                    queryClient.invalidateQueries({ queryKey: ['appointment', id] });
                    toast({
                      title: "Success",
                      description: "Category charges updated successfully",
                    });
                    setShowChargesModal(false);
                    setSelectedCategoryForCharges(null);
                  } else {
                    throw new Error(response.data.message);
                  }
                } catch (error: any) {
                  toast({
                    title: "Error",
                    description: error.message || "Failed to update category charges",
                    variant: "destructive"
                  });
                }
              }}
              onCancel={() => {
                setShowChargesModal(false);
                setSelectedCategoryForCharges(null);
              }}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

// Edit Time Form Component
interface EditTimeFormProps {
  appointment: Appointment;
  onSave: (data: any) => void;
  onCancel: () => void;
}

const EditTimeForm: React.FC<EditTimeFormProps> = ({ appointment, onSave, onCancel }) => {
  const appointmentDateTime = new Date(appointment.appointmentDate);
  const [formData, setFormData] = useState({
    appointmentDate: format(appointmentDateTime, 'yyyy-MM-dd'),
    appointmentTime: format(appointmentDateTime, 'HH:mm')
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Combine date and time into a single DateTime
    const combinedDateTime = new Date(`${formData.appointmentDate}T${formData.appointmentTime}`);
    onSave({ appointmentDate: combinedDateTime.toISOString() });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <Label htmlFor="appointmentDate">Appointment Date</Label>
        <Input
          id="appointmentDate"
          type="date"
          value={formData.appointmentDate}
          onChange={(e) => setFormData({ ...formData, appointmentDate: e.target.value })}
          required
        />
      </div>
      <div>
        <Label htmlFor="appointmentTime">Appointment Time</Label>
        <Input
          id="appointmentTime"
          type="time"
          value={formData.appointmentTime}
          onChange={(e) => setFormData({ ...formData, appointmentTime: e.target.value })}
          required
        />
      </div>
      <div className="flex justify-end gap-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit">
          Save Changes
        </Button>
      </div>
    </form>
  );
};

// Edit Categories Form Component
interface EditCategoriesFormProps {
  appointment: Appointment;
  availableCategories: any[];
  onSave: (data: any) => void;
  onCancel: () => void;
}

const EditCategoriesForm: React.FC<EditCategoriesFormProps> = ({
  appointment,
  availableCategories,
  onSave,
  onCancel
}) => {
  const [selectedCategories, setSelectedCategories] = useState<number[]>(
    appointment.appointmentCategories?.map((ac: any) => ac.appointmentCategoryId) || []
  );

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave({ appointmentCategoryIds: selectedCategories });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <Label>Select Categories</Label>
        <div className="grid gap-2 max-h-96 overflow-y-auto mt-2">
          {availableCategories.map((category: any) => (
            <div key={category.appointmentCategoryId} className="flex items-center space-x-3 p-3 border rounded-lg">
              <Checkbox
                checked={selectedCategories.includes(category.appointmentCategoryId)}
                onCheckedChange={(checked) => {
                  if (checked) {
                    setSelectedCategories([...selectedCategories, category.appointmentCategoryId]);
                  } else {
                    setSelectedCategories(selectedCategories.filter(id => id !== category.appointmentCategoryId));
                  }
                }}
              />
              <div className="flex-1">
                <h4 className="font-medium">{category.name}</h4>
                <p className="text-sm text-gray-600">{category.description}</p>
                <div className="flex items-center gap-4 mt-1">
                  <span className="text-xs text-green-600">
                    KES {category.defaultCharge?.toLocaleString() || 0}
                  </span>
                  <span className="text-xs text-gray-500">
                    {category.estimatedDuration || 30} min
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
      <div className="flex justify-end gap-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit">
          Save Changes
        </Button>
      </div>
    </form>
  );
};

// Edit Category Charges Form Component
interface EditCategoryChargesFormProps {
  category: any;
  onSave: (data: any) => void;
  onCancel: () => void;
}

const EditCategoryChargesForm: React.FC<EditCategoryChargesFormProps> = ({
  category,
  onSave,
  onCancel
}) => {
  const [formData, setFormData] = useState({
    charge: category.charge || 0,
    discountPercentage: category.discountPercentage || 0,
    notes: category.notes || ''
  });

  const finalAmount = formData.charge - (formData.charge * formData.discountPercentage / 100);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="charge">Base Charge (KES)</Label>
          <Input
            id="charge"
            type="number"
            min="0"
            step="0.01"
            value={formData.charge}
            onChange={(e) => setFormData({ ...formData, charge: parseFloat(e.target.value) || 0 })}
          />
        </div>
        <div>
          <Label htmlFor="discountPercentage">Discount (%)</Label>
          <Input
            id="discountPercentage"
            type="number"
            min="0"
            max="100"
            step="0.1"
            value={formData.discountPercentage}
            onChange={(e) => setFormData({ ...formData, discountPercentage: parseFloat(e.target.value) || 0 })}
          />
        </div>
      </div>

      <div className="p-3 bg-gray-50 rounded-lg">
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-600">Final Amount:</span>
          <span className="font-bold text-green-700">KES {finalAmount.toLocaleString()}</span>
        </div>
      </div>

      <div>
        <Label htmlFor="notes">Notes</Label>
        <Textarea
          id="notes"
          value={formData.notes}
          onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
          placeholder="Any special notes for this category charge..."
          rows={3}
        />
      </div>

      <div className="flex justify-end gap-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit">
          Save Changes
        </Button>
      </div>
    </form>
  );
};

export default AppointmentDetails;
