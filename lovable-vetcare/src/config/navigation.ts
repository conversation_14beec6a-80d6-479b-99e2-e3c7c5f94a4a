/**
 * Permission-Aware Navigation Configuration
 * 
 * This file defines the navigation structure with role-based permissions
 * to control which menu items are visible to different user types.
 */

import {
  Home,
  Users,
  PawPrint,
  Calendar,
  ClipboardList,
  DollarSign,
  Package2,
  Shield,
  Building2,
  Settings,
  BarChart2,
  Cat,
  Dna,
  Bell,
  Truck,
  Clipboard,
  Activity,
  HeartPulse,
  Microscope,
  FileText
} from 'lucide-react';

import { PERMISSIONS, ROLES } from '@/utils/permissions';

export interface NavigationItem {
  name: string;
  to?: string;
  icon: any;
  emoji?: string;
  color?: string;
  description?: string;
  permission?: string | string[];
  role?: string | string[];
  requireAll?: boolean;
  items?: NavigationItem[];
}

export const navigationConfig: NavigationItem[] = [
  {
    name: "Dashboard",
    to: "/dashboard",
    icon: Home,
    emoji: "🏠",
    color: "from-blue-500 to-blue-600",
    description: "Overview & Analytics"
  },
  {
    name: "Clients & Pets",
    icon: Users,
    emoji: "👩‍⚕️",
    color: "from-green-500 to-green-600",
    description: "Manage clients and pets",
    permission: PERMISSIONS.PATIENT_MANAGEMENT,
    items: [
      {
        name: "All Clients",
        to: "/clients",
        icon: Users,
        emoji: "👥",
        permission: PERMISSIONS.PATIENT_MANAGEMENT
      },
      {
        name: "Client Groups",
        to: "/client-groups",
        icon: Users,
        emoji: "👪",
        permission: PERMISSIONS.PATIENT_MANAGEMENT
      },
      {
        name: "All Pets",
        to: "/pets",
        icon: PawPrint,
        emoji: "🐾",
        permission: PERMISSIONS.PATIENT_MANAGEMENT
      },
      {
        name: "Species",
        to: "/species",
        icon: Cat,
        emoji: "🐱",
        permission: PERMISSIONS.PATIENT_MANAGEMENT
      },
      {
        name: "Breeds",
        to: "/breeds",
        icon: Dna,
        emoji: "🧬",
        permission: PERMISSIONS.PATIENT_MANAGEMENT
      },
      {
        name: "Medical History",
        to: "/pets/medical-history",
        icon: ClipboardList,
        emoji: "📋",
        permission: PERMISSIONS.MEDICAL_RECORDS
      }
    ]
  },
  {
    name: "Appointments",
    to: "/appointments",
    icon: Calendar,
    emoji: "📅",
    color: "from-purple-500 to-purple-600",
    description: "Schedule & manage appointments",
    permission: PERMISSIONS.APPOINTMENT_MANAGEMENT
  },
  {
    name: "Medical Records",
    icon: ClipboardList,
    color: "from-red-500 to-red-600",
    description: "Health records & treatments",
    permission: PERMISSIONS.MEDICAL_RECORDS,
    items: [
      {
        name: "All Records",
        to: "/medical-records",
        icon: Clipboard,
        permission: PERMISSIONS.MEDICAL_RECORDS
      },
      {
        name: "Consultations",
        to: "/records/consultations",
        icon: Activity,
        permission: PERMISSIONS.MEDICAL_RECORDS
      },
      {
        name: "Vaccinations",
        to: "/records/vaccinations",
        icon: HeartPulse,
        permission: PERMISSIONS.MEDICAL_RECORDS
      },
      {
        name: "Laboratory",
        to: "/records/laboratory",
        icon: Microscope,
        permission: PERMISSIONS.MEDICAL_RECORDS
      }
    ]
  },
  {
    name: "Billing & Finance",
    icon: DollarSign,
    color: "from-yellow-500 to-yellow-600",
    description: "Invoices & payments",
    permission: PERMISSIONS.BILLING_MANAGEMENT,
    items: [
      {
        name: "Invoices",
        to: "/invoices",
        icon: FileText,
        permission: PERMISSIONS.BILLING_MANAGEMENT
      },
      {
        name: "Receipts",
        to: "/receipts",
        icon: FileText,
        permission: PERMISSIONS.BILLING_MANAGEMENT
      },
      {
        name: "Reports",
        to: "/billing/reports",
        icon: BarChart2,
        permission: PERMISSIONS.BILLING_MANAGEMENT
      }
    ]
  },
  {
    name: "Inventory",
    to: "/inventory",
    icon: Package2,
    emoji: "📦",
    color: "from-orange-500 to-orange-600",
    description: "Stock & supplies management",
    permission: PERMISSIONS.INVENTORY_MANAGEMENT,
    items: [
      {
        name: "All Items",
        to: "/inventory",
        icon: Package2,
        emoji: "📋",
        permission: PERMISSIONS.INVENTORY_MANAGEMENT
      },
      {
        name: "Categories",
        to: "/inventory/categories",
        icon: Clipboard,
        emoji: "🗂️",
        permission: PERMISSIONS.INVENTORY_MANAGEMENT
      },
      {
        name: "Stock Alerts",
        to: "/inventory/alerts",
        icon: Bell,
        emoji: "🔔",
        permission: PERMISSIONS.INVENTORY_MANAGEMENT
      },
      {
        name: "Suppliers",
        to: "/inventory/suppliers",
        icon: Truck,
        emoji: "🚚",
        permission: PERMISSIONS.INVENTORY_MANAGEMENT
      }
    ]
  },
  {
    name: "Practice Management",
    icon: Building2,
    emoji: "🏢",
    color: "from-gray-500 to-gray-600",
    description: "System settings & users",
    permission: [PERMISSIONS.CLINIC_MANAGEMENT, PERMISSIONS.STAFF_MANAGEMENT],
    items: [
      {
        name: "Clinic Management",
        to: "/admin/clinics",
        icon: Building2,
        emoji: "🏥",
        permission: PERMISSIONS.CLINIC_MANAGEMENT
      },
      {
        name: "Staff Management",
        to: "/admin/staff",
        icon: Users,
        emoji: "👨‍⚕️",
        permission: PERMISSIONS.STAFF_MANAGEMENT
      },
      {
        name: "Roles & Permissions",
        to: "/admin/roles",
        icon: Shield,
        emoji: "🔒",
        role: [ROLES.SUPER_ADMIN, ROLES.ADMIN, ROLES.CLINIC_OWNER]
      },
      {
        name: "Admin Settings",
        to: "/admin/settings",
        icon: Settings,
        emoji: "⚙️",
        role: [ROLES.SUPER_ADMIN, ROLES.ADMIN, ROLES.CLINIC_ADMIN]
      }
    ]
  },
  {
    name: "Reports",
    icon: BarChart2,
    emoji: "📊",
    color: "from-indigo-500 to-indigo-600",
    description: "Analytics & insights",
    permission: PERMISSIONS.REPORTS_VIEW,
    items: [
      {
        name: "Financial Reports",
        to: "/reports/financial",
        icon: DollarSign,
        emoji: "💰",
        permission: PERMISSIONS.BILLING_MANAGEMENT
      },
      {
        name: "Appointment Reports",
        to: "/reports/appointments",
        icon: Calendar,
        emoji: "📅",
        permission: PERMISSIONS.APPOINTMENT_MANAGEMENT
      },
      {
        name: "Inventory Reports",
        to: "/reports/inventory",
        icon: Package2,
        emoji: "📦",
        permission: PERMISSIONS.INVENTORY_MANAGEMENT
      },
      {
        name: "Client Reports",
        to: "/reports/clients",
        icon: Users,
        emoji: "👥",
        permission: PERMISSIONS.PATIENT_MANAGEMENT
      },
      {
        name: "Staff Performance",
        to: "/reports/staff",
        icon: Activity,
        emoji: "📈",
        permission: PERMISSIONS.STAFF_MANAGEMENT
      }
    ]
  },
  {
    name: "Settings",
    to: "/settings",
    icon: Settings,
    emoji: "⚙️",
    description: "Personal & system settings"
  }
];

/**
 * Admin-only navigation items
 */
export const adminNavigationConfig: NavigationItem[] = [
  {
    name: "Admin Dashboard",
    to: "/admin",
    icon: Shield,
    emoji: "🔐",
    color: "from-red-500 to-red-600",
    description: "System administration",
    role: [ROLES.SUPER_ADMIN, ROLES.ADMIN]
  },
  {
    name: "System Management",
    icon: Settings,
    color: "from-gray-500 to-gray-600",
    description: "System-wide settings",
    role: [ROLES.SUPER_ADMIN, ROLES.ADMIN],
    items: [
      {
        name: "All Clinics",
        to: "/admin/all-clinics",
        icon: Building2,
        role: [ROLES.SUPER_ADMIN, ROLES.ADMIN]
      },
      {
        name: "System Users",
        to: "/admin/users",
        icon: Users,
        role: [ROLES.SUPER_ADMIN, ROLES.ADMIN]
      },
      {
        name: "Global Settings",
        to: "/admin/global-settings",
        icon: Settings,
        role: [ROLES.SUPER_ADMIN]
      },
      {
        name: "System Logs",
        to: "/admin/logs",
        icon: FileText,
        role: [ROLES.SUPER_ADMIN, ROLES.ADMIN]
      }
    ]
  }
];

/**
 * Filter navigation items based on user permissions
 */
export const filterNavigationByPermissions = (
  navigation: NavigationItem[],
  hasPermission: (permission: string) => boolean,
  hasRole: (role: string) => boolean,
  hasAnyPermission: (permissions: string[]) => boolean,
  hasAnyRole: (roles: string[]) => boolean
): NavigationItem[] => {
  return navigation.filter(item => {
    // Check permissions
    if (item.permission) {
      if (Array.isArray(item.permission)) {
        if (!hasAnyPermission(item.permission)) return false;
      } else {
        if (!hasPermission(item.permission)) return false;
      }
    }

    // Check roles
    if (item.role) {
      if (Array.isArray(item.role)) {
        if (!hasAnyRole(item.role)) return false;
      } else {
        if (!hasRole(item.role)) return false;
      }
    }

    // Filter sub-items
    if (item.items) {
      item.items = filterNavigationByPermissions(
        item.items,
        hasPermission,
        hasRole,
        hasAnyPermission,
        hasAnyRole
      );
      
      // Hide parent if no sub-items are visible
      if (item.items.length === 0 && !item.to) {
        return false;
      }
    }

    return true;
  });
};
