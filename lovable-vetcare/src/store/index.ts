/**
 * Root Store
 *
 * This file combines all store slices into a single store.
 */

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { createAuthSlice, AuthSlice } from './slices/authSlice';
import { createUISlice, UISlice } from './slices/uiSlice';
import { createReferenceDataSlice, ReferenceDataSlice } from './slices/referenceDataSlice';

// Combined store type
export type RootState = AuthSlice & UISlice & ReferenceDataSlice;

// Create the root store with all slices
export const useStore = create<RootState>()(
  persist(
    (...a) => ({
      ...createAuthSlice(...a),
      ...createUISlice(...a),
      ...createReferenceDataSlice(...a),
    }),
    {
      name: 'vet-care-store',
      // Only persist auth state, not UI state
      partialize: (state) => ({
        isAuthenticated: state.isAuthenticated,
        user: state.user,
        token: state.token,
        employee: state.employee,
        clinic: state.clinic,
        availableClinics: state.availableClinics,
      }),
    }
  )
);

// Export individual hooks for better type safety and component optimization
export const useAuth = () => {
  const {
    isAuthenticated, user, token, employee, clinic, availableClinics,
    login, logout, switchClinic, getToken, updateUser, updateStaff
  } = useStore();

  return {
    isAuthenticated, user, token, employee, clinic,
    availableClinics: availableClinics || [],
    currentClinic: clinic,
    login, logout, switchClinic, getToken, updateUser, updateStaff,
    // Map staff to employee for compatibility
    staff: employee
  };
};

export const useUI = () => {
  const {
    sidebarOpen, isMobileView, activeTheme,
    toggleSidebar, setSidebarOpen, setMobileView, setTheme
  } = useStore();

  return {
    sidebarOpen, isMobileView, activeTheme,
    toggleSidebar, setSidebarOpen, setMobileView, setTheme
  };
};

export const useReferenceData = () => {
  const {
    // Data
    species, breeds, breedsBySpecies, staff, roles, appointmentCategories, clients,
    // Loading states
    isLoadingSpecies, isLoadingBreeds, isLoadingStaff, isLoadingRoles,
    isLoadingAppointmentCategories, isLoadingClients,
    // Actions
    fetchSpecies, fetchBreeds, fetchBreedsBySpecies, fetchStaff, fetchRoles,
    fetchAppointmentCategories, fetchClients, clearCache, clearSpecificCache,
    // Getters
    getSpeciesData, getBreedsData, getBreedsBySpeciesData, getStaffData,
    getRolesData, getAppointmentCategoriesData, getClientsData
  } = useStore();

  return {
    // Data
    species, breeds, breedsBySpecies, staff, roles, appointmentCategories, clients,
    // Loading states
    isLoadingSpecies, isLoadingBreeds, isLoadingStaff, isLoadingRoles,
    isLoadingAppointmentCategories, isLoadingClients,
    // Actions
    fetchSpecies, fetchBreeds, fetchBreedsBySpecies, fetchStaff, fetchRoles,
    fetchAppointmentCategories, fetchClients, clearCache, clearSpecificCache,
    // Getters with automatic fetching
    getSpeciesData, getBreedsData, getBreedsBySpeciesData, getStaffData,
    getRolesData, getAppointmentCategoriesData, getClientsData
  };
};

// Re-export for backward compatibility
export * from './types';

// For backward compatibility
export const useAuthStore = {
  getState: () => {
    const state = useStore.getState();
    return {
      isAuthenticated: state.isAuthenticated,
      user: state.user,
      token: state.token,
      employee: state.employee,
      staff: state.employee, // Map employee to staff for compatibility
      clinic: state.clinic,
      currentClinic: state.clinic, // Map clinic to currentClinic for compatibility
      availableClinics: [], // Default empty array for compatibility
      login: state.login,
      // Make sure logout is properly forwarded to the new store
      logout: () => {
        state.logout();
      },
      getToken: state.getToken,
      // Add update functions that update the store
      updateUser: (updatedUser) => {
        // Forward to the new store's updateUser if available
        if (typeof state.updateUser === 'function') {
          state.updateUser(updatedUser);
        } else {
          console.log('Update user called with:', updatedUser);
        }
      },
      updateStaff: (updatedStaff) => {
        // Forward to the new store's updateStaff if available
        if (typeof state.updateStaff === 'function') {
          state.updateStaff(updatedStaff);
        } else {
          console.log('Update staff called with:', updatedStaff);
        }
      },
      // Add switchClinic stub for compatibility
      switchClinic: async (clinicId) => {
        console.log('Switch clinic called with:', clinicId);
        return {
          success: false,
          status: 501,
          message: 'Not implemented in new store yet',
          data: {}
        };
      }
    };
  },
};
