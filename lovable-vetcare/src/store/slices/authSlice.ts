/**
 * Enhanced Auth Store Slice
 *
 * This slice handles authentication state and operations with improved
 * multi-clinic support, role-based access, and freelancer management.
 */

import { StateCreator } from 'zustand';
import { api } from '@/services/api';
import { User, Staff, Clinic, StandardResponse } from '../types';

// Auth slice state interface
export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
  employee: Staff | null;
  clinic: Clinic | null;
  availableClinics: Clinic[];
  userType: 'user' | 'staff' | 'admin' | 'freelancer' | null;
  permissions: string[];
  sessionInfo: {
    loginTime: Date | null;
    lastActivity: Date | null;
    ipAddress: string | null;
  };
}

// Auth slice actions interface
export interface AuthActions {
  login: (email: string, password: string) => Promise<StandardResponse<{
    token: string;
    user: User;
    staff?: Staff;
    clinics?: Clinic[];
  }>>;
  logout: () => void;
  switchClinic: (clinicId: number) => Promise<StandardResponse<any>>;
  refreshToken: () => Promise<boolean>;
  getToken: () => string | null;
  updateUser: (updatedUser: Partial<User>) => void;
  updateStaff: (updatedStaff: Partial<Staff>) => void;
  updatePermissions: (permissions: string[]) => void;
  updateLastActivity: () => void;
}

// Combined auth slice type
export type AuthSlice = AuthState & AuthActions;

// Auth slice creator function
export const createAuthSlice: StateCreator<
  AuthSlice,
  [],
  [],
  AuthSlice
> = (set, get) => ({
  // Initial state
  isAuthenticated: false,
  user: null,
  token: null,
  employee: null,
  clinic: null,
  availableClinics: [],
  userType: null,
  permissions: [],
  sessionInfo: {
    loginTime: null,
    lastActivity: null,
    ipAddress: null
  },

  // Actions
  login: async (email, password) => {
    try {
      const response = await api.post<StandardResponse<{
        token: string;
        user: User;
        staff?: Staff;
        clinics?: Clinic[];
      }>>('/auth/sign-in', { email, password });

      const { status, message, data } = response;

      if (status === 200) {
        const userType = data.staff ? 'staff' : 'user';
        const clinics = data.clinics || [];
        const currentClinic = data.staff?.currentClinicId
          ? clinics.find(c => c.clinicId === data.staff?.currentClinicId) || (clinics.length > 0 ? clinics[0] : null)
          : (clinics.length > 0 ? clinics[0] : null);

        set({
          isAuthenticated: true,
          user: data.user,
          token: data.token,
          employee: data.staff,
          clinic: currentClinic,
          availableClinics: clinics,
          userType,
          sessionInfo: {
            loginTime: new Date(),
            lastActivity: new Date(),
            ipAddress: null // Will be set by backend if needed
          }
        });

        return {
          success: true,
          status,
          message: message || 'User signed in successfully',
          data
        };
      }
      return {
        success: false,
        status,
        message: message || 'Login failed',
        data
      };
    } catch (error: any) {
      return {
        success: false,
        status: error?.status || 500,
        message: error?.message || 'Login failed',
        data: error?.data || {}
      };
    }
  },

  logout: () => {
    // Clear all auth state
    set({
      isAuthenticated: false,
      user: null,
      token: null,
      employee: null,
      clinic: null,
      availableClinics: [],
      userType: null,
      permissions: [],
      sessionInfo: {
        loginTime: null,
        lastActivity: null,
        ipAddress: null
      }
    });

    // Clear localStorage to ensure all persisted auth data is removed
    localStorage.removeItem('vet-care-store');

    // Redirect to home page
    window.location.href = '/';
  },

  switchClinic: async (clinicId: number) => {
    try {
      const response = await api.post<StandardResponse<any>>('/auth/switch-clinic', { clinicId });

      if (response.status === 200) {
        const newClinic = get().availableClinics.find(c => c.clinicId === clinicId);
        if (newClinic) {
          set({ clinic: newClinic });
        }
        return response;
      }
      return response;
    } catch (error: any) {
      return {
        success: false,
        status: error?.status || 500,
        message: error?.message || 'Failed to switch clinic',
        data: error?.data || {}
      };
    }
  },

  refreshToken: async () => {
    try {
      const response = await api.post<StandardResponse<{ token: string }>>('/auth/refresh-token');

      if (response.status === 200) {
        set({ token: response.data.token });
        return true;
      }
      return false;
    } catch (error) {
      console.error('Token refresh failed:', error);
      return false;
    }
  },

  getToken: () => get().token,

  updateUser: (updatedUser) => {
    const currentUser = get().user;
    if (currentUser) {
      set({
        user: { ...currentUser, ...updatedUser }
      });
    }
  },

  updateStaff: (updatedStaff) => {
    const currentStaff = get().employee;
    if (currentStaff) {
      set({
        employee: { ...currentStaff, ...updatedStaff }
      });
    }
  },

  updatePermissions: (permissions) => {
    set({ permissions });
  },

  updateLastActivity: () => {
    const currentSession = get().sessionInfo;
    set({
      sessionInfo: {
        ...currentSession,
        lastActivity: new Date()
      }
    });
  },
});
