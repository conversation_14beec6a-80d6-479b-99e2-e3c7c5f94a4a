
import { Appointment, PaginationData } from '@/store/types';
import { api } from './api';
import { useStore } from '@/store';

export interface AppointmentResponse {
  success: boolean;
  status: number;
  message: string;
  data: Appointment | null;
}

export interface AppointmentsResponse {
  success: boolean;
  status: number;
  message: string;
  data: {
    data: Appointment[];
    pagination: PaginationData;
  };
}

export interface AppointmentSearchParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: number;
  date?: string;
  clinicId?: string;
  petId?: string;
  clientId?: string;
  staffId?: string;
  [key: string]: any;
}

// Helper function to get clinic context
const getClinicId = () => {
  const state = useStore.getState();
  return state.clinic?.clinicId || state.employee?.primaryClinicId || state.employee?.clinicId;
};

export const getAppointments = async (params: AppointmentSearchParams = {}): Promise<AppointmentsResponse> => {
  try {
    const queryParams = new URLSearchParams();

    queryParams.append('page', String(params.page || 1));
    queryParams.append('limit', String(params.limit || 10));

    // Temporarily disable automatic clinicId filtering for debugging
    // const clinicId = getClinicId();
    // if (clinicId && !params.clinicId) {
    //   params.clinicId = clinicId.toString();
    // }

    Object.entries(params).forEach(([key, value]) => {
      if (value && !['page', 'limit'].includes(key)) {
        queryParams.append(key, String(value));
      }
    });

    const url = `/appointments?${queryParams.toString()}`;
    console.log("🌐 Fetching appointments from URL:", url);

    const response = await api.get<AppointmentsResponse>(url);
    console.log("📡 Raw appointments API response:", response);

    return response;
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to fetch appointments',
      data: {
        data: [],
        pagination: {
          totalCount: 0,
          page: 1,
          limit: 10,
          offset: 0,
          totalPages: 0
        }
      }
    };
  }
};

export const getAppointmentById = async (appointmentId: string | number): Promise<AppointmentResponse> => {
  try {
    // Validate appointmentId before making the request
    if (!appointmentId || appointmentId === 'undefined' || appointmentId === 'null') {
      console.error("❌ Invalid appointmentId provided:", appointmentId);
      return {
        success: false,
        status: 400,
        message: 'Invalid appointment ID provided',
        data: null
      };
    }

    console.log("🔍 Fetching appointment by ID:", appointmentId);
    const response = await api.get<AppointmentResponse>(`/appointments/${appointmentId}`);
    console.log("📡 Appointment details response:", response);
    return response;
  } catch (error: any) {
    console.error("❌ Error fetching appointment:", error);
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to fetch appointment',
      data: null
    };
  }
};

export const createAppointment = async (appointmentData: Partial<Appointment>): Promise<AppointmentResponse> => {
  try {
    return await api.post<AppointmentResponse>('/appointments', appointmentData);
  } catch (error: any) {
    throw {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to create appointment',
      data: null
    };
  }
};

export const updateAppointment = async (
  appointmentId: string,
  appointmentData: Partial<Omit<Appointment, '_id'>>
): Promise<AppointmentResponse> => {
  try {
    return await api.put<AppointmentResponse>(`/appointments/${appointmentId}`, appointmentData);
  } catch (error: any) {
    throw {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to update appointment',
      data: null
    };
  }
};

export const completeAppointment = async (appointmentId: string): Promise<AppointmentResponse> => {
  try {
    return await api.put<AppointmentResponse>(`/appointments/${appointmentId}/complete`);
  } catch (error: any) {
    throw {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to complete appointment',
      data: null
    };
  }
};

export const deleteAppointment = async (appointmentId: string): Promise<AppointmentResponse> => {
  try {
    return await api.delete<AppointmentResponse>(`/appointments/${appointmentId}`);
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to delete appointment',
      data: null
    };
  }
};

/**
 * Interface for appointment type response
 */
export interface AppointmentTypesResponse {
  success: boolean;
  status: number;
  message: string;
  data: {
    categories: Array<{
      categoryId?: number;
      name: string;
      description?: string;
      estimatedDuration: number;
      defaultPrice: number;
      currency: string;
      isActive: boolean;
      icon?: string;
      color?: string;
      displayOrder?: number;
      defaultStaffRoles?: string[];
      requiresEquipment?: boolean;
      requiresQualification?: boolean;
      isEmergency?: boolean;
      tags?: string[];
    }>;
    pagination: {
      totalCount: number;
      page: number;
      limit: number;
      offset: number;
      totalPages: number;
    };
  } | null;
}

/**
 * Get available appointment types
 */
export const getAppointmentTypes = async (): Promise<AppointmentTypesResponse> => {
  try {
    return await api.get<AppointmentTypesResponse>('/appointments/types');
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to fetch appointment types',
      data: null
    };
  }
};

/**
 * Get appointments for a specific client
 */
export const getAppointmentsByClient = async (clientId: string, params: AppointmentSearchParams = {}): Promise<AppointmentsResponse> => {
  try {
    const queryParams = new URLSearchParams();

    queryParams.append('page', String(params.page || 1));
    queryParams.append('limit', String(params.limit || 10));

    Object.entries(params).forEach(([key, value]) => {
      if (value && !['page', 'limit', 'clientId'].includes(key)) {
        queryParams.append(key, String(value));
      }
    });

    return await api.get<AppointmentsResponse>(`/appointments/client/${clientId}?${queryParams.toString()}`);
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to fetch client appointments',
      data: {
        data: [],
        pagination: {
          totalCount: 0,
          page: 1,
          limit: 10,
          offset: 0,
          totalPages: 0
        }
      }
    };
  }
};

// Follow-up appointment interfaces
export interface FollowUpData {
  appointmentDate: string;
  staffInCharge?: number;
  priority?: 'low' | 'normal' | 'high' | 'emergency';
  reason?: string;
  type?: 'routine_checkup' | 'medication_review' | 'surgery_followup' | 'vaccination_reminder' | 'treatment_progress' | 'lab_results' | 'behavioral_assessment' | 'dental_checkup' | 'emergency_followup' | 'other';
  notes?: string;
}

export interface FollowUpReminderData {
  followUpDate: string;
  instructions?: string;
  priority?: 'low' | 'normal' | 'high' | 'urgent';
}

export interface AppointmentChainResponse {
  success: boolean;
  status: number;
  message: string;
  data: {
    parent: Appointment;
    followUps: Appointment[];
    totalAppointments: number;
  } | null;
}

/**
 * Create a follow-up appointment
 */
export const createFollowUpAppointment = async (
  parentAppointmentId: string | number,
  followUpData: FollowUpData
): Promise<AppointmentResponse> => {
  try {
    return await api.post<AppointmentResponse>(
      `/appointments/${parentAppointmentId}/follow-up`,
      followUpData
    );
  } catch (error: any) {
    throw {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to create follow-up appointment',
      data: null
    };
  }
};

/**
 * Schedule a follow-up reminder
 */
export const scheduleFollowUpReminder = async (
  appointmentId: string | number,
  reminderData: FollowUpReminderData
): Promise<AppointmentResponse> => {
  try {
    return await api.post<AppointmentResponse>(
      `/appointments/${appointmentId}/follow-up-reminder`,
      reminderData
    );
  } catch (error: any) {
    throw {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to schedule follow-up reminder',
      data: null
    };
  }
};

/**
 * Get appointment chain (parent + all follow-ups)
 */
export const getAppointmentChain = async (
  appointmentId: string | number
): Promise<AppointmentChainResponse> => {
  try {
    return await api.get<AppointmentChainResponse>(`/appointments/${appointmentId}/chain`);
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to fetch appointment chain',
      data: null
    };
  }
};

/**
 * Get overdue follow-ups for clinic
 */
export const getOverdueFollowUps = async (
  clinicId?: string | number,
  daysOverdue: number = 0
): Promise<AppointmentsResponse> => {
  try {
    const queryParams = new URLSearchParams();
    if (clinicId) queryParams.append('clinicId', String(clinicId));
    if (daysOverdue > 0) queryParams.append('daysOverdue', String(daysOverdue));

    return await api.get<AppointmentsResponse>(`/appointments/follow-ups/overdue?${queryParams.toString()}`);
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to fetch overdue follow-ups',
      data: {
        data: [],
        pagination: {
          totalCount: 0,
          page: 1,
          limit: 10,
          offset: 0,
          totalPages: 0
        }
      }
    };
  }
};

/**
 * Get upcoming follow-ups for clinic
 */
export const getUpcomingFollowUps = async (
  clinicId?: string | number,
  daysAhead: number = 7
): Promise<AppointmentsResponse> => {
  try {
    const queryParams = new URLSearchParams();
    if (clinicId) queryParams.append('clinicId', String(clinicId));
    if (daysAhead > 0) queryParams.append('daysAhead', String(daysAhead));

    return await api.get<AppointmentsResponse>(`/appointments/follow-ups/upcoming?${queryParams.toString()}`);
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to fetch upcoming follow-ups',
      data: {
        data: [],
        pagination: {
          totalCount: 0,
          page: 1,
          limit: 10,
          offset: 0,
          totalPages: 0
        }
      }
    };
  }
};
