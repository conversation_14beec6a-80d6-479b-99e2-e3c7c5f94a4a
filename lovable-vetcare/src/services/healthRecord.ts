import { PaginationData } from '@/store/types';
import { api } from './api';

export interface VitalSigns {
  temperature?: string;
  heartRate?: string;
  respiratoryRate?: string;
  weight?: string;
  bloodPressure?: string;
  [key: string]: string | undefined;
}

export interface Medication {
  name: string;
  dosage: string;
  frequency: string;
  duration?: string;
  notes?: string;
  prescribedBy?: string;
}

export interface LabResult {
  testName: string;
  result: string;
  normalRange?: string;
  interpretation?: string;
  performedBy?: string;
  performedDate?: Date;
}

export interface HealthRecord {
  _id: string;
  recordType: string;
  petId: {
    _id: string;
    name: string;
    species?: {
      _id: string;
      name: string;
    };
    breed?: {
      _id: string;
      name: string;
    };
  };
  clinicId: {
    _id: string;
    clinicName: string;
  };
  performedBy: {
    _id: string;
    firstName: string;
    lastName: string;
  };
  appointmentId?: {
    _id: string;
    appointmentDate: Date;
    status: string;
  };
  serviceId?: {
    _id: string;
    name: string;
  };
  date: Date;
  description?: string;
  diagnosis?: string;
  treatment?: string;
  medications?: Medication[];
  labResults?: LabResult[];
  vitalSigns?: VitalSigns;
  attachments?: string[];
  followUpDate?: Date;
  followUpInstructions?: string;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface HealthRecordResponse {
  success: boolean;
  status: number;
  message: string;
  data: HealthRecord | null;
}

export interface HealthRecordsResponse {
  success: boolean;
  status: number;
  message: string;
  data: {
    data: HealthRecord[];
    pagination: PaginationData;
  };
}

export interface HealthRecordSearchParams {
  page?: number;
  limit?: number;
  recordType?: string;
  startDate?: string;
  endDate?: string;
  clinicId?: string;
  includeDetails?: boolean;
  [key: string]: any;
}

export interface HealthRecordCreateParams {
  recordType: string;
  petId: string;
  clinicId: string;
  performedBy: string;
  appointmentId?: string;
  serviceId?: string;
  date: Date;
  description?: string;
  diagnosis?: string;
  treatment?: string;
  medications?: Omit<Medication, 'prescribedBy'>[];
  labResults?: Omit<LabResult, 'performedBy' | 'performedDate'>[];
  vitalSigns?: VitalSigns;
  attachments?: string[];
  followUpDate?: Date;
  followUpInstructions?: string;
  notes?: string;
}

/**
 * Get health records for a specific pet with pagination and filtering
 */
export const getHealthRecordsByPet = async (
  petId: string,
  params: HealthRecordSearchParams = {}
): Promise<HealthRecordsResponse> => {
  try {
    const queryParams = new URLSearchParams();

    queryParams.append('page', String(params.page || 1));
    queryParams.append('limit', String(params.limit || 10));

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && !['page', 'limit'].includes(key)) {
        queryParams.append(key, String(value));
      }
    });

    return await api.get<HealthRecordsResponse>(`/health-records/pet/${petId}?${queryParams.toString()}`);
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to fetch health records',
      data: {
        data: [],
        pagination: {
          totalCount: 0,
          page: 1,
          limit: 10,
          offset: 0,
          totalPages: 0
        }
      }
    };
  }
};

/**
 * Get health records by appointment ID
 */
export const getHealthRecordsByAppointment = async (
  appointmentId: string | number,
  includeDetails: boolean = false
): Promise<HealthRecordsResponse> => {
  try {
    const queryParams = new URLSearchParams();
    if (includeDetails) {
      queryParams.append('includeDetails', 'true');
    }

    const url = `/health-records/appointment/${appointmentId}${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return await api.get<HealthRecordsResponse>(url);
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to fetch health records for appointment',
      data: {
        data: [],
        count: 0,
        appointmentId: Number(appointmentId)
      }
    };
  }
};

/**
 * Get a specific health record by ID
 */
export const getHealthRecordById = async (recordId: string): Promise<HealthRecordResponse> => {
  try {
    return await api.get<HealthRecordResponse>(`/health-records/${recordId}`);
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to fetch health record',
      data: null
    };
  }
};

/**
 * Create a new health record
 */
export const createHealthRecord = async (data: HealthRecordCreateParams): Promise<HealthRecordResponse> => {
  try {
    return await api.post<HealthRecordResponse>('/health-records', data);
  } catch (error: any) {
    throw {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to create health record',
      data: null
    };
  }
};

/**
 * Update a health record
 */
export const updateHealthRecord = async (
  recordId: string,
  data: Partial<Omit<HealthRecordCreateParams, 'petId'>>
): Promise<HealthRecordResponse> => {
  try {
    return await api.put<HealthRecordResponse>(`/health-records/${recordId}`, data);
  } catch (error: any) {
    throw {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to update health record',
      data: null
    };
  }
};

/**
 * Delete a health record
 */
export const deleteHealthRecord = async (recordId: string): Promise<HealthRecordResponse> => {
  try {
    return await api.delete<HealthRecordResponse>(`/health-records/${recordId}`);
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to delete health record',
      data: null
    };
  }
};

/**
 * Get pet medical history (all health records with details)
 */
export const getPetMedicalHistory = async (petId: string): Promise<HealthRecordsResponse> => {
  try {
    return await api.get<HealthRecordsResponse>(`/health-records/pet/${petId}/medical-history`);
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to fetch pet medical history',
      data: {
        data: [],
        pagination: {
          totalCount: 0,
          page: 1,
          limit: 10,
          offset: 0,
          totalPages: 0
        }
      }
    };
  }
};
