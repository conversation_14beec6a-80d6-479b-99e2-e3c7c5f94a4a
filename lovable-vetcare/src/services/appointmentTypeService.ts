import { api } from './api';

export interface AppointmentTypeService {
  appointmentTypeServiceId: number;
  appointmentTypeId: number;
  serviceId: number;
  isDefault: boolean;
  displayOrder: number;
  isRequired: boolean;
  clinicId?: number;
  isActive: boolean;
  createdBy: number;
  modifiedBy?: number;
  createdAt: string;
  updatedAt: string;
}

export interface ServiceWithRelationData {
  serviceId: number;
  serviceName: string;
  description?: string;
  category: string;
  defaultPrice: number;
  currency: string;
  estimatedDuration: number;
  isDefault: boolean;
  displayOrder: number;
  isRequired: boolean;
  appointmentTypeServiceId: number;
}

export interface ServicesByType {
  [appointmentTypeId: string]: ServiceWithRelationData[];
}

export interface ChargeBreakdown {
  serviceId: number;
  serviceName: string;
  unitPrice: number;
  quantity: number;
  total: number;
}

export interface ChargeCalculation {
  totalCharges: number;
  breakdown: ChargeBreakdown[];
  currency: string;
}

// Get services for specific appointment types
export const getServicesByAppointmentTypes = async (
  appointmentTypeIds: number[],
  clinicId?: number
): Promise<{
  servicesByType: ServicesByType;
  totalServices: number;
  totalAppointmentTypes: number;
}> => {
  const params = new URLSearchParams({
    appointmentTypeIds: appointmentTypeIds.join(','),
    ...(clinicId && { clinicId: clinicId.toString() })
  });

  const response = await api.get(`/appointment-type-services/services?${params}`);
  return response.data.data;
};

// Get services by category
export const getServicesByCategory = async (
  category: string,
  clinicId?: number
): Promise<ServiceWithRelationData[]> => {
  const params = new URLSearchParams({
    category,
    ...(clinicId && { clinicId: clinicId.toString() })
  });

  const response = await api.get(`/appointment-type-services/services/category?${params}`);
  return response.data.data;
};

// Get appointment type with its services
export const getAppointmentTypeWithServices = async (
  appointmentTypeId: number,
  clinicId?: number
): Promise<{
  appointmentType: any;
  services: ServiceWithRelationData[];
  totalServices: number;
}> => {
  const params = clinicId ? `?clinicId=${clinicId}` : '';
  const response = await api.get(`/appointment-type-services/${appointmentTypeId}${params}`);
  return response.data.data;
};

// Add service to appointment type
export const addServiceToAppointmentType = async (data: {
  appointmentTypeId: number;
  serviceId: number;
  isDefault?: boolean;
  displayOrder?: number;
  isRequired?: boolean;
  clinicId?: number;
}): Promise<AppointmentTypeService> => {
  const response = await api.post('/appointment-type-services', data);
  return response.data.data;
};

// Update appointment type service relationship
export const updateAppointmentTypeService = async (
  appointmentTypeServiceId: number,
  data: {
    isDefault?: boolean;
    displayOrder?: number;
    isRequired?: boolean;
    isActive?: boolean;
  }
): Promise<AppointmentTypeService> => {
  const response = await api.put(`/appointment-type-services/${appointmentTypeServiceId}`, data);
  return response.data.data;
};

// Remove service from appointment type
export const removeServiceFromAppointmentType = async (
  appointmentTypeServiceId: number
): Promise<void> => {
  await api.delete(`/appointment-type-services/${appointmentTypeServiceId}`);
};

// Calculate appointment charges
export const calculateAppointmentCharges = async (data: {
  appointmentTypeIds: number[];
  selectedServices?: Array<{
    serviceId: number;
    quantity?: number;
    customPrice?: number;
  }>;
}): Promise<ChargeCalculation> => {
  const response = await api.post('/appointment-type-services/calculate-charges', data);
  return response.data.data;
};

// Get all services for dropdown selection
export const getAllServicesForSelection = async (
  clinicId?: number
): Promise<ServiceWithRelationData[]> => {
  const params = clinicId ? `?clinicId=${clinicId}` : '';
  const response = await api.get(`/services${params}`);
  return response.data.data;
};
