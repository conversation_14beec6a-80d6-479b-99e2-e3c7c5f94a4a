import { Pet, PaginatedResponse } from '@/store/types';
import {
  getPaginatedData,
  PaginationParams,
  createEntity,
  getEntityById,
  updateEntity,
  deleteEntity
} from './apiUtils';
import { useStore } from '@/store';

export interface PetSearchParams extends PaginationParams {
  petProfileId?: string;
  name?: string;
  breed?: string;
  clientId?: string | number; // Allow both for flexibility
  speciesId?: number; // Add speciesId for filtering
  breedId?: number; // Add breedId for filtering
}

export interface CreatePetDto {
  name: string; // Backend now uses 'name' field
  speciesId: number; // Changed to number for query efficiency
  breedId: number; // Changed to number for query efficiency
  color: string;
  lifeStatus: 'alive' | 'deceased';
  gender: 'male' | 'female';
  microchipId?: string;
  weight: number;
  dateOfBirth: Date;
  clientId: number; // Changed to number for consistency
  ownerId?: number; // Kept for backward compatibility, changed to number
  petStatus?: number;
}

// CRUD operations for pets
// Helper function to get clinic context
const getClinicId = () => {
  const state = useStore.getState();
  return state.clinic?.clinicId || state.employee?.primaryClinicId || state.employee?.clinicId;
};

export const getPets = async (params: PetSearchParams = {}): Promise<PaginatedResponse<Pet>> => {
  // Always include clinicId if available
  const clinicId = getClinicId();
  if (clinicId && !params.clinicId) {
    params.clinicId = clinicId;
  }

  return getPaginatedData<Pet>('pets', params);
};

export const getPetById = async (id: string) => {
  return getEntityById<Pet>('pets', id);
};

export const createPet = async (petData: CreatePetDto) => {
  return createEntity<Pet, CreatePetDto>('pets', petData);
};

export const updatePet = async (petId: string, petData: Partial<CreatePetDto>) => {
  return updateEntity<Pet, Partial<CreatePetDto>>('pets', petId, petData);
};

export const deletePet = async (petId: string) => {
  return deleteEntity('pets', petId);
};

/**
 * Get all pets for a specific client
 * @param clientId The ID of the pet owner
 * @param params Additional search parameters
 */
export const getPetsByOwner = async (clientId: string, params: PetSearchParams = {}) => {
  // Use the existing getPets function with the clientId parameter
  return getPets({ ...params, clientId });
};

/**
 * Legacy function - use getPetsByOwner instead
 * @deprecated Use getPetsByOwner with clientId instead
 */
export const getPetsByClient = async (clientId: string, params: PetSearchParams = {}) => {
  return getPetsByOwner(clientId, params);
};
