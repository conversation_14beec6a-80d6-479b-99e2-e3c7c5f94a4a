/**
 * WebSocket Service for Real-time Updates
 * 
 * This service provides real-time communication capabilities for the frontend,
 * enabling live updates for appointments, notifications, and system events.
 */

import { io, Socket } from 'socket.io-client';
import { useStore } from '@/store';

interface WebSocketEvents {
  // Connection events
  connected: (data: { message: string; userInfo: any }) => void;
  disconnect: () => void;
  
  // Appointment events
  appointment_notification: (data: { type: string; appointment: any; timestamp: Date }) => void;
  appointment_update: (data: { type: string; appointment: any; timestamp: Date }) => void;
  appointment_updated: (data: { appointment: any; updatedBy: string; timestamp: Date }) => void;
  
  // Notification events
  notification: (data: { type: string; message: string; timestamp: Date }) => void;
  system_notification: (data: { type: string; message: string; severity: string; timestamp: Date }) => void;
  
  // Inventory events
  inventory_alert: (data: { type: string; inventory: any; timestamp: Date }) => void;
  
  // General events
  pong: () => void;
}

class WebSocketService {
  private socket: Socket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000; // Start with 1 second
  private isConnecting = false;
  private eventListeners: Map<string, Function[]> = new Map();

  /**
   * Initialize WebSocket connection
   */
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.socket?.connected || this.isConnecting) {
        resolve();
        return;
      }

      this.isConnecting = true;
      const token = useStore.getState().token;

      if (!token) {
        this.isConnecting = false;
        reject(new Error('No authentication token available'));
        return;
      }

      const serverUrl = import.meta.env.VITE_API_BASE_URL?.replace('/api', '') || 'http://localhost:3000';

      this.socket = io(serverUrl, {
        auth: {
          token
        },
        transports: ['websocket', 'polling'],
        timeout: 10000,
        forceNew: true
      });

      this.setupEventHandlers();

      this.socket.on('connect', () => {
        console.log('WebSocket connected successfully');
        this.isConnecting = false;
        this.reconnectAttempts = 0;
        this.reconnectDelay = 1000;
        resolve();
      });

      this.socket.on('connect_error', (error) => {
        console.error('WebSocket connection error:', error);
        this.isConnecting = false;
        this.handleReconnection();
        reject(error);
      });
    });
  }

  /**
   * Disconnect WebSocket
   */
  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.isConnecting = false;
    this.reconnectAttempts = 0;
  }

  /**
   * Setup event handlers
   */
  private setupEventHandlers(): void {
    if (!this.socket) return;

    // Connection events
    this.socket.on('connected', (data) => {
      console.log('WebSocket connection confirmed:', data);
      this.emit('connected', data);
    });

    this.socket.on('disconnect', (reason) => {
      console.log('WebSocket disconnected:', reason);
      this.emit('disconnect');
      
      // Auto-reconnect unless it was a manual disconnect
      if (reason !== 'io client disconnect') {
        this.handleReconnection();
      }
    });

    // Appointment events
    this.socket.on('appointment_notification', (data) => {
      console.log('Appointment notification:', data);
      this.emit('appointment_notification', data);
      this.showNotification('New appointment created', 'info');
    });

    this.socket.on('appointment_update', (data) => {
      console.log('Appointment update:', data);
      this.emit('appointment_update', data);
      this.showNotification(`Appointment ${data.type}`, 'info');
    });

    this.socket.on('appointment_updated', (data) => {
      console.log('Appointment updated by:', data.updatedBy);
      this.emit('appointment_updated', data);
    });

    // Notification events
    this.socket.on('notification', (data) => {
      console.log('Notification received:', data);
      this.emit('notification', data);
      this.showNotification(data.message, data.type || 'info');
    });

    this.socket.on('system_notification', (data) => {
      console.log('System notification:', data);
      this.emit('system_notification', data);
      this.showNotification(data.message, data.severity || 'info');
    });

    // Inventory events
    this.socket.on('inventory_alert', (data) => {
      console.log('Inventory alert:', data);
      this.emit('inventory_alert', data);
      this.showNotification('Inventory alert: Low stock detected', 'warning');
    });

    // Ping/Pong for connection health
    this.socket.on('pong', () => {
      this.emit('pong');
    });
  }

  /**
   * Handle reconnection logic
   */
  private handleReconnection(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); // Exponential backoff

    console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts})`);

    setTimeout(() => {
      this.connect().catch((error) => {
        console.error('Reconnection failed:', error);
      });
    }, delay);
  }

  /**
   * Join clinic room
   */
  joinClinic(clinicId: number): void {
    if (this.socket?.connected) {
      this.socket.emit('join_clinic', { clinicId });
    }
  }

  /**
   * Leave clinic room
   */
  leaveClinic(clinicId: number): void {
    if (this.socket?.connected) {
      this.socket.emit('leave_clinic', { clinicId });
    }
  }

  /**
   * Send appointment update
   */
  sendAppointmentUpdate(appointmentData: any): void {
    if (this.socket?.connected) {
      this.socket.emit('appointment_update', appointmentData);
    }
  }

  /**
   * Mark notification as read
   */
  markNotificationRead(notificationId: string): void {
    if (this.socket?.connected) {
      this.socket.emit('notification_read', { notificationId });
    }
  }

  /**
   * Send ping to check connection
   */
  ping(): void {
    if (this.socket?.connected) {
      this.socket.emit('ping');
    }
  }

  /**
   * Add event listener
   */
  on<K extends keyof WebSocketEvents>(event: K, callback: WebSocketEvents[K]): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  /**
   * Remove event listener
   */
  off<K extends keyof WebSocketEvents>(event: K, callback: WebSocketEvents[K]): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * Emit event to listeners
   */
  private emit(event: string, data?: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in WebSocket event listener for ${event}:`, error);
        }
      });
    }
  }

  /**
   * Show notification to user
   */
  private showNotification(message: string, type: 'info' | 'success' | 'warning' | 'error' = 'info'): void {
    // This would integrate with your notification system (e.g., toast notifications)
    // For now, we'll just log it
    console.log(`[${type.toUpperCase()}] ${message}`);
    
    // You can integrate with a toast library here
    // Example: toast[type](message);
  }

  /**
   * Get connection status
   */
  get isConnected(): boolean {
    return this.socket?.connected || false;
  }

  /**
   * Get connection ID
   */
  get connectionId(): string | undefined {
    return this.socket?.id;
  }
}

// Create singleton instance
const webSocketService = new WebSocketService();

export default webSocketService;

// Export types for use in components
export type { WebSocketEvents };
