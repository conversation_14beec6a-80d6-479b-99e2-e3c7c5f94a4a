import { api } from './api';
import { Invoice, PaginationData } from '@/store/types';

export interface InvoiceResponse {
  success: boolean;
  status: number;
  message: string;
  data: Invoice | null;
}

export interface InvoicesResponse {
  success: boolean;
  status: number;
  message: string;
  data: {
    data: Invoice[];
    pagination: PaginationData;
  };
}

export interface InvoiceSearchParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: string;
  startDate?: string;
  endDate?: string;
  clientId?: string;
  petId?: string;
  [key: string]: any;
}

/**
 * Get all invoices with pagination and filtering
 */
export const getInvoices = async (params: InvoiceSearchParams = {}): Promise<InvoicesResponse> => {
  try {
    const queryParams = new URLSearchParams();

    queryParams.append('page', String(params.page || 1));
    queryParams.append('limit', String(params.limit || 10));

    Object.entries(params).forEach(([key, value]) => {
      if (value && !['page', 'limit'].includes(key)) {
        queryParams.append(key, String(value));
      }
    });

    return await api.get<InvoicesResponse>(`/invoices?${queryParams.toString()}`);
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to fetch invoices',
      data: {
        data: [],
        pagination: {
          totalCount: 0,
          page: 1,
          limit: 10,
          offset: 0,
          totalPages: 0
        }
      }
    };
  }
};

/**
 * Get invoices for a specific client
 */
export const getInvoicesByClient = async (clientId: string, params: InvoiceSearchParams = {}): Promise<InvoicesResponse> => {
  try {
    const queryParams = new URLSearchParams();

    queryParams.append('page', String(params.page || 1));
    queryParams.append('limit', String(params.limit || 10));

    Object.entries(params).forEach(([key, value]) => {
      if (value && !['page', 'limit', 'clientId'].includes(key)) {
        queryParams.append(key, String(value));
      }
    });

    return await api.get<InvoicesResponse>(`/invoices/client/${clientId}?${queryParams.toString()}`);
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to fetch client invoices',
      data: {
        data: [],
        pagination: {
          totalCount: 0,
          page: 1,
          limit: 10,
          offset: 0,
          totalPages: 0
        }
      }
    };
  }
};

/**
 * Get a single invoice by ID
 */
export const getInvoiceById = async (invoiceId: string): Promise<InvoiceResponse> => {
  try {
    return await api.get<InvoiceResponse>(`/invoices/${invoiceId}`);
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to fetch invoice',
      data: null
    };
  }
};

/**
 * Create a new invoice
 */
export const createInvoice = async (invoiceData: Partial<Invoice>): Promise<InvoiceResponse> => {
  try {
    return await api.post<InvoiceResponse>('/invoices', invoiceData);
  } catch (error: any) {
    throw {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to create invoice',
      data: null
    };
  }
};

/**
 * Update an existing invoice
 */
export const updateInvoice = async (
  invoiceId: string,
  invoiceData: Partial<Omit<Invoice, '_id'>>
): Promise<InvoiceResponse> => {
  try {
    return await api.put<InvoiceResponse>(`/invoices/${invoiceId}`, invoiceData);
  } catch (error: any) {
    throw {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to update invoice',
      data: null
    };
  }
};

/**
 * Delete an invoice
 */
export const deleteInvoice = async (invoiceId: string): Promise<InvoiceResponse> => {
  try {
    return await api.delete<InvoiceResponse>(`/invoices/${invoiceId}`);
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to delete invoice',
      data: null
    };
  }
};

/**
 * Generate invoice from appointment
 */
export const generateInvoiceFromAppointment = async (appointmentId: number): Promise<InvoiceResponse> => {
  try {
    const response = await api.post<InvoiceResponse>(`/invoices/generate/${appointmentId}`);
    if (response.success && response.data) {
      return response;
    }
    throw new Error('Failed to generate invoice');
  } catch (error: any) {
    throw {
      success: false,
      status: error.response?.status || error.status || 500,
      message: error.response?.data?.message || error.message || 'Failed to generate invoice',
      data: null
    };
  }
};

/**
 * Get invoice by appointment ID
 */
export const getInvoiceByAppointment = async (appointmentId: number): Promise<InvoiceResponse> => {
  try {
    const response = await api.get<InvoiceResponse>(`/invoices/appointment/${appointmentId}`);
    if (response.success && response.data) {
      return response;
    }
    throw new Error('Invoice not found');
  } catch (error: any) {
    if (error.response?.status === 404) {
      throw { status: 404, message: 'Invoice not found' };
    }
    throw {
      success: false,
      status: error.response?.status || error.status || 500,
      message: error.response?.data?.message || error.message || 'Failed to fetch invoice',
      data: null
    };
  }
};

/**
 * Apply discount to invoice
 */
export const applyDiscountToInvoice = async (
  invoiceId: number,
  discount: {
    discountType: 'percentage' | 'fixed';
    discountValue: number;
    reason: string;
  }
): Promise<InvoiceResponse> => {
  try {
    return await api.post<InvoiceResponse>(`/invoices/${invoiceId}/discount`, discount);
  } catch (error: any) {
    throw {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to apply discount',
      data: null
    };
  }
};

/**
 * Update invoice charges (categories, services, discounts)
 */
export const updateInvoiceCharges = async (
  invoiceId: number,
  chargesData: {
    appointmentCategories?: any[];
    services?: any[];
    medications?: any[];
    discounts?: any[];
  }
): Promise<InvoiceResponse> => {
  try {
    return await api.put<InvoiceResponse>(`/invoices/${invoiceId}/charges`, chargesData);
  } catch (error: any) {
    throw {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to update invoice charges',
      data: null
    };
  }
};

/**
 * Apply discount to specific category or service
 */
export const applyCategoryDiscount = async (
  invoiceId: number,
  discountData: {
    categoryId?: number;
    serviceId?: number;
    discountType: 'percentage' | 'fixed';
    discountValue: number;
    reason: string;
  }
): Promise<InvoiceResponse> => {
  try {
    return await api.post<InvoiceResponse>(`/invoices/${invoiceId}/category-discount`, discountData);
  } catch (error: any) {
    throw {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to apply category discount',
      data: null
    };
  }
};

/**
 * Update invoice status
 */
export const updateInvoiceStatus = async (
  invoiceId: number,
  status: string
): Promise<InvoiceResponse> => {
  try {
    return await api.patch<InvoiceResponse>(`/invoices/${invoiceId}/status`, { status });
  } catch (error: any) {
    throw {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to update invoice status',
      data: null
    };
  }
};
