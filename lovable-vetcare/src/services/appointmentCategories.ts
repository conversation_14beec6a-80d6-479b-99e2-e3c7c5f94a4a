import { api } from './api';

export interface AppointmentCategory {
  appointmentCategoryId: number;
  name: string;
  description?: string;
  icon?: string;
  color?: string;
  displayOrder: number;
  isActive: boolean;
  clinicId?: number;
  defaultStaffRoles: string[];
  estimatedDuration: number;
  requiresEquipment: boolean;
  requiresQualification: boolean;
  createdBy: number;
  modifiedBy?: number;
  createdAt: string;
  updatedAt: string;
}

export interface CategoryService {
  categoryServiceId: number;
  categoryServiceName: string;
  categoryServiceCode?: string;
  description?: string;
  appointmentCategoryId: number;
  defaultPrice: number;
  currency: string;
  estimatedDuration: number;
  isActive: boolean;
  isCustom: boolean;
  clinicId?: number;
  createdBy: number;
  createdAt: string;
  updatedAt: string;
}

export interface AppointmentCategoriesResponse {
  success: boolean;
  status: number;
  message: string;
  data: AppointmentCategory[];
}

export interface CategoryServicesResponse {
  success: boolean;
  status: number;
  message: string;
  data: CategoryService[];
}

export interface AppointmentCategoryParams {
  clinicId?: number;
  isActive?: boolean;
}

export interface CategoryServicesParams {
  clinicId?: number;
  isActive?: boolean;
}

/**
 * Get all appointment categories
 */
export const getAppointmentCategories = async (params: AppointmentCategoryParams = {}): Promise<AppointmentCategoriesResponse> => {
  try {
    const queryParams = new URLSearchParams();
    
    if (params.clinicId) {
      queryParams.append('clinicId', params.clinicId.toString());
    }
    
    if (params.isActive !== undefined) {
      queryParams.append('isActive', params.isActive.toString());
    }

    const url = `/appointments/categories${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    console.log("🌐 Fetching appointment categories from URL:", url);

    const response = await api.get<AppointmentCategoriesResponse>(url);
    console.log("📡 Raw appointment categories API response:", response);

    return response;
  } catch (error: any) {
    console.error("❌ Error fetching appointment categories:", error);
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to fetch appointment categories',
      data: []
    };
  }
};

/**
 * Get services for a specific appointment category
 */
export const getCategoryServices = async (
  appointmentCategoryId: number,
  params: CategoryServicesParams = {}
): Promise<CategoryServicesResponse> => {
  try {
    const queryParams = new URLSearchParams();
    
    if (params.clinicId) {
      queryParams.append('clinicId', params.clinicId.toString());
    }
    
    if (params.isActive !== undefined) {
      queryParams.append('isActive', params.isActive.toString());
    }

    const url = `/appointments/categories/${appointmentCategoryId}/services${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    console.log("🌐 Fetching category services from URL:", url);

    const response = await api.get<CategoryServicesResponse>(url);
    console.log("📡 Raw category services API response:", response);

    return response;
  } catch (error: any) {
    console.error("❌ Error fetching category services:", error);
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to fetch category services',
      data: []
    };
  }
};

/**
 * Get all services for multiple appointment categories
 */
export const getServicesForCategories = async (
  appointmentCategoryIds: number[],
  params: CategoryServicesParams = {}
): Promise<{ [categoryId: number]: CategoryService[] }> => {
  try {
    const servicesByCategory: { [categoryId: number]: CategoryService[] } = {};
    
    // Fetch services for each category in parallel
    const promises = appointmentCategoryIds.map(async (categoryId) => {
      const response = await getCategoryServices(categoryId, params);
      if (response.success) {
        servicesByCategory[categoryId] = response.data;
      }
      return { categoryId, services: response.data };
    });

    await Promise.all(promises);
    
    return servicesByCategory;
  } catch (error: any) {
    console.error("❌ Error fetching services for categories:", error);
    return {};
  }
};

/**
 * Get appointment category by ID
 */
export const getAppointmentCategoryById = async (appointmentCategoryId: number): Promise<{
  success: boolean;
  status: number;
  message: string;
  data: AppointmentCategory | null;
}> => {
  try {
    const response = await api.get<{
      success: boolean;
      status: number;
      message: string;
      data: AppointmentCategory;
    }>(`/appointments/categories/${appointmentCategoryId}`);

    return response;
  } catch (error: any) {
    console.error("❌ Error fetching appointment category:", error);
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to fetch appointment category',
      data: null
    };
  }
};

/**
 * Get staff suitable for appointment category
 */
export const getStaffForCategory = async (
  appointmentCategoryId: number,
  clinicId?: number
): Promise<{
  success: boolean;
  data: Array<{
    staffId: number;
    firstName: string;
    lastName: string;
    jobTitle: string;
    isAvailable: boolean;
  }>;
}> => {
  try {
    const queryParams = new URLSearchParams();
    if (clinicId) {
      queryParams.append('clinicId', clinicId.toString());
    }

    const response = await api.get(`/appointments/categories/${appointmentCategoryId}/staff${queryParams.toString() ? `?${queryParams.toString()}` : ''}`);
    return response;
  } catch (error: any) {
    console.error("❌ Error fetching staff for category:", error);
    return {
      success: false,
      data: []
    };
  }
};

export default {
  getAppointmentCategories,
  getCategoryServices,
  getServicesForCategories,
  getAppointmentCategoryById,
  getStaffForCategory
};
