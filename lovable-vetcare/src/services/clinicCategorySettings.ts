import { api } from './api';

export interface ClinicCategorySettings {
  clinicCategorySettingsId: number;
  clinicId: number;
  appointmentCategoryId: number;
  isEnabled: boolean;
  customName?: string;
  customDescription?: string;
  customCharge?: number;
  customDiscountPercentage: number;
  currency: string;
  customDuration?: number;
  displayOrder: number;
  allowedStaffRoles: string[];
  preferredStaff: Array<{
    staffId: number;
    priority: number;
  }>;
  clinicNotes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface AvailableCategory {
  appointmentCategoryId: number;
  name: string;
  description?: string;
  icon?: string;
  color?: string;
  defaultCharge: number;
  currency: string;
  estimatedDuration: number;
  requiresEquipment: boolean;
  requiresQualification: boolean;
  defaultStaffRoles: string[];
  isEnabledForClinic: boolean;
}

export interface ClinicCategoryResponse {
  success: boolean;
  status: number;
  message: string;
  data: ClinicCategorySettings[];
}

export interface AvailableCategoriesResponse {
  success: boolean;
  status: number;
  message: string;
  data: AvailableCategory[];
}

/**
 * Get all categories for a clinic (enabled and disabled)
 */
export const getClinicCategories = async (
  clinicId: number,
  enabled?: boolean
): Promise<ClinicCategoryResponse> => {
  try {
    const queryParams = new URLSearchParams();
    if (enabled !== undefined) {
      queryParams.append('enabled', enabled.toString());
    }

    const url = `/clinic-category-settings/clinics/${clinicId}/categories${
      queryParams.toString() ? `?${queryParams.toString()}` : ''
    }`;

    const response = await api.get<ClinicCategoryResponse>(url);
    return response;
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to fetch clinic categories',
      data: []
    };
  }
};

/**
 * Get all available categories that a clinic can enable
 */
export const getAvailableCategories = async (
  clinicId: number
): Promise<AvailableCategoriesResponse> => {
  try {
    const response = await api.get<AvailableCategoriesResponse>(
      `/clinic-category-settings/clinics/${clinicId}/available-categories`
    );
    return response;
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to fetch available categories',
      data: []
    };
  }
};

/**
 * Enable/disable a category for a clinic
 */
export const updateClinicCategoryStatus = async (
  clinicId: number,
  appointmentCategoryId: number,
  isEnabled: boolean
): Promise<{ success: boolean; message: string; data?: any }> => {
  try {
    const response = await api.put(
      `/clinic-category-settings/clinics/${clinicId}/categories/${appointmentCategoryId}/status`,
      { isEnabled }
    );
    return response;
  } catch (error: any) {
    return {
      success: false,
      message: error.message || 'Failed to update category status'
    };
  }
};

/**
 * Update clinic-specific category settings
 */
export const updateClinicCategorySettings = async (
  clinicId: number,
  appointmentCategoryId: number,
  settings: Partial<ClinicCategorySettings>
): Promise<{ success: boolean; message: string; data?: any }> => {
  try {
    const response = await api.put(
      `/clinic-category-settings/clinics/${clinicId}/categories/${appointmentCategoryId}/settings`,
      settings
    );
    return response;
  } catch (error: any) {
    return {
      success: false,
      message: error.message || 'Failed to update category settings'
    };
  }
};

/**
 * Bulk update clinic category selections
 */
export const bulkUpdateClinicCategories = async (
  clinicId: number,
  categoryIds: number[]
): Promise<{ success: boolean; message: string; data?: any }> => {
  try {
    const response = await api.put(
      `/clinic-category-settings/clinics/${clinicId}/categories/bulk-update`,
      { categoryIds }
    );
    return response;
  } catch (error: any) {
    return {
      success: false,
      message: error.message || 'Failed to update clinic categories'
    };
  }
};

export default {
  getClinicCategories,
  getAvailableCategories,
  updateClinicCategoryStatus,
  updateClinicCategorySettings,
  bulkUpdateClinicCategories
};
