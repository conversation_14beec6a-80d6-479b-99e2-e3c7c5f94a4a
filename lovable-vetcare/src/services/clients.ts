import { Client, PaginationData } from '@/store/types';
import { api } from './api';
import { useStore } from '@/store';

export interface ClientSearchParams {
    page?: number;
    limit?: number;
    offset?: number;
    name?: string;
    status?: number;
    clientId?: number;
    isWalkIn?: boolean;
    [key: string]: any;
}

export interface ClientResponse {
    success: boolean;
    status: number;
    message: string;
    data: Client | null;
}

export interface ClientsResponse {
    success: boolean;
    status: number;
    message: string;
    data: {
        data: Client[];
        pagination: PaginationData;
    };
}

// Helper function to get clinic context
const getClinicId = () => {
    const state = useStore.getState();
    return state.clinic?.clinicId || state.employee?.primaryClinicId || state.employee?.clinicId;
};

export const getClients = async (params: ClientSearchParams = {}): Promise<ClientsResponse> => {
    try {
        const queryParams = new URLSearchParams();

        queryParams.append('page', String(params.page || 1));
        queryParams.append('limit', String(params.limit || 10));

        // Always include clinicId if available
        const clinicId = getClinicId();
        if (clinicId && !params.clinicId) {
            params.clinicId = clinicId;
        }

        Object.entries(params).forEach(([key, value]) => {
            if (value && !['page', 'limit'].includes(key)) {
                queryParams.append(key, String(value));
            }
        });

        return await api.get<ClientsResponse>(`/clients?${queryParams.toString()}`);
    } catch (error: any) {
        return {
            success: false,
            status: error.status || 500,
            message: error.message || 'Failed to fetch clients',
            data: {
                data: [],
                pagination: {
                    totalCount: 0,
                    page: 1,
                    limit: 10,
                    offset: 0,
                    totalPages: 0
                }
            }
        };
    }
};

export const getClientById = async (clientId: string): Promise<ClientResponse> => {
    try {
        return await api.get<ClientResponse>(`/clients/${clientId}`);
    } catch (error: any) {
        return {
            success: false,
            status: error.status || 500,
            message: error.message || 'Failed to fetch client',
            data: null
        };
    }
};

export const createClient = async (clientData: Partial<Client>): Promise<ClientResponse> => {
    try {
        return await api.post<ClientResponse>('/clients', clientData);
    } catch (error: any) {
        return {
            success: false,
            status: error.status || 500,
            message: error.message || 'Failed to create client',
            data: null
        };
    }
};

export const updateClient = async (clientId: string, clientData: Partial<Client>): Promise<ClientResponse> => {
    try {
        return await api.put<ClientResponse>(`/clients/${clientId}`, clientData);
    } catch (error: any) {
        return {
            success: false,
            status: error.status || 500,
            message: error.message || 'Failed to update client',
            data: null
        };
    }
};

export const deleteClient = async (clientId: string): Promise<ClientResponse> => {
    try {
        return await api.delete<ClientResponse>(`/clients/${clientId}`);
    } catch (error: any) {
        return {
            success: false,
            status: error.status || 500,
            message: error.message || 'Failed to delete client',
            data: null
        };
    }
};

// Note: searchClients functionality is now handled by getClients with search parameters
