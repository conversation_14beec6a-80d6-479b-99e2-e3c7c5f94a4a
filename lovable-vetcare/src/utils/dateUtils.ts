import { format } from "date-fns";

/**
 * Utility functions for handling appointment dates consistently across the application
 */

export interface AppointmentWithDate {
  dateTime?: string | Date | null;
  appointmentDate?: string | Date | null;
}

/**
 * Get the appointment date value, preferring dateTime but falling back to appointmentDate
 */
export const getAppointmentDateValue = (appointment: AppointmentWithDate): string | Date | null => {
  return appointment.dateTime || appointment.appointmentDate || null;
};

/**
 * Check if an appointment has a valid date
 */
export const hasValidAppointmentDate = (appointment: AppointmentWithDate): boolean => {
  const dateValue = getAppointmentDateValue(appointment);
  if (!dateValue) return false;
  
  try {
    const date = new Date(dateValue);
    return !isNaN(date.getTime());
  } catch {
    return false;
  }
};

/**
 * Format appointment date safely
 */
export const formatAppointmentDate = (appointment: AppointmentWithDate, formatString: string = "MMMM d, yyyy"): string => {
  try {
    const dateValue = getAppointmentDateValue(appointment);
    if (!dateValue) return "Invalid Date";
    
    const date = new Date(dateValue);
    if (isNaN(date.getTime())) return "Invalid Date";
    
    return format(date, formatString);
  } catch (error) {
    console.error("Error formatting appointment date:", error, "Appointment:", appointment);
    return "Invalid Date";
  }
};

/**
 * Format appointment time safely
 */
export const formatAppointmentTime = (appointment: AppointmentWithDate, formatString: string = "h:mm a"): string => {
  try {
    const dateValue = getAppointmentDateValue(appointment);
    if (!dateValue) return "Invalid Time";
    
    const date = new Date(dateValue);
    if (isNaN(date.getTime())) return "Invalid Time";
    
    return format(date, formatString);
  } catch (error) {
    console.error("Error formatting appointment time:", error, "Appointment:", appointment);
    return "Invalid Time";
  }
};

/**
 * Format appointment date and time together
 */
export const formatAppointmentDateTime = (appointment: AppointmentWithDate, formatString: string = "PPp"): string => {
  try {
    const dateValue = getAppointmentDateValue(appointment);
    if (!dateValue) return "Invalid Date/Time";
    
    const date = new Date(dateValue);
    if (isNaN(date.getTime())) return "Invalid Date/Time";
    
    return format(date, formatString);
  } catch (error) {
    console.error("Error formatting appointment date/time:", error, "Appointment:", appointment);
    return "Invalid Date/Time";
  }
};

/**
 * Get appointment date as Date object safely
 */
export const getAppointmentDateObject = (appointment: AppointmentWithDate): Date | null => {
  try {
    const dateValue = getAppointmentDateValue(appointment);
    if (!dateValue) return null;
    
    const date = new Date(dateValue);
    if (isNaN(date.getTime())) return null;
    
    return date;
  } catch (error) {
    console.error("Error creating appointment date object:", error, "Appointment:", appointment);
    return null;
  }
};
