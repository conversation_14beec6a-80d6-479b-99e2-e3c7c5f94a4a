/**
 * Role-Based Permission System
 * 
 * This utility provides comprehensive role-based access control for UI elements
 * and functionality based on user roles and permissions.
 */

import { User, Staff } from '@/store/types';

// Define role hierarchy and permissions
export const ROLES = {
  SUPER_ADMIN: 'super_admin',
  ADMIN: 'admin', 
  SUPPORT: 'support',
  CLINIC_OWNER: 'clinic_owner',
  CLINIC_ADMIN: 'clinic_admin',
  VETERINARIAN: 'veterinarian',
  VETERINARIAN_ASSISTANT: 'veterinarian_assistant',
  VETERINARY_TECHNICIAN: 'veterinary_technician',
  RECEPTIONIST: 'receptionist',
  G<PERSON><PERSON><PERSON>: 'groomer',
  CLIENT: 'client'
} as const;

export const PERMISSIONS = {
  // System permissions
  ALL_ACCESS: 'all_access',
  SYSTEM_ADMIN: 'system_admin',
  
  // Clinic management
  CLINIC_MANAGEMENT: 'clinic_management',
  STAFF_MANAGEMENT: 'staff_management',
  
  // Patient and medical
  PATIENT_MANAGEMENT: 'patient_management',
  MEDICAL_RECORDS: 'medical_records',
  APPOINTMENT_MANAGEMENT: 'appointment_management',
  
  // Financial
  BILLING_MANAGEMENT: 'billing_management',
  INVENTORY_MANAGEMENT: 'inventory_management',
  
  // Reports and analytics
  REPORTS_VIEW: 'reports_view',
  ANALYTICS_VIEW: 'analytics_view'
} as const;

// Role-based permission mapping
export const ROLE_PERMISSIONS = {
  [ROLES.SUPER_ADMIN]: [PERMISSIONS.ALL_ACCESS],
  [ROLES.ADMIN]: [
    PERMISSIONS.SYSTEM_ADMIN,
    PERMISSIONS.CLINIC_MANAGEMENT,
    PERMISSIONS.STAFF_MANAGEMENT,
    PERMISSIONS.PATIENT_MANAGEMENT,
    PERMISSIONS.MEDICAL_RECORDS,
    PERMISSIONS.APPOINTMENT_MANAGEMENT,
    PERMISSIONS.BILLING_MANAGEMENT,
    PERMISSIONS.INVENTORY_MANAGEMENT,
    PERMISSIONS.REPORTS_VIEW,
    PERMISSIONS.ANALYTICS_VIEW
  ],
  [ROLES.CLINIC_OWNER]: [
    PERMISSIONS.CLINIC_MANAGEMENT,
    PERMISSIONS.STAFF_MANAGEMENT,
    PERMISSIONS.PATIENT_MANAGEMENT,
    PERMISSIONS.MEDICAL_RECORDS,
    PERMISSIONS.APPOINTMENT_MANAGEMENT,
    PERMISSIONS.BILLING_MANAGEMENT,
    PERMISSIONS.INVENTORY_MANAGEMENT,
    PERMISSIONS.REPORTS_VIEW,
    PERMISSIONS.ANALYTICS_VIEW
  ],
  [ROLES.CLINIC_ADMIN]: [
    PERMISSIONS.STAFF_MANAGEMENT,
    PERMISSIONS.PATIENT_MANAGEMENT,
    PERMISSIONS.MEDICAL_RECORDS,
    PERMISSIONS.APPOINTMENT_MANAGEMENT,
    PERMISSIONS.BILLING_MANAGEMENT,
    PERMISSIONS.INVENTORY_MANAGEMENT,
    PERMISSIONS.REPORTS_VIEW
  ],
  [ROLES.VETERINARIAN]: [
    PERMISSIONS.PATIENT_MANAGEMENT,
    PERMISSIONS.MEDICAL_RECORDS,
    PERMISSIONS.APPOINTMENT_MANAGEMENT
  ],
  [ROLES.VETERINARIAN_ASSISTANT]: [
    PERMISSIONS.PATIENT_MANAGEMENT,
    PERMISSIONS.MEDICAL_RECORDS
  ],
  [ROLES.VETERINARY_TECHNICIAN]: [
    PERMISSIONS.PATIENT_MANAGEMENT,
    PERMISSIONS.MEDICAL_RECORDS
  ],
  [ROLES.RECEPTIONIST]: [
    PERMISSIONS.PATIENT_MANAGEMENT,
    PERMISSIONS.APPOINTMENT_MANAGEMENT
  ],
  [ROLES.GROOMER]: [
    PERMISSIONS.PATIENT_MANAGEMENT
  ]
};

// User type definitions
export interface AuthUser {
  user?: User | null;
  employee?: Staff | null;
  userType?: 'user' | 'staff' | 'admin' | null;
  isAuthenticated: boolean;
}

/**
 * Check if user has a specific role
 */
export const hasRole = (authUser: AuthUser, role: string): boolean => {
  if (!authUser.isAuthenticated) return false;
  
  // Check for super admin by email
  if (authUser.user?.email === '<EMAIL>') return true;
  
  // Check staff role
  if (authUser.employee?.roleId) {
    const staffRole = typeof authUser.employee.roleId === 'object' 
      ? authUser.employee.roleId.roleName 
      : authUser.employee.roleId;
    return staffRole === role;
  }
  
  // Check user role
  if (authUser.user?.roleId) {
    const userRole = typeof authUser.user.roleId === 'object'
      ? authUser.user.roleId.roleName
      : authUser.user.roleId;
    return userRole === role;
  }
  
  return false;
};

/**
 * Check if user has any of the specified roles
 */
export const hasAnyRole = (authUser: AuthUser, roles: string[]): boolean => {
  return roles.some(role => hasRole(authUser, role));
};

/**
 * Check if user has a specific permission
 */
export const hasPermission = (authUser: AuthUser, permission: string): boolean => {
  if (!authUser.isAuthenticated) return false;
  
  // Super admin has all permissions
  if (authUser.user?.email === '<EMAIL>') return true;
  
  // Get user's role
  let userRole: string | null = null;
  
  if (authUser.employee?.roleId) {
    userRole = typeof authUser.employee.roleId === 'object' 
      ? authUser.employee.roleId.roleName 
      : authUser.employee.roleId;
  } else if (authUser.user?.roleId) {
    userRole = typeof authUser.user.roleId === 'object'
      ? authUser.user.roleId.roleName
      : authUser.user.roleId;
  }
  
  if (!userRole) return false;
  
  // Check if role has the permission
  const rolePermissions = ROLE_PERMISSIONS[userRole as keyof typeof ROLE_PERMISSIONS];
  return rolePermissions?.includes(permission as any) || false;
};

/**
 * Check if user has any of the specified permissions
 */
export const hasAnyPermission = (authUser: AuthUser, permissions: string[]): boolean => {
  return permissions.some(permission => hasPermission(authUser, permission));
};

/**
 * Check if user is system admin
 */
export const isSystemAdmin = (authUser: AuthUser): boolean => {
  return hasRole(authUser, ROLES.SUPER_ADMIN) || 
         hasRole(authUser, ROLES.ADMIN) ||
         authUser.user?.email === '<EMAIL>';
};

/**
 * Check if user is clinic owner
 */
export const isClinicOwner = (authUser: AuthUser): boolean => {
  return hasRole(authUser, ROLES.CLINIC_OWNER) || 
         authUser.employee?.isClinicOwner === true;
};

/**
 * Check if user is clinic admin (owner or admin)
 */
export const isClinicAdmin = (authUser: AuthUser): boolean => {
  return isClinicOwner(authUser) || hasRole(authUser, ROLES.CLINIC_ADMIN);
};

/**
 * Check if user can manage staff
 */
export const canManageStaff = (authUser: AuthUser): boolean => {
  return isSystemAdmin(authUser) || 
         isClinicAdmin(authUser) || 
         hasPermission(authUser, PERMISSIONS.STAFF_MANAGEMENT);
};

/**
 * Check if user can manage clinics
 */
export const canManageClinics = (authUser: AuthUser): boolean => {
  return isSystemAdmin(authUser) || 
         hasPermission(authUser, PERMISSIONS.CLINIC_MANAGEMENT);
};

/**
 * Check if user can access admin dashboard
 */
export const canAccessAdminDashboard = (authUser: AuthUser): boolean => {
  return isSystemAdmin(authUser);
};

/**
 * Check if user can access clinic dashboard
 */
export const canAccessClinicDashboard = (authUser: AuthUser): boolean => {
  return authUser.isAuthenticated && authUser.userType === 'staff';
};

/**
 * Get user's display role for UI
 */
export const getUserDisplayRole = (authUser: AuthUser): string => {
  if (!authUser.isAuthenticated) return 'Guest';

  if (authUser.user?.email === '<EMAIL>') return 'Super Admin';

  if (authUser.employee?.roleId) {
    const role = typeof authUser.employee.roleId === 'object'
      ? authUser.employee.roleId.roleName
      : authUser.employee.roleId;

    // Ensure role is a string before calling replace
    if (typeof role === 'string') {
      return role.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
    }
    return 'Staff';
  }

  if (authUser.user?.roleId) {
    const role = typeof authUser.user.roleId === 'object'
      ? authUser.user.roleId.roleName
      : authUser.user.roleId;

    // Ensure role is a string before calling replace
    if (typeof role === 'string') {
      return role.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
    }
    return 'User';
  }

  return 'User';
};

/**
 * Get available navigation items based on user permissions
 */
export const getAvailableNavigation = (authUser: AuthUser) => {
  const navigation = [];
  
  // Dashboard access
  if (canAccessAdminDashboard(authUser)) {
    navigation.push({ name: 'Admin Dashboard', path: '/admin' });
  }
  
  if (canAccessClinicDashboard(authUser)) {
    navigation.push({ name: 'Dashboard', path: '/dashboard' });
  }
  
  // Patient management
  if (hasPermission(authUser, PERMISSIONS.PATIENT_MANAGEMENT)) {
    navigation.push(
      { name: 'Clients', path: '/clients' },
      { name: 'Pets', path: '/pets' }
    );
  }
  
  // Appointment management
  if (hasPermission(authUser, PERMISSIONS.APPOINTMENT_MANAGEMENT)) {
    navigation.push({ name: 'Appointments', path: '/appointments' });
  }
  
  // Medical records
  if (hasPermission(authUser, PERMISSIONS.MEDICAL_RECORDS)) {
    navigation.push({ name: 'Medical Records', path: '/medical-records' });
  }
  
  // Staff management
  if (canManageStaff(authUser)) {
    navigation.push({ name: 'Staff', path: '/staff' });
  }
  
  // Clinic management
  if (canManageClinics(authUser)) {
    navigation.push({ name: 'Clinics', path: '/clinics' });
  }
  
  // Billing
  if (hasPermission(authUser, PERMISSIONS.BILLING_MANAGEMENT)) {
    navigation.push({ name: 'Billing', path: '/billing' });
  }
  
  // Reports
  if (hasPermission(authUser, PERMISSIONS.REPORTS_VIEW)) {
    navigation.push({ name: 'Reports', path: '/reports' });
  }
  
  return navigation;
};
