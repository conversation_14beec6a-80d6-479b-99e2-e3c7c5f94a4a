/**
 * Custom hooks that provide React Query-like interface for reference data
 * but use the centralized store to avoid multiple API calls
 */

import { useEffect, useState } from 'react';
import { useReferenceData } from '@/store';
import { Species, Breed, Staff, Role, AppointmentCategory, Client } from '@/store/types';

interface QueryResult<T> {
  data: T;
  isLoading: boolean;
  error: Error | null;
  refetch: () => Promise<void>;
}

/**
 * Hook for species data
 */
export const useSpeciesQuery = (): QueryResult<Species[]> => {
  const { species, isLoadingSpecies, getSpeciesData } = useReferenceData();
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setError(null);
        await getSpeciesData();
      } catch (err) {
        setError(err as Error);
      }
    };

    if (species.length === 0 && !isLoadingSpecies) {
      fetchData();
    }
  }, [species.length, isLoadingSpecies, getSpeciesData]);

  const refetch = async () => {
    try {
      setError(null);
      await getSpeciesData();
    } catch (err) {
      setError(err as Error);
    }
  };

  return {
    data: species,
    isLoading: isLoadingSpecies,
    error,
    refetch
  };
};

/**
 * Hook for breeds data
 */
export const useBreedsQuery = (): QueryResult<Breed[]> => {
  const { breeds, isLoadingBreeds, getBreedsData } = useReferenceData();
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setError(null);
        await getBreedsData();
      } catch (err) {
        setError(err as Error);
      }
    };

    if (breeds.length === 0 && !isLoadingBreeds) {
      fetchData();
    }
  }, [breeds.length, isLoadingBreeds, getBreedsData]);

  const refetch = async () => {
    try {
      setError(null);
      await getBreedsData();
    } catch (err) {
      setError(err as Error);
    }
  };

  return {
    data: breeds,
    isLoading: isLoadingBreeds,
    error,
    refetch
  };
};

/**
 * Hook for breeds by species
 */
export const useBreedsBySpeciesQuery = (speciesId: string | null): QueryResult<Breed[]> => {
  const { breedsBySpecies, getBreedsBySpeciesData } = useReferenceData();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      if (!speciesId) return;
      
      try {
        setIsLoading(true);
        setError(null);
        await getBreedsBySpeciesData(speciesId);
      } catch (err) {
        setError(err as Error);
      } finally {
        setIsLoading(false);
      }
    };

    if (speciesId && (!breedsBySpecies[speciesId] || breedsBySpecies[speciesId].length === 0)) {
      fetchData();
    }
  }, [speciesId, breedsBySpecies, getBreedsBySpeciesData]);

  const refetch = async () => {
    if (!speciesId) return;
    
    try {
      setIsLoading(true);
      setError(null);
      await getBreedsBySpeciesData(speciesId);
    } catch (err) {
      setError(err as Error);
    } finally {
      setIsLoading(false);
    }
  };

  return {
    data: speciesId ? (breedsBySpecies[speciesId] || []) : [],
    isLoading,
    error,
    refetch
  };
};

/**
 * Hook for staff data
 */
export const useStaffQuery = (): QueryResult<Staff[]> => {
  const { staff, isLoadingStaff, getStaffData } = useReferenceData();
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setError(null);
        await getStaffData();
      } catch (err) {
        setError(err as Error);
      }
    };

    if (staff.length === 0 && !isLoadingStaff) {
      fetchData();
    }
  }, [staff.length, isLoadingStaff, getStaffData]);

  const refetch = async () => {
    try {
      setError(null);
      await getStaffData();
    } catch (err) {
      setError(err as Error);
    }
  };

  return {
    data: staff,
    isLoading: isLoadingStaff,
    error,
    refetch
  };
};

/**
 * Hook for roles data
 */
export const useRolesQuery = (): QueryResult<Role[]> => {
  const { roles, isLoadingRoles, getRolesData } = useReferenceData();
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setError(null);
        await getRolesData();
      } catch (err) {
        setError(err as Error);
      }
    };

    if (roles.length === 0 && !isLoadingRoles) {
      fetchData();
    }
  }, [roles.length, isLoadingRoles, getRolesData]);

  const refetch = async () => {
    try {
      setError(null);
      await getRolesData();
    } catch (err) {
      setError(err as Error);
    }
  };

  return {
    data: roles,
    isLoading: isLoadingRoles,
    error,
    refetch
  };
};

/**
 * Hook for appointment categories data
 */
export const useAppointmentCategoriesQuery = (): QueryResult<AppointmentCategory[]> => {
  const { appointmentCategories, isLoadingAppointmentCategories, getAppointmentCategoriesData } = useReferenceData();
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setError(null);
        await getAppointmentCategoriesData();
      } catch (err) {
        setError(err as Error);
      }
    };

    if (appointmentCategories.length === 0 && !isLoadingAppointmentCategories) {
      fetchData();
    }
  }, [appointmentCategories.length, isLoadingAppointmentCategories, getAppointmentCategoriesData]);

  const refetch = async () => {
    try {
      setError(null);
      await getAppointmentCategoriesData();
    } catch (err) {
      setError(err as Error);
    }
  };

  return {
    data: appointmentCategories,
    isLoading: isLoadingAppointmentCategories,
    error,
    refetch
  };
};

/**
 * Hook for clients data
 */
export const useClientsQuery = (): QueryResult<Client[]> => {
  const { clients, isLoadingClients, getClientsData } = useReferenceData();
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setError(null);
        await getClientsData();
      } catch (err) {
        setError(err as Error);
      }
    };

    if (clients.length === 0 && !isLoadingClients) {
      fetchData();
    }
  }, [clients.length, isLoadingClients, getClientsData]);

  const refetch = async () => {
    try {
      setError(null);
      await getClientsData();
    } catch (err) {
      setError(err as Error);
    }
  };

  return {
    data: clients,
    isLoading: isLoadingClients,
    error,
    refetch
  };
};

/**
 * Hook to preload all reference data
 */
export const usePreloadReferenceData = () => {
  const { 
    getSpeciesData, 
    getStaffData, 
    getRolesData, 
    getAppointmentCategoriesData 
  } = useReferenceData();

  const preloadAll = async () => {
    try {
      await Promise.all([
        getSpeciesData(),
        getStaffData(),
        getRolesData(),
        getAppointmentCategoriesData()
      ]);
    } catch (error) {
      console.error('Failed to preload reference data:', error);
    }
  };

  return { preloadAll };
};
