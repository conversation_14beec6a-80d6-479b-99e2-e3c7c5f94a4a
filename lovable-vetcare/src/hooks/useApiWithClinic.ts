/**
 * Custom hook that automatically includes clinic_id in all API calls
 * This ensures data consistency and proper clinic-scoped operations
 */

import { useAuth } from '@/store';
import { api } from '@/services/api';

export interface ApiParams {
  [key: string]: any;
}

export interface ApiResponse<T = any> {
  success: boolean;
  status: number;
  message: string;
  data: T;
}

/**
 * Hook that provides API methods with automatic clinic_id inclusion
 */
export const useApiWithClinic = () => {
  const { staff, currentClinic } = useAuth();
  
  // Get clinic ID from multiple possible sources
  const getClinicId = (): number | undefined => {
    return currentClinic?.clinicId || staff?.currentClinicId || staff?.primaryClinicId || staff?.clinicId;
  };

  /**
   * Automatically adds clinic_id to API parameters
   */
  const withClinicId = (params: ApiParams = {}): ApiParams => {
    const clinicId = getClinicId();
    
    if (clinicId) {
      return {
        clinicId,
        ...params
      };
    }
    
    return params;
  };

  /**
   * GET request with automatic clinic_id inclusion
   */
  const get = async <T = any>(url: string, params: ApiParams = {}): Promise<ApiResponse<T>> => {
    return api.get<ApiResponse<T>>(url, { params: withClinicId(params) });
  };

  /**
   * POST request with automatic clinic_id inclusion
   */
  const post = async <T = any>(url: string, data: any = {}, params: ApiParams = {}): Promise<ApiResponse<T>> => {
    const bodyWithClinicId = typeof data === 'object' && data !== null ? withClinicId(data) : data;
    return api.post<ApiResponse<T>>(url, bodyWithClinicId, { params: withClinicId(params) });
  };

  /**
   * PUT request with automatic clinic_id inclusion
   */
  const put = async <T = any>(url: string, data: any = {}, params: ApiParams = {}): Promise<ApiResponse<T>> => {
    const bodyWithClinicId = typeof data === 'object' && data !== null ? withClinicId(data) : data;
    return api.put<ApiResponse<T>>(url, bodyWithClinicId, { params: withClinicId(params) });
  };

  /**
   * DELETE request with automatic clinic_id inclusion
   */
  const del = async <T = any>(url: string, params: ApiParams = {}): Promise<ApiResponse<T>> => {
    return api.delete<ApiResponse<T>>(url, { params: withClinicId(params) });
  };

  /**
   * Check if user has clinic context
   */
  const hasClinicContext = (): boolean => {
    return !!getClinicId();
  };

  /**
   * Get current clinic ID
   */
  const getCurrentClinicId = (): number | undefined => {
    return getClinicId();
  };

  return {
    get,
    post,
    put,
    delete: del,
    withClinicId,
    hasClinicContext,
    getCurrentClinicId,
    clinicId: getClinicId()
  };
};
