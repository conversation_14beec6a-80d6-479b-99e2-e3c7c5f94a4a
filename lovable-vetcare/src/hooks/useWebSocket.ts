/**
 * WebSocket React Hook
 * 
 * This hook provides easy integration with the WebSocket service
 * for React components, handling connection lifecycle and events.
 */

import { useEffect, useRef, useCallback, useState } from 'react';
import { useAuth } from '@/store';
import webSocketService, { WebSocketEvents } from '@/services/websocket';

interface UseWebSocketOptions {
  autoConnect?: boolean;
  clinicId?: number;
  onConnect?: () => void;
  onDisconnect?: () => void;
  onError?: (error: any) => void;
}

interface UseWebSocketReturn {
  isConnected: boolean;
  connectionId: string | undefined;
  connect: () => Promise<void>;
  disconnect: () => void;
  joinClinic: (clinicId: number) => void;
  leaveClinic: (clinicId: number) => void;
  sendAppointmentUpdate: (data: any) => void;
  markNotificationRead: (notificationId: string) => void;
  ping: () => void;
  on: <K extends keyof WebSocketEvents>(event: K, callback: WebSocketEvents[K]) => void;
  off: <K extends keyof WebSocketEvents>(event: K, callback: WebSocketEvents[K]) => void;
}

/**
 * Main WebSocket hook
 */
export const useWebSocket = (options: UseWebSocketOptions = {}): UseWebSocketReturn => {
  const {
    autoConnect = true,
    clinicId,
    onConnect,
    onDisconnect,
    onError
  } = options;

  const { isAuthenticated, token, clinic } = useAuth();
  const [isConnected, setIsConnected] = useState(false);
  const [connectionId, setConnectionId] = useState<string | undefined>();
  const eventListenersRef = useRef<Map<string, Function>>(new Map());

  // Connect to WebSocket
  const connect = useCallback(async () => {
    if (!isAuthenticated || !token) {
      console.warn('Cannot connect to WebSocket: Not authenticated');
      return;
    }

    try {
      await webSocketService.connect();
      setIsConnected(true);
      setConnectionId(webSocketService.connectionId);
      
      // Join clinic room if clinicId is provided
      const targetClinicId = clinicId || clinic?.clinicId;
      if (targetClinicId) {
        webSocketService.joinClinic(targetClinicId);
      }
      
      onConnect?.();
    } catch (error) {
      console.error('Failed to connect to WebSocket:', error);
      setIsConnected(false);
      setConnectionId(undefined);
      onError?.(error);
    }
  }, [isAuthenticated, token, clinicId, clinic?.clinicId, onConnect, onError]);

  // Disconnect from WebSocket
  const disconnect = useCallback(() => {
    webSocketService.disconnect();
    setIsConnected(false);
    setConnectionId(undefined);
    onDisconnect?.();
  }, [onDisconnect]);

  // Join clinic room
  const joinClinic = useCallback((clinicId: number) => {
    webSocketService.joinClinic(clinicId);
  }, []);

  // Leave clinic room
  const leaveClinic = useCallback((clinicId: number) => {
    webSocketService.leaveClinic(clinicId);
  }, []);

  // Send appointment update
  const sendAppointmentUpdate = useCallback((data: any) => {
    webSocketService.sendAppointmentUpdate(data);
  }, []);

  // Mark notification as read
  const markNotificationRead = useCallback((notificationId: string) => {
    webSocketService.markNotificationRead(notificationId);
  }, []);

  // Send ping
  const ping = useCallback(() => {
    webSocketService.ping();
  }, []);

  // Add event listener
  const on = useCallback(<K extends keyof WebSocketEvents>(
    event: K,
    callback: WebSocketEvents[K]
  ) => {
    webSocketService.on(event, callback);
    eventListenersRef.current.set(`${event}_${Date.now()}`, callback);
  }, []);

  // Remove event listener
  const off = useCallback(<K extends keyof WebSocketEvents>(
    event: K,
    callback: WebSocketEvents[K]
  ) => {
    webSocketService.off(event, callback);
    // Remove from our tracking
    for (const [key, listener] of eventListenersRef.current.entries()) {
      if (listener === callback) {
        eventListenersRef.current.delete(key);
        break;
      }
    }
  }, []);

  // Setup connection status listeners
  useEffect(() => {
    const handleConnect = () => {
      setIsConnected(true);
      setConnectionId(webSocketService.connectionId);
    };

    const handleDisconnect = () => {
      setIsConnected(false);
      setConnectionId(undefined);
    };

    webSocketService.on('connected', handleConnect);
    webSocketService.on('disconnect', handleDisconnect);

    return () => {
      webSocketService.off('connected', handleConnect);
      webSocketService.off('disconnect', handleDisconnect);
    };
  }, []);

  // Auto-connect when authenticated
  useEffect(() => {
    if (autoConnect && isAuthenticated && token && !isConnected) {
      connect();
    }
  }, [autoConnect, isAuthenticated, token, isConnected, connect]);

  // Disconnect when not authenticated
  useEffect(() => {
    if (!isAuthenticated && isConnected) {
      disconnect();
    }
  }, [isAuthenticated, isConnected, disconnect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Remove all event listeners added by this hook
      for (const [, callback] of eventListenersRef.current.entries()) {
        // Note: We can't easily remove specific listeners without the event name
        // This is a limitation of the current implementation
      }
      eventListenersRef.current.clear();
    };
  }, []);

  return {
    isConnected,
    connectionId,
    connect,
    disconnect,
    joinClinic,
    leaveClinic,
    sendAppointmentUpdate,
    markNotificationRead,
    ping,
    on,
    off
  };
};

/**
 * Hook for appointment-specific WebSocket events
 */
export const useAppointmentWebSocket = (clinicId?: number) => {
  const webSocket = useWebSocket({ clinicId });
  const [appointments, setAppointments] = useState<any[]>([]);
  const [notifications, setNotifications] = useState<any[]>([]);

  useEffect(() => {
    const handleAppointmentNotification = (data: any) => {
      setNotifications(prev => [data, ...prev.slice(0, 9)]); // Keep last 10 notifications
    };

    const handleAppointmentUpdate = (data: any) => {
      setAppointments(prev => {
        const index = prev.findIndex(apt => apt.appointmentId === data.appointment.appointmentId);
        if (index >= 0) {
          const updated = [...prev];
          updated[index] = { ...updated[index], ...data.appointment };
          return updated;
        }
        return prev;
      });
    };

    webSocket.on('appointment_notification', handleAppointmentNotification);
    webSocket.on('appointment_update', handleAppointmentUpdate);
    webSocket.on('appointment_updated', handleAppointmentUpdate);

    return () => {
      webSocket.off('appointment_notification', handleAppointmentNotification);
      webSocket.off('appointment_update', handleAppointmentUpdate);
      webSocket.off('appointment_updated', handleAppointmentUpdate);
    };
  }, [webSocket]);

  return {
    ...webSocket,
    appointments,
    notifications,
    clearNotifications: () => setNotifications([])
  };
};

/**
 * Hook for inventory-specific WebSocket events
 */
export const useInventoryWebSocket = (clinicId?: number) => {
  const webSocket = useWebSocket({ clinicId });
  const [inventoryAlerts, setInventoryAlerts] = useState<any[]>([]);

  useEffect(() => {
    const handleInventoryAlert = (data: any) => {
      setInventoryAlerts(prev => [data, ...prev.slice(0, 4)]); // Keep last 5 alerts
    };

    webSocket.on('inventory_alert', handleInventoryAlert);

    return () => {
      webSocket.off('inventory_alert', handleInventoryAlert);
    };
  }, [webSocket]);

  return {
    ...webSocket,
    inventoryAlerts,
    clearAlerts: () => setInventoryAlerts([])
  };
};

/**
 * Hook for system notifications
 */
export const useSystemNotifications = () => {
  const webSocket = useWebSocket();
  const [systemNotifications, setSystemNotifications] = useState<any[]>([]);

  useEffect(() => {
    const handleSystemNotification = (data: any) => {
      setSystemNotifications(prev => [data, ...prev.slice(0, 9)]); // Keep last 10 notifications
    };

    const handleNotification = (data: any) => {
      if (data.type === 'system') {
        setSystemNotifications(prev => [data, ...prev.slice(0, 9)]);
      }
    };

    webSocket.on('system_notification', handleSystemNotification);
    webSocket.on('notification', handleNotification);

    return () => {
      webSocket.off('system_notification', handleSystemNotification);
      webSocket.off('notification', handleNotification);
    };
  }, [webSocket]);

  return {
    ...webSocket,
    systemNotifications,
    clearSystemNotifications: () => setSystemNotifications([])
  };
};

export default useWebSocket;
