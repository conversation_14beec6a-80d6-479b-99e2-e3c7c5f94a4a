/**
 * React Hook for Permission-Based Access Control
 * 
 * This hook provides easy access to permission checking functions
 * and user role information throughout the React application.
 */

import { useMemo } from 'react';
import { useAuth } from '@/store';
import {
  hasRole,
  hasAnyRole,
  hasPermission,
  hasAnyPermission,
  isSystemAdmin,
  isClinicOwner,
  isClinicAdmin,
  canManageStaff,
  canManageClinics,
  canAccessAdminDashboard,
  canAccessClinicDashboard,
  getUserDisplayRole,
  getAvailableNavigation,
  ROLES,
  PERMISSIONS,
  type AuthUser
} from '@/utils/permissions';

/**
 * Hook for permission-based access control
 */
export const usePermissions = () => {
  const auth = useAuth();
  
  // Create auth user object for permission functions
  const authUser: AuthUser = useMemo(() => ({
    user: auth.user,
    employee: auth.employee,
    userType: auth.userType,
    isAuthenticated: auth.isAuthenticated
  }), [auth.user, auth.employee, auth.userType, auth.isAuthenticated]);

  // Memoize permission checks for performance
  const permissions = useMemo(() => ({
    // Role checks
    hasRole: (role: string) => hasRole(authUser, role),
    hasAnyRole: (roles: string[]) => hasAnyRole(authUser, roles),
    
    // Permission checks
    hasPermission: (permission: string) => hasPermission(authUser, permission),
    hasAnyPermission: (permissions: string[]) => hasAnyPermission(authUser, permissions),
    
    // Specific role checks
    isSystemAdmin: () => isSystemAdmin(authUser),
    isClinicOwner: () => isClinicOwner(authUser),
    isClinicAdmin: () => isClinicAdmin(authUser),
    
    // Capability checks
    canManageStaff: () => canManageStaff(authUser),
    canManageClinics: () => canManageClinics(authUser),
    canAccessAdminDashboard: () => canAccessAdminDashboard(authUser),
    canAccessClinicDashboard: () => canAccessClinicDashboard(authUser),
    
    // UI helpers
    getUserDisplayRole: () => getUserDisplayRole(authUser),
    getAvailableNavigation: () => getAvailableNavigation(authUser),
    
    // Quick permission checks for common actions
    canCreateAppointments: () => hasPermission(authUser, PERMISSIONS.APPOINTMENT_MANAGEMENT),
    canViewMedicalRecords: () => hasPermission(authUser, PERMISSIONS.MEDICAL_RECORDS),
    canManagePatients: () => hasPermission(authUser, PERMISSIONS.PATIENT_MANAGEMENT),
    canManageBilling: () => hasPermission(authUser, PERMISSIONS.BILLING_MANAGEMENT),
    canViewReports: () => hasPermission(authUser, PERMISSIONS.REPORTS_VIEW),
    canManageInventory: () => hasPermission(authUser, PERMISSIONS.INVENTORY_MANAGEMENT),
    
    // Role-specific checks
    isVeterinarian: () => hasRole(authUser, ROLES.VETERINARIAN),
    isReceptionist: () => hasRole(authUser, ROLES.RECEPTIONIST),
    isGroomer: () => hasRole(authUser, ROLES.GROOMER),
    isTechnician: () => hasRole(authUser, ROLES.VETERINARY_TECHNICIAN),
    isAssistant: () => hasRole(authUser, ROLES.VETERINARIAN_ASSISTANT),
    
    // Combined checks for UI elements
    canAccessStaffSection: () => canManageStaff(authUser) || isSystemAdmin(authUser),
    canAccessClinicSection: () => canManageClinics(authUser) || isSystemAdmin(authUser),
    canAccessFinancialSection: () => hasAnyPermission(authUser, [
      PERMISSIONS.BILLING_MANAGEMENT,
      PERMISSIONS.INVENTORY_MANAGEMENT
    ]),
    canAccessReportsSection: () => hasAnyPermission(authUser, [
      PERMISSIONS.REPORTS_VIEW,
      PERMISSIONS.ANALYTICS_VIEW
    ]),
    
    // Admin-specific checks
    canSwitchClinics: () => isSystemAdmin(authUser) || isClinicOwner(authUser),
    canViewAllClinics: () => isSystemAdmin(authUser),
    canCreateClinics: () => isSystemAdmin(authUser),
    canDeleteClinics: () => isSystemAdmin(authUser),
    
    // Clinic-specific checks
    canInviteStaff: () => isClinicAdmin(authUser) || canManageStaff(authUser),
    canRemoveStaff: () => isClinicAdmin(authUser) || canManageStaff(authUser),
    canUpdateClinicSettings: () => isClinicAdmin(authUser) || canManageClinics(authUser),
    canViewClinicAnalytics: () => isClinicAdmin(authUser) || hasPermission(authUser, PERMISSIONS.ANALYTICS_VIEW),
    
    // Medical-specific checks
    canPrescribeMedication: () => hasRole(authUser, ROLES.VETERINARIAN),
    canPerformSurgery: () => hasRole(authUser, ROLES.VETERINARIAN),
    canUpdateMedicalRecords: () => hasAnyRole(authUser, [
      ROLES.VETERINARIAN,
      ROLES.VETERINARY_TECHNICIAN
    ]),
    canViewLabResults: () => hasAnyRole(authUser, [
      ROLES.VETERINARIAN,
      ROLES.VETERINARY_TECHNICIAN,
      ROLES.VETERINARIAN_ASSISTANT
    ])
  }), [authUser]);

  return {
    ...permissions,
    // Export constants for use in components
    ROLES,
    PERMISSIONS,
    // Current user info
    currentUser: authUser,
    userRole: getUserDisplayRole(authUser),
    isAuthenticated: auth.isAuthenticated
  };
};

/**
 * Higher-order component for permission-based rendering
 */
export const withPermission = (
  Component: React.ComponentType<any>,
  requiredPermission: string | string[],
  fallback?: React.ComponentType<any> | null
) => {
  return (props: any) => {
    const { hasPermission, hasAnyPermission } = usePermissions();
    
    const hasAccess = Array.isArray(requiredPermission)
      ? hasAnyPermission(requiredPermission)
      : hasPermission(requiredPermission);
    
    if (!hasAccess) {
      return fallback ? React.createElement(fallback, props) : null;
    }
    
    return React.createElement(Component, props);
  };
};

/**
 * Higher-order component for role-based rendering
 */
export const withRole = (
  Component: React.ComponentType<any>,
  requiredRole: string | string[],
  fallback?: React.ComponentType<any> | null
) => {
  return (props: any) => {
    const { hasRole, hasAnyRole } = usePermissions();
    
    const hasAccess = Array.isArray(requiredRole)
      ? hasAnyRole(requiredRole)
      : hasRole(requiredRole);
    
    if (!hasAccess) {
      return fallback ? React.createElement(fallback, props) : null;
    }
    
    return React.createElement(Component, props);
  };
};

export default usePermissions;
