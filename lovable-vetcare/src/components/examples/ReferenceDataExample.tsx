/**
 * Example component demonstrating the new reference data system
 * This shows how to use centralized caching instead of multiple API calls
 */

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2, RefreshCw, Trash2 } from 'lucide-react';
import {
  useSpeciesQuery,
  useBreedsBySpeciesQuery,
  useStaffQuery,
  useRolesQuery,
  useAppointmentCategoriesQuery,
  usePreloadReferenceData
} from '@/hooks/useReferenceDataQuery';
import { useReferenceData } from '@/store';

const ReferenceDataExample: React.FC = () => {
  const [selectedSpecies, setSelectedSpecies] = useState<string>('');
  
  // Using the new reference data hooks
  const { data: species, isLoading: loadingSpecies, refetch: refetchSpecies } = useSpeciesQuery();
  const { data: breeds, isLoading: loadingBreeds } = useBreedsBySpeciesQuery(selectedSpecies || null);
  const { data: staff, isLoading: loadingStaff, refetch: refetchStaff } = useStaffQuery();
  const { data: roles, isLoading: loadingRoles } = useRolesQuery();
  const { data: appointmentCategories, isLoading: loadingCategories } = useAppointmentCategoriesQuery();
  
  // Direct store access for advanced operations
  const { clearCache, clearSpecificCache } = useReferenceData();
  const { preloadAll } = usePreloadReferenceData();

  const handleSpeciesChange = (value: string) => {
    setSelectedSpecies(value);
  };

  const handlePreloadAll = async () => {
    await preloadAll();
  };

  const handleClearAllCache = () => {
    clearCache();
    setSelectedSpecies(''); // Reset selection
  };

  const handleClearSpecificCache = (dataType: string) => {
    clearSpecificCache(dataType as any);
    if (dataType === 'species') {
      setSelectedSpecies(''); // Reset selection if species cache is cleared
    }
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Reference Data Management Example</h1>
        <div className="flex gap-2">
          <Button onClick={handlePreloadAll} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Preload All
          </Button>
          <Button onClick={handleClearAllCache} variant="outline">
            <Trash2 className="h-4 w-4 mr-2" />
            Clear All Cache
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Species Card */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Species</CardTitle>
            <div className="flex gap-2">
              <Button
                size="sm"
                variant="ghost"
                onClick={() => refetchSpecies()}
                disabled={loadingSpecies}
              >
                <RefreshCw className={`h-3 w-3 ${loadingSpecies ? 'animate-spin' : ''}`} />
              </Button>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => handleClearSpecificCache('species')}
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {loadingSpecies ? (
              <div className="flex items-center justify-center py-4">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span className="ml-2">Loading...</span>
              </div>
            ) : (
              <div className="space-y-2">
                <Badge variant="secondary">{species.length} species loaded</Badge>
                <Select value={selectedSpecies} onValueChange={handleSpeciesChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a species" />
                  </SelectTrigger>
                  <SelectContent>
                    {species.map((s) => (
                      <SelectItem key={s.speciesId} value={s.speciesId?.toString() || ''}>
                        {s.speciesName || s.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Breeds Card */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Breeds</CardTitle>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => handleClearSpecificCache('breeds')}
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </CardHeader>
          <CardContent>
            {!selectedSpecies ? (
              <p className="text-sm text-muted-foreground">Select a species to see breeds</p>
            ) : loadingBreeds ? (
              <div className="flex items-center justify-center py-4">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span className="ml-2">Loading breeds...</span>
              </div>
            ) : (
              <div className="space-y-2">
                <Badge variant="secondary">{breeds.length} breeds loaded</Badge>
                <div className="max-h-32 overflow-y-auto space-y-1">
                  {breeds.map((breed) => (
                    <div key={breed.breedId} className="text-sm p-1 bg-gray-50 rounded">
                      {breed.breedName || breed.name}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Staff Card */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Staff</CardTitle>
            <div className="flex gap-2">
              <Button
                size="sm"
                variant="ghost"
                onClick={() => refetchStaff()}
                disabled={loadingStaff}
              >
                <RefreshCw className={`h-3 w-3 ${loadingStaff ? 'animate-spin' : ''}`} />
              </Button>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => handleClearSpecificCache('staff')}
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {loadingStaff ? (
              <div className="flex items-center justify-center py-4">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span className="ml-2">Loading...</span>
              </div>
            ) : (
              <div className="space-y-2">
                <Badge variant="secondary">{staff.length} staff members</Badge>
                <div className="max-h-32 overflow-y-auto space-y-1">
                  {staff.slice(0, 5).map((member) => (
                    <div key={member.staffId} className="text-sm p-1 bg-gray-50 rounded">
                      {member.firstName} {member.lastName}
                    </div>
                  ))}
                  {staff.length > 5 && (
                    <div className="text-xs text-muted-foreground">
                      +{staff.length - 5} more...
                    </div>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Roles Card */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Roles</CardTitle>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => handleClearSpecificCache('roles')}
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </CardHeader>
          <CardContent>
            {loadingRoles ? (
              <div className="flex items-center justify-center py-4">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span className="ml-2">Loading...</span>
              </div>
            ) : (
              <div className="space-y-2">
                <Badge variant="secondary">{roles.length} roles</Badge>
                <div className="space-y-1">
                  {roles.map((role) => (
                    <div key={role.roleId} className="text-sm p-1 bg-gray-50 rounded">
                      {role.roleName}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Appointment Categories Card */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Appointment Categories</CardTitle>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => handleClearSpecificCache('appointmentCategories')}
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </CardHeader>
          <CardContent>
            {loadingCategories ? (
              <div className="flex items-center justify-center py-4">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span className="ml-2">Loading...</span>
              </div>
            ) : (
              <div className="space-y-2">
                <Badge variant="secondary">{appointmentCategories.length} categories</Badge>
                <div className="max-h-32 overflow-y-auto space-y-1">
                  {appointmentCategories.map((category) => (
                    <div key={category.appointmentCategoryId} className="text-sm p-1 bg-gray-50 rounded">
                      {category.name}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Performance Info Card */}
        <Card>
          <CardHeader>
            <CardTitle className="text-sm font-medium">Performance Benefits</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>Cached data loads instantly</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span>Reduced API calls</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                <span>Consistent data across components</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                <span>5-minute TTL with auto-refresh</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Usage Instructions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h4 className="font-medium mb-2">Before (Multiple API calls):</h4>
            <pre className="bg-gray-100 p-3 rounded text-sm overflow-x-auto">
{`const { data: speciesData } = useQuery({
  queryKey: ['species'],
  queryFn: () => getSpecies()
});
const species = speciesData?.data?.data || [];`}
            </pre>
          </div>
          <div>
            <h4 className="font-medium mb-2">After (Centralized cache):</h4>
            <pre className="bg-gray-100 p-3 rounded text-sm overflow-x-auto">
{`const { data: species, isLoading } = useSpeciesQuery();
// species is ready to use, no data extraction needed`}
            </pre>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ReferenceDataExample;
