
import { useState } from "react";
import { Outlet } from "react-router-dom";
import { Navbar } from "./Navbar";
import { EnhancedSidebar } from "./EnhancedSidebar";
import { useIsMobile } from "@/hooks/use-mobile";

const Layout = () => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const isMobile = useIsMobile();

  const toggleSidebar = () => setIsSidebarOpen(!isSidebarOpen);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
      <Navbar onMenuClick={toggleSidebar} />
      <div className="flex">
        <EnhancedSidebar isOpen={isSidebarOpen} onClose={() => setIsSidebarOpen(false)} />
        <main
          className={`flex-1 mt-16 transition-all duration-300 ${
            isSidebarOpen && !isMobile ? "ml-[280px]" : isMobile ? "ml-0" : "ml-16"
          }`}
        >
          <Outlet />
        </main>
      </div>
    </div>
  );
};

export default Layout;
