
import { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { Menu, X, Bell, Settings, Moon, Sun, Search, User, LogOut, Sparkles, Building2, Shield } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { useTheme } from "next-themes";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/store";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";
import { usePermissions } from "@/hooks/usePermissions";
import { ClinicSelectorModal } from "@/components/clinic/ClinicSelectorModal";

interface NavbarProps {
  onMenuClick: () => void;
}

export const Navbar = ({ onMenuClick }: NavbarProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchOpen, setSearchOpen] = useState(false);
  const [clinicModalOpen, setClinicModalOpen] = useState(false);
  const [notifications] = useState(3); // Mock notification count
  const { logout, user, employee: staff, clinic: currentClinic } = useAuth();
  const { theme, setTheme } = useTheme();
  const {
    isSystemAdmin,
    isClinicAdmin,
    canSwitchClinics,
    getUserDisplayRole,
    canAccessAdminDashboard
  } = usePermissions();

  const handleMenuClick = () => {
    setIsOpen(!isOpen);
    onMenuClick();
  };

  const toggleTheme = () => {
    setTheme(theme === "dark" ? "light" : "dark");
  };

  return (
    <motion.nav
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ type: "spring", stiffness: 300, damping: 30 }}
      className="bg-white/90 dark:bg-gray-900/90 backdrop-blur-xl border-b border-gray-200 dark:border-gray-700 px-4 py-3 fixed w-full top-0 z-50 shadow-sm"
    >
      <div className="flex items-center justify-between">
        {/* Left Section */}
        <div className="flex items-center space-x-4">
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={handleMenuClick}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-xl transition-colors duration-200"
          >
            <motion.div
              animate={{ rotate: isOpen ? 180 : 0 }}
              transition={{ duration: 0.3 }}
            >
              {isOpen ? <X size={20} /> : <Menu size={20} />}
            </motion.div>
          </motion.button>

          <Link to="/" className="flex items-center space-x-3">
            <motion.div
              whileHover={{ rotate: 360 }}
              transition={{ duration: 0.5 }}
              className="p-2 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600"
            >
              <Sparkles className="h-5 w-5 text-white" />
            </motion.div>
            <div className="hidden sm:block">
              <span className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                VetCare Pro
              </span>
              <div className="text-xs text-gray-500 -mt-1">
                Practice Management
              </div>
            </div>
          </Link>
        </div>

        {/* Center Section - Search */}
        <div className="hidden md:flex flex-1 max-w-md mx-8">
          <div className="relative w-full">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search patients, appointments, records..."
              className="pl-10 bg-gray-50 dark:bg-gray-800 border-0 focus:ring-2 focus:ring-blue-500 rounded-xl"
            />
          </div>
        </div>

        {/* Right Section */}
        <div className="flex items-center space-x-3">
          {/* Mobile Search */}
          <div className="md:hidden">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setSearchOpen(!searchOpen)}
              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-xl transition-colors duration-200"
            >
              <Search className="h-5 w-5" />
            </motion.button>
          </div>

          {/* Admin Dashboard Access */}
          {canAccessAdminDashboard() && (
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button
                variant="ghost"
                size="sm"
                asChild
                className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-xl"
              >
                <Link to="/admin">
                  <Shield className="h-5 w-5 text-red-500" />
                </Link>
              </Button>
            </motion.div>
          )}

          {/* Notifications */}
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="relative"
          >
            <Button
              variant="ghost"
              size="sm"
              className="relative p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-xl"
            >
              <Bell className="h-5 w-5" />
              {notifications > 0 && (
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  className="absolute -top-1 -right-1"
                >
                  <Badge className="h-5 w-5 p-0 text-xs bg-red-500 hover:bg-red-600">
                    {notifications}
                  </Badge>
                </motion.div>
              )}
            </Button>
          </motion.div>

          {/* Theme Toggle */}
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleTheme}
              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-xl"
            >
              <motion.div
                animate={{ rotate: theme === "dark" ? 180 : 0 }}
                transition={{ duration: 0.3 }}
              >
                {theme === "dark" ? <Sun className="h-5 w-5" /> : <Moon className="h-5 w-5" />}
              </motion.div>
            </Button>
          </motion.div>

          {/* User Profile Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="flex items-center space-x-3 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-xl p-2 transition-colors duration-200"
              >
                <Avatar className="w-8 h-8 ring-2 ring-blue-500/20">
                  <AvatarImage src={staff?.imageUrl || "https://github.com/shadcn.png"} />
                  <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white text-sm font-medium">
                    {user?.firstName?.charAt(0)}{user?.lastName?.charAt(0)}
                  </AvatarFallback>
                </Avatar>
                <div className="hidden sm:block text-left">
                  <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {user?.firstName} {user?.lastName}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {getUserDisplayRole()}
                  </div>
                </div>
              </motion.button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-64 p-2">
              <DropdownMenuLabel className="p-3">
                <div className="flex items-center space-x-3">
                  <Avatar className="w-12 h-12">
                    <AvatarImage src={staff?.imageUrl || "https://github.com/shadcn.png"} />
                    <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white">
                      {user?.firstName?.charAt(0)}{user?.lastName?.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                      {user?.firstName} {user?.lastName}
                    </p>
                    <p className="text-xs text-gray-500 truncate">
                      {user?.email}
                    </p>
                    {currentClinic && (
                      <p className="text-xs text-blue-600 dark:text-blue-400 truncate mt-1">
                        📍 {currentClinic.clinicName}
                      </p>
                    )}
                  </div>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />

              {/* Clinic Switcher */}
              {canSwitchClinics() && (
                <>
                  <DropdownMenuItem
                    onClick={() => setClinicModalOpen(true)}
                    className="flex items-center space-x-2 w-full p-2 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-lg cursor-pointer"
                  >
                    <Building2 className="h-4 w-4" />
                    <span>Switch Clinic</span>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                </>
              )}

              <DropdownMenuItem asChild>
                <Link to="/profile" className="flex items-center space-x-2 w-full p-2 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-lg">
                  <User className="h-4 w-4" />
                  <span>View Profile</span>
                </Link>
              </DropdownMenuItem>

              <DropdownMenuItem asChild>
                <Link to="/settings/user" className="flex items-center space-x-2 w-full p-2 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-lg">
                  <Settings className="h-4 w-4" />
                  <span>Settings</span>
                </Link>
              </DropdownMenuItem>

              {/* Theme Toggle */}
              <DropdownMenuItem
                onClick={toggleTheme}
                className="flex items-center space-x-2 w-full p-2 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-lg cursor-pointer"
              >
                {theme === "dark" ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
                <span>{theme === "dark" ? "Light Mode" : "Dark Mode"}</span>
              </DropdownMenuItem>

              {/* Admin Links */}
              {isClinicAdmin() && (
                <DropdownMenuItem asChild>
                  <Link to="/admin/clinics" className="flex items-center space-x-2 w-full p-2 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-lg">
                    <Sparkles className="h-4 w-4" />
                    <span>Manage Clinics</span>
                  </Link>
                </DropdownMenuItem>
              )}

              {isSystemAdmin() && (
                <DropdownMenuItem asChild>
                  <Link to="/admin" className="flex items-center space-x-2 w-full p-2 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-lg">
                    <Shield className="h-4 w-4 text-red-500" />
                    <span>Admin Dashboard</span>
                  </Link>
                </DropdownMenuItem>
              )}

              <DropdownMenuSeparator />

              <DropdownMenuItem
                onClick={() => logout()}
                className="flex items-center space-x-2 text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20 cursor-pointer p-2 rounded-lg"
              >
                <LogOut className="h-4 w-4" />
                <span>Log out</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Mobile Search Overlay */}
      <AnimatePresence>
        {searchOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            className="md:hidden border-t border-gray-200 dark:border-gray-700 p-4"
          >
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search patients, appointments, records..."
                className="pl-10 bg-gray-50 dark:bg-gray-800 border-0 focus:ring-2 focus:ring-blue-500 rounded-xl"
                autoFocus
              />
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Clinic Selector Modal */}
      <ClinicSelectorModal
        open={clinicModalOpen}
        onOpenChange={setClinicModalOpen}
      />
    </motion.nav>
  );
};
