import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from "@/components/ui/dialog";
import { 
  Receipt, 
  FileText, 
  DollarSign, 
  CreditCard, 
  Printer, 
  Download, 
  Edit,
  Trash2,
  Plus,
  Calculator
} from "lucide-react";
import { useAuth } from "@/store";
import { api } from "@/services/api";
import { toast } from "@/components/ui/use-toast";

interface InvoiceItem {
  categoryId: number;
  categoryName: string;
  services: {
    serviceId: number;
    serviceName: string;
    quantity: number;
    unitPrice: number;
    discount: number;
    total: number;
  }[];
  categoryTotal: number;
  categoryDiscount: number;
}

interface Invoice {
  invoiceId?: number;
  appointmentId: number;
  clientId: number;
  petId: number;
  clinicId: number;
  invoiceNumber?: string;
  items: InvoiceItem[];
  subtotal: number;
  totalDiscount: number;
  taxAmount: number;
  grandTotal: number;
  status: 'draft' | 'sent' | 'paid' | 'overdue';
  paymentMethod?: string;
  notes?: string;
  dueDate: string;
}

interface InvoiceGeneratorProps {
  appointmentId?: number;
  existingInvoice?: Invoice;
  onInvoiceGenerated?: (invoice: Invoice) => void;
}

export function InvoiceGenerator({ 
  appointmentId, 
  existingInvoice, 
  onInvoiceGenerated 
}: InvoiceGeneratorProps) {
  const { currentClinic } = useAuth();
  const [loading, setLoading] = useState(false);
  const [appointment, setAppointment] = useState<any>(null);
  const [invoice, setInvoice] = useState<Invoice | null>(existingInvoice || null);
  const [editMode, setEditMode] = useState(!existingInvoice);
  const [previewMode, setPreviewMode] = useState(false);

  useEffect(() => {
    if (appointmentId && !existingInvoice) {
      fetchAppointmentDetails();
    }
  }, [appointmentId]);

  const fetchAppointmentDetails = async () => {
    if (!appointmentId) return;
    
    setLoading(true);
    try {
      const response = await api.get(`/appointments/${appointmentId}`);
      if (response.success) {
        setAppointment(response.data.appointment);
        generateInvoiceFromAppointment(response.data.appointment);
      }
    } catch (error) {
      console.error('Error fetching appointment:', error);
      toast({
        title: "Error",
        description: "Failed to fetch appointment details",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const generateInvoiceFromAppointment = (appointmentData: any) => {
    const items: InvoiceItem[] = appointmentData.appointmentCategories.map((category: any) => ({
      categoryId: category.categoryId,
      categoryName: category.categoryName,
      services: category.services.map((service: any) => ({
        serviceId: service.serviceId,
        serviceName: service.serviceName,
        quantity: service.quantity || 1,
        unitPrice: service.price || 0,
        discount: service.discount || 0,
        total: (service.price || 0) * (service.quantity || 1) * (1 - (service.discount || 0) / 100)
      })),
      categoryTotal: category.services.reduce((sum: number, service: any) => 
        sum + (service.price || 0) * (service.quantity || 1), 0),
      categoryDiscount: category.discount || 0
    }));

    const subtotal = items.reduce((sum, item) => sum + item.categoryTotal, 0);
    const totalDiscount = items.reduce((sum, item) => 
      sum + (item.categoryTotal * item.categoryDiscount / 100), 0);
    const taxAmount = (subtotal - totalDiscount) * 0.16; // 16% VAT
    const grandTotal = subtotal - totalDiscount + taxAmount;

    const newInvoice: Invoice = {
      appointmentId: appointmentData.appointmentId,
      clientId: appointmentData.clientId,
      petId: appointmentData.petId,
      clinicId: currentClinic?.clinicId || 0,
      items,
      subtotal,
      totalDiscount,
      taxAmount,
      grandTotal,
      status: 'draft',
      dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days from now
      notes: ''
    };

    setInvoice(newInvoice);
  };

  const updateItemPrice = (categoryIndex: number, serviceIndex: number, field: string, value: number) => {
    if (!invoice) return;

    const updatedInvoice = { ...invoice };
    const item = updatedInvoice.items[categoryIndex];
    const service = item.services[serviceIndex];

    if (field === 'unitPrice') {
      service.unitPrice = value;
    } else if (field === 'quantity') {
      service.quantity = value;
    } else if (field === 'discount') {
      service.discount = value;
    }

    // Recalculate service total
    service.total = service.unitPrice * service.quantity * (1 - service.discount / 100);

    // Recalculate category total
    item.categoryTotal = item.services.reduce((sum, s) => sum + s.unitPrice * s.quantity, 0);

    // Recalculate invoice totals
    updatedInvoice.subtotal = updatedInvoice.items.reduce((sum, item) => sum + item.categoryTotal, 0);
    updatedInvoice.totalDiscount = updatedInvoice.items.reduce((sum, item) => 
      sum + (item.categoryTotal * item.categoryDiscount / 100) + 
      item.services.reduce((serviceSum, service) => 
        serviceSum + (service.unitPrice * service.quantity * service.discount / 100), 0), 0);
    updatedInvoice.taxAmount = (updatedInvoice.subtotal - updatedInvoice.totalDiscount) * 0.16;
    updatedInvoice.grandTotal = updatedInvoice.subtotal - updatedInvoice.totalDiscount + updatedInvoice.taxAmount;

    setInvoice(updatedInvoice);
  };

  const saveInvoice = async () => {
    if (!invoice) return;

    setLoading(true);
    try {
      const endpoint = invoice.invoiceId ? `/invoices/${invoice.invoiceId}` : '/invoices';
      const method = invoice.invoiceId ? 'put' : 'post';
      
      const response = await api[method](endpoint, invoice);
      
      if (response.success) {
        const savedInvoice = response.data.invoice;
        setInvoice(savedInvoice);
        setEditMode(false);
        
        toast({
          title: "Success",
          description: `Invoice ${invoice.invoiceId ? 'updated' : 'created'} successfully`,
        });
        
        if (onInvoiceGenerated) {
          onInvoiceGenerated(savedInvoice);
        }
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to save invoice",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const generateReceipt = async () => {
    if (!invoice?.invoiceId) return;

    setLoading(true);
    try {
      const response = await api.post(`/invoices/${invoice.invoiceId}/receipt`);
      
      if (response.success) {
        toast({
          title: "Success",
          description: "Receipt generated successfully",
        });
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to generate receipt",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const printInvoice = () => {
    window.print();
  };

  const downloadInvoice = async () => {
    if (!invoice?.invoiceId) return;

    try {
      const response = await api.get(`/invoices/${invoice.invoiceId}/pdf`, {
        responseType: 'blob'
      });
      
      const blob = new Blob([response.data], { type: 'application/pdf' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `invoice-${invoice.invoiceNumber || invoice.invoiceId}.pdf`;
      link.click();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to download invoice",
        variant: "destructive"
      });
    }
  };

  if (!invoice) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <p className="text-muted-foreground">No invoice data available</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Invoice Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Receipt className="h-5 w-5" />
              {invoice.invoiceNumber ? `Invoice ${invoice.invoiceNumber}` : 'New Invoice'}
              <Badge variant={
                invoice.status === 'paid' ? 'default' :
                invoice.status === 'overdue' ? 'destructive' :
                invoice.status === 'sent' ? 'secondary' : 'outline'
              }>
                {invoice.status}
              </Badge>
            </CardTitle>
            <div className="flex gap-2">
              {!editMode && (
                <>
                  <Button variant="outline" size="sm" onClick={() => setEditMode(true)}>
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </Button>
                  <Button variant="outline" size="sm" onClick={printInvoice}>
                    <Printer className="h-4 w-4 mr-2" />
                    Print
                  </Button>
                  <Button variant="outline" size="sm" onClick={downloadInvoice}>
                    <Download className="h-4 w-4 mr-2" />
                    Download
                  </Button>
                  {invoice.status === 'paid' && (
                    <Button variant="outline" size="sm" onClick={generateReceipt}>
                      <FileText className="h-4 w-4 mr-2" />
                      Receipt
                    </Button>
                  )}
                </>
              )}
              {editMode && (
                <>
                  <Button variant="outline" onClick={() => setEditMode(false)}>
                    Cancel
                  </Button>
                  <Button onClick={saveInvoice} disabled={loading}>
                    {loading ? 'Saving...' : 'Save Invoice'}
                  </Button>
                </>
              )}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Invoice Details */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Client & Pet Info */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Client & Pet Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {appointment && (
              <>
                <div>
                  <Label className="text-sm font-medium">Client</Label>
                  <p className="text-sm">{appointment.clientName}</p>
                  <p className="text-sm text-muted-foreground">{appointment.clientPhone}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Pet</Label>
                  <p className="text-sm">{appointment.petName}</p>
                  <p className="text-sm text-muted-foreground">
                    {appointment.petSpecies} • {appointment.petBreed}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Appointment Date</Label>
                  <p className="text-sm">{new Date(appointment.appointmentDate).toLocaleDateString()}</p>
                </div>
              </>
            )}
          </CardContent>
        </Card>

        {/* Invoice Summary */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Invoice Summary</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <Label>Subtotal</Label>
                <p className="font-medium">KES {invoice.subtotal.toFixed(2)}</p>
              </div>
              <div>
                <Label>Discount</Label>
                <p className="font-medium text-green-600">-KES {invoice.totalDiscount.toFixed(2)}</p>
              </div>
              <div>
                <Label>Tax (16%)</Label>
                <p className="font-medium">KES {invoice.taxAmount.toFixed(2)}</p>
              </div>
              <div>
                <Label>Grand Total</Label>
                <p className="text-lg font-bold">KES {invoice.grandTotal.toFixed(2)}</p>
              </div>
            </div>
            
            {editMode && (
              <div className="space-y-2">
                <Label htmlFor="dueDate">Due Date</Label>
                <Input
                  id="dueDate"
                  type="date"
                  value={invoice.dueDate}
                  onChange={(e) => setInvoice(prev => prev ? { ...prev, dueDate: e.target.value } : null)}
                />
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Invoice Items */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Services & Charges</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Service</TableHead>
                <TableHead>Quantity</TableHead>
                <TableHead>Unit Price</TableHead>
                <TableHead>Discount %</TableHead>
                <TableHead>Total</TableHead>
                {editMode && <TableHead>Actions</TableHead>}
              </TableRow>
            </TableHeader>
            <TableBody>
              {invoice.items.map((category, categoryIndex) => (
                <>
                  <TableRow key={`category-${categoryIndex}`} className="bg-muted/50">
                    <TableCell colSpan={editMode ? 6 : 5} className="font-medium">
                      {category.categoryName}
                    </TableCell>
                  </TableRow>
                  {category.services.map((service, serviceIndex) => (
                    <TableRow key={`service-${categoryIndex}-${serviceIndex}`}>
                      <TableCell>{service.serviceName}</TableCell>
                      <TableCell>
                        {editMode ? (
                          <Input
                            type="number"
                            value={service.quantity}
                            onChange={(e) => updateItemPrice(categoryIndex, serviceIndex, 'quantity', parseFloat(e.target.value) || 0)}
                            className="w-20"
                          />
                        ) : (
                          service.quantity
                        )}
                      </TableCell>
                      <TableCell>
                        {editMode ? (
                          <Input
                            type="number"
                            value={service.unitPrice}
                            onChange={(e) => updateItemPrice(categoryIndex, serviceIndex, 'unitPrice', parseFloat(e.target.value) || 0)}
                            className="w-24"
                          />
                        ) : (
                          `KES ${service.unitPrice.toFixed(2)}`
                        )}
                      </TableCell>
                      <TableCell>
                        {editMode ? (
                          <Input
                            type="number"
                            value={service.discount}
                            onChange={(e) => updateItemPrice(categoryIndex, serviceIndex, 'discount', parseFloat(e.target.value) || 0)}
                            className="w-20"
                          />
                        ) : (
                          `${service.discount}%`
                        )}
                      </TableCell>
                      <TableCell className="font-medium">
                        KES {service.total.toFixed(2)}
                      </TableCell>
                      {editMode && (
                        <TableCell>
                          <Button variant="ghost" size="sm">
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </TableCell>
                      )}
                    </TableRow>
                  ))}
                </>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
