import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Textarea } from "@/components/ui/textarea";
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from "@/components/ui/dialog";
import { 
  Bot, 
  Lightbulb, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  Stethoscope,
  Calendar,
  TrendingUp,
  MessageSquare,
  Send,
  Loader2
} from "lucide-react";
import { useAuth } from "@/store";
import { api } from "@/services/api";
import { toast } from "@/components/ui/use-toast";

interface AISuggestion {
  suggestionId: number;
  type: 'appointment' | 'medical' | 'workflow' | 'insight';
  title: string;
  description: string;
  confidence: number;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  actionable: boolean;
  metadata?: any;
}

interface AIChat {
  id: string;
  message: string;
  response: string;
  timestamp: Date;
  type: 'appointment' | 'medical' | 'general';
}

export function AIAssistant() {
  const { currentClinic } = useAuth();
  const [loading, setLoading] = useState(false);
  const [suggestions, setSuggestions] = useState<AISuggestion[]>([]);
  const [chatHistory, setChatHistory] = useState<AIChat[]>([]);
  const [currentMessage, setCurrentMessage] = useState('');
  const [chatOpen, setChatOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<'suggestions' | 'chat' | 'insights'>('suggestions');

  useEffect(() => {
    if (currentClinic) {
      fetchAISuggestions();
      fetchClinicInsights();
    }
  }, [currentClinic]);

  const fetchAISuggestions = async () => {
    if (!currentClinic) return;
    
    setLoading(true);
    try {
      const response = await api.get('/ai/suggestions', {
        params: { clinicId: currentClinic.clinicId }
      });
      
      if (response.success) {
        setSuggestions(response.data.suggestions || []);
      }
    } catch (error) {
      console.error('Error fetching AI suggestions:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchClinicInsights = async () => {
    if (!currentClinic) return;
    
    try {
      const response = await api.get('/ai/clinic-insights', {
        params: { clinicId: currentClinic.clinicId }
      });
      
      if (response.success) {
        // Handle insights data
        console.log('Clinic insights:', response.data);
      }
    } catch (error) {
      console.error('Error fetching clinic insights:', error);
    }
  };

  const sendChatMessage = async () => {
    if (!currentMessage.trim() || !currentClinic) return;
    
    const message = currentMessage.trim();
    setCurrentMessage('');
    setLoading(true);
    
    try {
      const response = await api.post('/ai/chat', {
        message,
        clinicId: currentClinic.clinicId,
        context: 'general'
      });
      
      if (response.success) {
        const newChat: AIChat = {
          id: Date.now().toString(),
          message,
          response: response.data.response,
          timestamp: new Date(),
          type: 'general'
        };
        
        setChatHistory(prev => [...prev, newChat]);
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to get AI response",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const applySuggestion = async (suggestion: AISuggestion) => {
    if (!suggestion.actionable) return;
    
    setLoading(true);
    try {
      const response = await api.post(`/ai/apply-suggestion/${suggestion.suggestionId}`);
      
      if (response.success) {
        toast({
          title: "Success",
          description: "AI suggestion applied successfully",
        });
        fetchAISuggestions(); // Refresh suggestions
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to apply suggestion",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const getSuggestionIcon = (type: string) => {
    switch (type) {
      case 'appointment': return <Calendar className="h-4 w-4" />;
      case 'medical': return <Stethoscope className="h-4 w-4" />;
      case 'workflow': return <TrendingUp className="h-4 w-4" />;
      case 'insight': return <Lightbulb className="h-4 w-4" />;
      default: return <Bot className="h-4 w-4" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'destructive';
      case 'high': return 'default';
      case 'normal': return 'secondary';
      case 'low': return 'outline';
      default: return 'secondary';
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600';
    if (confidence >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  if (!currentClinic) {
    return (
      <Alert>
        <Bot className="h-4 w-4" />
        <AlertDescription>
          Please select a clinic to access AI assistance.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* AI Assistant Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Bot className="h-5 w-5" />
              AI Assistant - {currentClinic.clinicName}
            </CardTitle>
            <div className="flex gap-2">
              <Button
                variant={activeTab === 'suggestions' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setActiveTab('suggestions')}
              >
                Suggestions
              </Button>
              <Button
                variant={activeTab === 'insights' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setActiveTab('insights')}
              >
                Insights
              </Button>
              <Dialog open={chatOpen} onOpenChange={setChatOpen}>
                <DialogTrigger asChild>
                  <Button variant="outline" size="sm">
                    <MessageSquare className="h-4 w-4 mr-2" />
                    Chat
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl max-h-[600px]">
                  <DialogHeader>
                    <DialogTitle>AI Chat Assistant</DialogTitle>
                  </DialogHeader>
                  <div className="flex flex-col h-[500px]">
                    {/* Chat History */}
                    <div className="flex-1 overflow-y-auto space-y-4 p-4 border rounded-lg">
                      {chatHistory.length === 0 ? (
                        <div className="text-center text-muted-foreground">
                          Start a conversation with the AI assistant
                        </div>
                      ) : (
                        chatHistory.map((chat) => (
                          <div key={chat.id} className="space-y-2">
                            <div className="bg-blue-50 p-3 rounded-lg">
                              <div className="font-medium text-sm text-blue-800">You:</div>
                              <div>{chat.message}</div>
                            </div>
                            <div className="bg-gray-50 p-3 rounded-lg">
                              <div className="font-medium text-sm text-gray-800 flex items-center gap-1">
                                <Bot className="h-3 w-3" />
                                AI Assistant:
                              </div>
                              <div>{chat.response}</div>
                            </div>
                          </div>
                        ))
                      )}
                    </div>
                    
                    {/* Chat Input */}
                    <div className="flex gap-2 mt-4">
                      <Input
                        value={currentMessage}
                        onChange={(e) => setCurrentMessage(e.target.value)}
                        placeholder="Ask the AI assistant anything..."
                        onKeyPress={(e) => e.key === 'Enter' && sendChatMessage()}
                        disabled={loading}
                      />
                      <Button 
                        onClick={sendChatMessage}
                        disabled={loading || !currentMessage.trim()}
                        size="sm"
                      >
                        {loading ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <Send className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* AI Suggestions */}
      {activeTab === 'suggestions' && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium">AI Suggestions</h3>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={fetchAISuggestions}
              disabled={loading}
            >
              {loading ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <Bot className="h-4 w-4 mr-2" />
              )}
              Refresh
            </Button>
          </div>
          
          {suggestions.length === 0 ? (
            <Card>
              <CardContent className="p-6 text-center text-muted-foreground">
                No AI suggestions available at the moment.
              </CardContent>
            </Card>
          ) : (
            <div className="grid gap-4">
              {suggestions.map((suggestion) => (
                <Card key={suggestion.suggestionId}>
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          {getSuggestionIcon(suggestion.type)}
                          <h4 className="font-medium">{suggestion.title}</h4>
                          <Badge variant={getPriorityColor(suggestion.priority)}>
                            {suggestion.priority}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground mb-3">
                          {suggestion.description}
                        </p>
                        <div className="flex items-center gap-4 text-xs text-muted-foreground">
                          <span className={`font-medium ${getConfidenceColor(suggestion.confidence)}`}>
                            {Math.round(suggestion.confidence * 100)}% confidence
                          </span>
                          <span className="capitalize">{suggestion.type} suggestion</span>
                        </div>
                      </div>
                      {suggestion.actionable && (
                        <Button 
                          size="sm"
                          onClick={() => applySuggestion(suggestion)}
                          disabled={loading}
                        >
                          Apply
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      )}

      {/* AI Insights */}
      {activeTab === 'insights' && (
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Clinic Insights</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2 mb-2">
                  <TrendingUp className="h-4 w-4 text-green-600" />
                  <h4 className="font-medium">Efficiency Score</h4>
                </div>
                <div className="text-2xl font-bold text-green-600">87%</div>
                <p className="text-xs text-muted-foreground">
                  Above average for similar clinics
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2 mb-2">
                  <Clock className="h-4 w-4 text-blue-600" />
                  <h4 className="font-medium">Avg Wait Time</h4>
                </div>
                <div className="text-2xl font-bold text-blue-600">12 min</div>
                <p className="text-xs text-muted-foreground">
                  2 minutes better than last month
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2 mb-2">
                  <CheckCircle className="h-4 w-4 text-purple-600" />
                  <h4 className="font-medium">Completion Rate</h4>
                </div>
                <div className="text-2xl font-bold text-purple-600">94%</div>
                <p className="text-xs text-muted-foreground">
                  Excellent appointment completion
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      )}
    </div>
  );
}
