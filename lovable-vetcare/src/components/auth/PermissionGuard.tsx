/**
 * Permission Guard Components
 * 
 * These components provide declarative permission-based rendering
 * for UI elements based on user roles and permissions.
 */

import React from 'react';
import { usePermissions } from '@/hooks/usePermissions';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Lock } from 'lucide-react';

interface PermissionGuardProps {
  children: React.ReactNode;
  permission?: string | string[];
  role?: string | string[];
  requireAll?: boolean; // If true, user must have ALL permissions/roles
  fallback?: React.ReactNode;
  showFallback?: boolean;
}

/**
 * Guard component that shows/hides content based on permissions
 */
export const PermissionGuard: React.FC<PermissionGuardProps> = ({
  children,
  permission,
  role,
  requireAll = false,
  fallback,
  showFallback = false
}) => {
  const { hasPermission, hasAnyPermission, hasRole, hasAnyRole } = usePermissions();

  let hasAccess = true;

  // Check permissions
  if (permission) {
    if (Array.isArray(permission)) {
      hasAccess = requireAll 
        ? permission.every(p => hasPermission(p))
        : hasAnyPermission(permission);
    } else {
      hasAccess = hasPermission(permission);
    }
  }

  // Check roles (only if permission check passed)
  if (hasAccess && role) {
    if (Array.isArray(role)) {
      hasAccess = requireAll 
        ? role.every(r => hasRole(r))
        : hasAnyRole(role);
    } else {
      hasAccess = hasRole(role);
    }
  }

  if (!hasAccess) {
    if (showFallback && fallback) {
      return <>{fallback}</>;
    }
    if (showFallback) {
      return (
        <Alert className="border-amber-200 bg-amber-50">
          <Lock className="h-4 w-4" />
          <AlertDescription>
            You don't have permission to access this content.
          </AlertDescription>
        </Alert>
      );
    }
    return null;
  }

  return <>{children}</>;
};

/**
 * Guard for system admin only content
 */
export const AdminOnly: React.FC<{
  children: React.ReactNode;
  fallback?: React.ReactNode;
  showFallback?: boolean;
}> = ({ children, fallback, showFallback = false }) => {
  const { isSystemAdmin } = usePermissions();

  if (!isSystemAdmin()) {
    if (showFallback && fallback) {
      return <>{fallback}</>;
    }
    if (showFallback) {
      return (
        <Alert className="border-red-200 bg-red-50">
          <Lock className="h-4 w-4" />
          <AlertDescription>
            Admin access required.
          </AlertDescription>
        </Alert>
      );
    }
    return null;
  }

  return <>{children}</>;
};

/**
 * Guard for clinic admin/owner content
 */
export const ClinicAdminOnly: React.FC<{
  children: React.ReactNode;
  fallback?: React.ReactNode;
  showFallback?: boolean;
}> = ({ children, fallback, showFallback = false }) => {
  const { isClinicAdmin } = usePermissions();

  if (!isClinicAdmin()) {
    if (showFallback && fallback) {
      return <>{fallback}</>;
    }
    if (showFallback) {
      return (
        <Alert className="border-amber-200 bg-amber-50">
          <Lock className="h-4 w-4" />
          <AlertDescription>
            Clinic admin access required.
          </AlertDescription>
        </Alert>
      );
    }
    return null;
  }

  return <>{children}</>;
};

/**
 * Guard for staff only content
 */
export const StaffOnly: React.FC<{
  children: React.ReactNode;
  fallback?: React.ReactNode;
  showFallback?: boolean;
}> = ({ children, fallback, showFallback = false }) => {
  const { currentUser } = usePermissions();

  if (currentUser.userType !== 'staff') {
    if (showFallback && fallback) {
      return <>{fallback}</>;
    }
    if (showFallback) {
      return (
        <Alert className="border-blue-200 bg-blue-50">
          <Lock className="h-4 w-4" />
          <AlertDescription>
            Staff access required.
          </AlertDescription>
        </Alert>
      );
    }
    return null;
  }

  return <>{children}</>;
};

/**
 * Guard for veterinarian only content
 */
export const VeterinarianOnly: React.FC<{
  children: React.ReactNode;
  fallback?: React.ReactNode;
  showFallback?: boolean;
}> = ({ children, fallback, showFallback = false }) => {
  const { isVeterinarian } = usePermissions();

  if (!isVeterinarian()) {
    if (showFallback && fallback) {
      return <>{fallback}</>;
    }
    if (showFallback) {
      return (
        <Alert className="border-green-200 bg-green-50">
          <Lock className="h-4 w-4" />
          <AlertDescription>
            Veterinarian access required.
          </AlertDescription>
        </Alert>
      );
    }
    return null;
  }

  return <>{children}</>;
};

/**
 * Conditional button that shows/hides based on permissions
 */
interface PermissionButtonProps {
  children: React.ReactNode;
  permission?: string | string[];
  role?: string | string[];
  requireAll?: boolean;
  className?: string;
  onClick?: () => void;
  disabled?: boolean;
}

export const PermissionButton: React.FC<PermissionButtonProps> = ({
  children,
  permission,
  role,
  requireAll = false,
  className,
  onClick,
  disabled = false,
  ...props
}) => {
  const { hasPermission, hasAnyPermission, hasRole, hasAnyRole } = usePermissions();

  let hasAccess = true;

  // Check permissions
  if (permission) {
    if (Array.isArray(permission)) {
      hasAccess = requireAll 
        ? permission.every(p => hasPermission(p))
        : hasAnyPermission(permission);
    } else {
      hasAccess = hasPermission(permission);
    }
  }

  // Check roles (only if permission check passed)
  if (hasAccess && role) {
    if (Array.isArray(role)) {
      hasAccess = requireAll 
        ? role.every(r => hasRole(r))
        : hasAnyRole(role);
    } else {
      hasAccess = hasRole(role);
    }
  }

  if (!hasAccess) {
    return null;
  }

  return (
    <button
      className={className}
      onClick={onClick}
      disabled={disabled}
      {...props}
    >
      {children}
    </button>
  );
};

/**
 * Navigation item that shows/hides based on permissions
 */
interface PermissionNavItemProps {
  children: React.ReactNode;
  permission?: string | string[];
  role?: string | string[];
  requireAll?: boolean;
  href?: string;
  className?: string;
}

export const PermissionNavItem: React.FC<PermissionNavItemProps> = ({
  children,
  permission,
  role,
  requireAll = false,
  href,
  className,
  ...props
}) => {
  const { hasPermission, hasAnyPermission, hasRole, hasAnyRole } = usePermissions();

  let hasAccess = true;

  // Check permissions
  if (permission) {
    if (Array.isArray(permission)) {
      hasAccess = requireAll 
        ? permission.every(p => hasPermission(p))
        : hasAnyPermission(permission);
    } else {
      hasAccess = hasPermission(permission);
    }
  }

  // Check roles (only if permission check passed)
  if (hasAccess && role) {
    if (Array.isArray(role)) {
      hasAccess = requireAll 
        ? role.every(r => hasRole(r))
        : hasAnyRole(role);
    } else {
      hasAccess = hasRole(role);
    }
  }

  if (!hasAccess) {
    return null;
  }

  return (
    <a href={href} className={className} {...props}>
      {children}
    </a>
  );
};
