/**
 * Clinic Selector Modal Component
 * 
 * A modal dialog for selecting and switching between clinics.
 * Includes search functionality and proper clinic context management.
 */

import React, { useState, useMemo } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Building2, 
  Search, 
  Check, 
  MapPin, 
  Phone, 
  Mail,
  Users,
  Clock
} from 'lucide-react';
import { useAuth } from '@/store';
import { usePermissions } from '@/hooks/usePermissions';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';

interface ClinicSelectorModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const ClinicSelectorModal: React.FC<ClinicSelectorModalProps> = ({
  open,
  onOpenChange
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const { 
    availableClinics, 
    clinic: currentClinic, 
    switchClinic 
  } = useAuth();
  const { isSystemAdmin, canSwitchClinics } = usePermissions();

  // Filter clinics based on search query
  const filteredClinics = useMemo(() => {
    if (!searchQuery.trim()) return availableClinics;
    
    const query = searchQuery.toLowerCase();
    return availableClinics.filter(clinic => 
      clinic.clinicName?.toLowerCase().includes(query) ||
      clinic.address?.toLowerCase().includes(query) ||
      clinic.email?.toLowerCase().includes(query) ||
      clinic.phoneNumber?.includes(query)
    );
  }, [availableClinics, searchQuery]);

  const handleClinicSelect = async (clinicId: number) => {
    if (clinicId === currentClinic?.clinicId) {
      onOpenChange(false);
      return;
    }

    setLoading(true);
    try {
      const result = await switchClinic(clinicId);
      if (result.success) {
        console.log('Successfully switched to clinic:', clinicId);
        onOpenChange(false);
        // Reload the page to update context
        window.location.reload();
      } else {
        console.error('Failed to switch clinic:', result.message);
      }
    } catch (error) {
      console.error('Error switching clinic:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setSearchQuery('');
    onOpenChange(false);
  };

  if (!canSwitchClinics()) {
    return null;
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Select Clinic
          </DialogTitle>
          <DialogDescription>
            Choose a clinic to switch your current context. 
            {isSystemAdmin() && " As an admin, you have access to all clinics."}
          </DialogDescription>
        </DialogHeader>

        {/* Search Input */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search clinics by name, address, or contact..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Current Clinic */}
        {currentClinic && (
          <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 dark:bg-blue-800 rounded-lg">
                <Building2 className="h-4 w-4 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <h4 className="font-medium text-blue-900 dark:text-blue-100">
                    {currentClinic.clinicName}
                  </h4>
                  <Badge variant="secondary" className="bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100">
                    Current
                  </Badge>
                </div>
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  {currentClinic.address}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Clinics List */}
        <div className="flex-1 overflow-y-auto max-h-[400px] space-y-2">
          <AnimatePresence>
            {filteredClinics.length === 0 ? (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="text-center py-8 text-gray-500"
              >
                <Building2 className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>No clinics found matching your search.</p>
              </motion.div>
            ) : (
              filteredClinics.map((clinic, index) => {
                const isSelected = clinic.clinicId === currentClinic?.clinicId;
                
                return (
                  <motion.div
                    key={clinic.clinicId}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ delay: index * 0.05 }}
                    className={cn(
                      "p-4 rounded-lg border transition-all duration-200 cursor-pointer",
                      isSelected
                        ? "bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800"
                        : "bg-white border-gray-200 hover:bg-gray-50 dark:bg-gray-800 dark:border-gray-700 dark:hover:bg-gray-700"
                    )}
                    onClick={() => handleClinicSelect(clinic.clinicId)}
                  >
                    <div className="flex items-start gap-3">
                      <Avatar className="w-12 h-12">
                        <AvatarImage src={clinic.logoUrl} />
                        <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white">
                          {clinic.clinicName?.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <h3 className="font-medium text-gray-900 dark:text-gray-100 truncate">
                            {clinic.clinicName}
                          </h3>
                          {isSelected && (
                            <Check className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                          )}
                        </div>
                        
                        <div className="space-y-1 text-sm text-gray-600 dark:text-gray-400">
                          {clinic.address && (
                            <div className="flex items-center gap-1">
                              <MapPin className="h-3 w-3" />
                              <span className="truncate">{clinic.address}</span>
                            </div>
                          )}
                          
                          <div className="flex items-center gap-4">
                            {clinic.phoneNumber && (
                              <div className="flex items-center gap-1">
                                <Phone className="h-3 w-3" />
                                <span>{clinic.phoneNumber}</span>
                              </div>
                            )}
                            
                            {clinic.email && (
                              <div className="flex items-center gap-1">
                                <Mail className="h-3 w-3" />
                                <span className="truncate">{clinic.email}</span>
                              </div>
                            )}
                          </div>
                          
                          {clinic.staffCount && (
                            <div className="flex items-center gap-1">
                              <Users className="h-3 w-3" />
                              <span>{clinic.staffCount} staff members</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </motion.div>
                );
              })
            )}
          </AnimatePresence>
        </div>

        {/* Footer */}
        <div className="flex justify-between items-center pt-4 border-t">
          <p className="text-sm text-gray-500">
            {filteredClinics.length} clinic{filteredClinics.length !== 1 ? 's' : ''} available
          </p>
          
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            {isSystemAdmin() && (
              <Button asChild>
                <a href="/admin/clinics" target="_blank" rel="noopener noreferrer">
                  Manage Clinics
                </a>
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
