import { useState, useEffect } from "react";
import { Building2, Check, ChevronsUpDown } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useAuth } from "@/store";
import { Clinic } from "@/store/types";
import { cn } from "@/lib/utils";
import { Skeleton } from "@/components/ui/skeleton";

export function ClinicSwitcher() {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const { staff, currentClinic, availableClinics, switchClinic } = useAuth();

  // Check if user has multiple clinics or is a clinic owner
  const isClinicOwner = staff?.isClinicOwner;
  const clinics = availableClinics || [];
  const hasMultipleClinics = clinics.length > 1;
  const showClinicSwitcher = isClinicOwner || hasMultipleClinics;

  if (!showClinicSwitcher) return null;

  const handleClinicSelect = async (clinicId: string) => {
    if (clinicId === currentClinic?.clinicId?.toString()) {
      setOpen(false);
      return;
    }

    setLoading(true);
    try {
      const result = await switchClinic(clinicId);
      if (result.success) {
        console.log('Successfully switched to clinic:', clinicId);
      } else {
        console.error('Failed to switch clinic:', result.message);
      }
    } catch (error) {
      console.error('Error switching clinic:', error);
    } finally {
      setLoading(false);
      setOpen(false);
    }
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          aria-label="Select a clinic"
          className={cn("w-[200px] justify-between", loading && "opacity-50 cursor-not-allowed")}
          disabled={loading}
        >
          <div className="flex items-center gap-2 truncate">
            <Building2 className="h-4 w-4" />
            <span className="truncate">
              {currentClinic?.clinicName || "Select clinic"}
            </span>
          </div>
          <ChevronsUpDown className="ml-auto h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[200px] p-0">
        <Command>
          <CommandInput placeholder="Search clinic..." />
          <CommandList>
            <CommandEmpty>No clinic found.</CommandEmpty>
            <CommandGroup>
              {clinics.map((clinic) => (
                <CommandItem
                  key={clinic.clinicId}
                  value={clinic.clinicId?.toString()}
                  onSelect={() => handleClinicSelect(clinic.clinicId?.toString()!)}
                  className="cursor-pointer"
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      currentClinic?.clinicId === clinic.clinicId
                        ? "opacity-100"
                        : "opacity-0"
                    )}
                  />
                  <span className="truncate">{clinic.clinicName}</span>
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}

export function ClinicSwitcherSkeleton() {
  return <Skeleton className="h-9 w-[200px] rounded-md" />;
}
