import { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  <PERSON>alog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from "@/components/ui/dialog";
import { 
  Bell, 
  BellRing, 
  Calendar, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  User, 
  PawPrint,
  X,
  Settings,
  Volume2,
  VolumeX
} from "lucide-react";
import { useAuth } from "@/store";
import { toast } from "@/components/ui/use-toast";
import { io, Socket } from "socket.io-client";

interface Notification {
  id: string;
  type: 'appointment' | 'emergency' | 'reminder' | 'system' | 'ai_suggestion';
  title: string;
  message: string;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  timestamp: Date;
  read: boolean;
  actionable: boolean;
  metadata?: any;
}

interface NotificationSettings {
  soundEnabled: boolean;
  showDesktop: boolean;
  emergencyOnly: boolean;
  categories: {
    appointments: boolean;
    emergencies: boolean;
    reminders: boolean;
    system: boolean;
    ai_suggestions: boolean;
  };
}

export function RealTimeNotifications() {
  const { currentClinic, staff } = useAuth();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [connected, setConnected] = useState(false);
  const [settingsOpen, setSettingsOpen] = useState(false);
  const [notificationsOpen, setNotificationsOpen] = useState(false);
  const socketRef = useRef<Socket | null>(null);
  
  const [settings, setSettings] = useState<NotificationSettings>({
    soundEnabled: true,
    showDesktop: true,
    emergencyOnly: false,
    categories: {
      appointments: true,
      emergencies: true,
      reminders: true,
      system: true,
      ai_suggestions: true
    }
  });

  useEffect(() => {
    if (currentClinic && staff) {
      initializeWebSocket();
      requestNotificationPermission();
    }

    return () => {
      if (socketRef.current) {
        socketRef.current.disconnect();
      }
    };
  }, [currentClinic, staff]);

  const initializeWebSocket = () => {
    if (!currentClinic || !staff) return;

    // Connect to WebSocket server
    const socket = io('http://localhost:5500', {
      auth: {
        clinicId: currentClinic.clinicId,
        staffId: staff.staffId,
        token: localStorage.getItem('token')
      }
    });

    socket.on('connect', () => {
      console.log('Connected to WebSocket server');
      setConnected(true);
      
      // Join clinic room for clinic-specific notifications
      socket.emit('join-clinic', currentClinic.clinicId);
    });

    socket.on('disconnect', () => {
      console.log('Disconnected from WebSocket server');
      setConnected(false);
    });

    // Listen for real-time notifications
    socket.on('notification', (notification: Notification) => {
      handleNewNotification(notification);
    });

    // Listen for appointment updates
    socket.on('appointment-update', (data: any) => {
      const notification: Notification = {
        id: `appointment-${data.appointmentId}-${Date.now()}`,
        type: 'appointment',
        title: 'Appointment Updated',
        message: `Appointment for ${data.petName} has been ${data.status}`,
        priority: 'normal',
        timestamp: new Date(),
        read: false,
        actionable: true,
        metadata: { appointmentId: data.appointmentId }
      };
      handleNewNotification(notification);
    });

    // Listen for emergency alerts
    socket.on('emergency-alert', (data: any) => {
      const notification: Notification = {
        id: `emergency-${Date.now()}`,
        type: 'emergency',
        title: 'Emergency Alert',
        message: data.message,
        priority: 'urgent',
        timestamp: new Date(),
        read: false,
        actionable: true,
        metadata: data
      };
      handleNewNotification(notification);
    });

    // Listen for AI suggestions
    socket.on('ai-suggestion', (data: any) => {
      const notification: Notification = {
        id: `ai-${data.suggestionId}-${Date.now()}`,
        type: 'ai_suggestion',
        title: 'AI Suggestion',
        message: data.title,
        priority: data.priority || 'normal',
        timestamp: new Date(),
        read: false,
        actionable: true,
        metadata: data
      };
      handleNewNotification(notification);
    });

    socketRef.current = socket;
  };

  const requestNotificationPermission = async () => {
    if ('Notification' in window && Notification.permission === 'default') {
      await Notification.requestPermission();
    }
  };

  const handleNewNotification = (notification: Notification) => {
    // Check if notification type is enabled
    if (!settings.categories[notification.type as keyof typeof settings.categories]) {
      return;
    }

    // Check if emergency only mode is enabled
    if (settings.emergencyOnly && notification.priority !== 'urgent') {
      return;
    }

    // Add to notifications list
    setNotifications(prev => [notification, ...prev.slice(0, 49)]); // Keep last 50
    setUnreadCount(prev => prev + 1);

    // Play sound if enabled
    if (settings.soundEnabled) {
      playNotificationSound(notification.priority);
    }

    // Show desktop notification if enabled
    if (settings.showDesktop && 'Notification' in window && Notification.permission === 'granted') {
      new Notification(notification.title, {
        body: notification.message,
        icon: '/favicon.ico',
        tag: notification.id
      });
    }

    // Show toast notification
    toast({
      title: notification.title,
      description: notification.message,
      variant: notification.priority === 'urgent' ? 'destructive' : 'default'
    });
  };

  const playNotificationSound = (priority: string) => {
    const audio = new Audio();
    switch (priority) {
      case 'urgent':
        audio.src = '/sounds/urgent.mp3';
        break;
      case 'high':
        audio.src = '/sounds/high.mp3';
        break;
      default:
        audio.src = '/sounds/notification.mp3';
        break;
    }
    audio.play().catch(() => {
      // Ignore audio play errors (user interaction required)
    });
  };

  const markAsRead = (notificationId: string) => {
    setNotifications(prev => 
      prev.map(n => 
        n.id === notificationId ? { ...n, read: true } : n
      )
    );
    setUnreadCount(prev => Math.max(0, prev - 1));
  };

  const markAllAsRead = () => {
    setNotifications(prev => prev.map(n => ({ ...n, read: true })));
    setUnreadCount(0);
  };

  const removeNotification = (notificationId: string) => {
    const notification = notifications.find(n => n.id === notificationId);
    if (notification && !notification.read) {
      setUnreadCount(prev => Math.max(0, prev - 1));
    }
    setNotifications(prev => prev.filter(n => n.id !== notificationId));
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'appointment': return <Calendar className="h-4 w-4" />;
      case 'emergency': return <AlertTriangle className="h-4 w-4" />;
      case 'reminder': return <Clock className="h-4 w-4" />;
      case 'system': return <Settings className="h-4 w-4" />;
      case 'ai_suggestion': return <CheckCircle className="h-4 w-4" />;
      default: return <Bell className="h-4 w-4" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'destructive';
      case 'high': return 'default';
      case 'normal': return 'secondary';
      case 'low': return 'outline';
      default: return 'secondary';
    }
  };

  const formatTimestamp = (timestamp: Date) => {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const minutes = Math.floor(diff / 60000);
    
    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (minutes < 1440) return `${Math.floor(minutes / 60)}h ago`;
    return timestamp.toLocaleDateString();
  };

  return (
    <div className="relative">
      {/* Notification Bell */}
      <Dialog open={notificationsOpen} onOpenChange={setNotificationsOpen}>
        <DialogTrigger asChild>
          <Button variant="ghost" size="sm" className="relative">
            {connected ? (
              <BellRing className="h-5 w-5" />
            ) : (
              <Bell className="h-5 w-5" />
            )}
            {unreadCount > 0 && (
              <Badge 
                variant="destructive" 
                className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 text-xs"
              >
                {unreadCount > 99 ? '99+' : unreadCount}
              </Badge>
            )}
          </Button>
        </DialogTrigger>
        <DialogContent className="max-w-md max-h-[600px]">
          <DialogHeader>
            <div className="flex items-center justify-between">
              <DialogTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5" />
                Notifications
                {!connected && (
                  <Badge variant="outline" className="text-xs">
                    Offline
                  </Badge>
                )}
              </DialogTitle>
              <div className="flex gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={markAllAsRead}
                  disabled={unreadCount === 0}
                >
                  Mark all read
                </Button>
                <Dialog open={settingsOpen} onOpenChange={setSettingsOpen}>
                  <DialogTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <Settings className="h-4 w-4" />
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-sm">
                    <DialogHeader>
                      <DialogTitle>Notification Settings</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Sound notifications</span>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setSettings(prev => ({ ...prev, soundEnabled: !prev.soundEnabled }))}
                        >
                          {settings.soundEnabled ? (
                            <Volume2 className="h-4 w-4" />
                          ) : (
                            <VolumeX className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Desktop notifications</span>
                        <input
                          type="checkbox"
                          checked={settings.showDesktop}
                          onChange={(e) => setSettings(prev => ({ ...prev, showDesktop: e.target.checked }))}
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Emergency only</span>
                        <input
                          type="checkbox"
                          checked={settings.emergencyOnly}
                          onChange={(e) => setSettings(prev => ({ ...prev, emergencyOnly: e.target.checked }))}
                        />
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
              </div>
            </div>
          </DialogHeader>
          
          <div className="space-y-2 max-h-[400px] overflow-y-auto">
            {notifications.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                No notifications yet
              </div>
            ) : (
              notifications.map((notification) => (
                <Card 
                  key={notification.id} 
                  className={`cursor-pointer transition-colors ${
                    !notification.read ? 'bg-blue-50 border-blue-200' : ''
                  }`}
                  onClick={() => markAsRead(notification.id)}
                >
                  <CardContent className="p-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          {getNotificationIcon(notification.type)}
                          <span className="font-medium text-sm">{notification.title}</span>
                          <Badge variant={getPriorityColor(notification.priority)} className="text-xs">
                            {notification.priority}
                          </Badge>
                        </div>
                        <p className="text-xs text-muted-foreground mb-2">
                          {notification.message}
                        </p>
                        <div className="text-xs text-muted-foreground">
                          {formatTimestamp(notification.timestamp)}
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          removeNotification(notification.id);
                        }}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Connection Status Indicator */}
      {!connected && (
        <Alert className="mt-2">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Real-time notifications are currently offline. Reconnecting...
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}
