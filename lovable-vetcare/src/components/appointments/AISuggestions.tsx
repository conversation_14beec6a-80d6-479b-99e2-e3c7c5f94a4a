import React, { useState } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  Brain,
  Lightbulb,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  Pill,
  Stethoscope,
  FileText,
  ThumbsUp,
  ThumbsDown,
  Wand2
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { useToast } from '@/components/ui/use-toast';
import { api } from '@/services/api';

interface AISuggestionsProps {
  appointmentId: number;
  petData?: any;
  appointmentData?: any;
  clinicId?: number;
}

interface AISuggestion {
  suggestionId: number;
  category: string;
  suggestionType: string;
  title: string;
  description: string;
  confidenceScore: number;
  priority: string;
  medications: any[];
  treatments: any[];
  diagnosticTests: any[];
  followUp: any;
  status: string;
  reviewNotes?: string;
  createdAt: string;
}

const AISuggestions: React.FC<AISuggestionsProps> = ({
  appointmentId,
  petData,
  appointmentData,
  clinicId
}) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const [isGenerating, setIsGenerating] = useState(false);
  const [showSuggestionForm, setShowSuggestionForm] = useState(false);
  const [suggestionForm, setSuggestionForm] = useState({
    symptoms: '',
    vitalSigns: {
      temperature: '',
      heartRate: '',
      respiratoryRate: '',
      weight: '',
      bloodPressure: ''
    },
    previousConditions: '',
    currentMedications: '',
    allergies: '',
    requestedCategories: ['diagnosis', 'treatment']
  });
  const [generationContext, setGenerationContext] = useState({
    selectedCategory: '',
    selectedWords: [] as string[],
    generatedNotes: [] as Array<{
      category: string;
      content: string;
      sentiment: string;
      timestamp: string;
    }>
  });

  // Fetch AI suggestions
  const { data: suggestionsResponse, isLoading } = useQuery({
    queryKey: ['aiSuggestions', appointmentId],
    queryFn: async () => {
      const response = await api.get(`/appointments/${appointmentId}/ai-suggestions`);
      return response.data;
    }
  });

  const suggestions: AISuggestion[] = suggestionsResponse?.data?.suggestions || [];

  // Generate AI suggestions mutation
  const generateSuggestionsMutation = useMutation({
    mutationFn: async (context: any) => {
      const response = await api.post(`/appointments/${appointmentId}/ai-suggestions`, {
        symptoms: context.symptoms.split(',').map((s: string) => s.trim()).filter(Boolean),
        vitalSigns: context.vitalSigns,
        previousConditions: context.previousConditions.split(',').map((c: string) => c.trim()).filter(Boolean),
        currentMedications: context.currentMedications.split(',').map((m: string) => m.trim()).filter(Boolean),
        allergies: context.allergies.split(',').map((a: string) => a.trim()).filter(Boolean),
        requestedCategories: context.requestedCategories,
        clinicId,
        appointmentId
      });
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['aiSuggestions', appointmentId] });
      setIsGenerating(false);
      setShowSuggestionForm(false);
      toast({
        title: "Success",
        description: "AI suggestions generated successfully",
      });
    },
    onError: (error: any) => {
      setIsGenerating(false);
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to generate AI suggestions",
        variant: "destructive",
      });
    }
  });

  const handleGenerateSuggestions = () => {
    setIsGenerating(true);
    generateSuggestionsMutation.mutate(suggestionForm);
  };

  // Review suggestion mutation
  const reviewSuggestionMutation = useMutation({
    mutationFn: async ({ suggestionId, status, reviewNotes }: { suggestionId: number; status: string; reviewNotes: string }) => {
      const response = await api.put(`/appointments/suggestions/${suggestionId}/review`, {
        status,
        reviewNotes
      });
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['aiSuggestions', appointmentId] });
      toast({
        title: "Success",
        description: "Suggestion reviewed successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to review suggestion",
        variant: "destructive",
      });
    }
  });

  // Implement suggestion mutation
  const implementSuggestionMutation = useMutation({
    mutationFn: async ({ suggestionId, implementationNotes }: { suggestionId: number; implementationNotes: string }) => {
      const response = await api.put(`/appointments/suggestions/${suggestionId}/implement`, {
        implementationNotes
      });
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['aiSuggestions', appointmentId] });
      toast({
        title: "Success",
        description: "Suggestion implemented successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to implement suggestion",
        variant: "destructive",
      });
    }
  });

  const handleGenerateNotes = async () => {
    setIsGenerating(true);
    try {
      // Simulate AI note generation based on selected words and category
      const { selectedCategory, selectedWords } = generationContext;

      // Determine sentiment based on selected words
      const negativeWords = ['concerning', 'abnormal', 'declined', 'worsened', 'complications', 'adverse', 'poor response', 'deteriorated', 'critical', 'urgent'];
      const positiveWords = ['excellent', 'improved', 'responding well', 'healthy', 'optimal', 'successful', 'effective', 'progressing', 'recovered', 'thriving'];

      const hasNegative = selectedWords.some(word => negativeWords.includes(word));
      const hasPositive = selectedWords.some(word => positiveWords.includes(word));

      let sentiment = 'neutral';
      if (hasNegative && !hasPositive) sentiment = 'negative';
      else if (hasPositive && !hasNegative) sentiment = 'positive';
      else if (hasNegative && hasPositive) sentiment = 'mixed';

      // Generate contextual notes based on category and sentiment
      let noteContent = `${selectedCategory.charAt(0).toUpperCase() + selectedCategory.slice(1)} Assessment:\n\n`;

      if (sentiment === 'positive') {
        noteContent += `Patient showing ${selectedWords.join(', ')} signs during ${selectedCategory} evaluation. `;
        noteContent += `All indicators suggest favorable progress. Continue current treatment plan with regular monitoring.`;
      } else if (sentiment === 'negative') {
        noteContent += `Patient presenting with ${selectedWords.join(', ')} findings during ${selectedCategory} assessment. `;
        noteContent += `Immediate attention required. Consider additional diagnostic measures and treatment modifications.`;
      } else if (sentiment === 'mixed') {
        noteContent += `Patient showing mixed indicators: ${selectedWords.join(', ')} during ${selectedCategory} evaluation. `;
        noteContent += `Some positive progress noted alongside areas of concern. Continued monitoring and selective intervention recommended.`;
      } else {
        noteContent += `Patient maintaining ${selectedWords.join(', ')} status during ${selectedCategory} assessment. `;
        noteContent += `No significant changes from baseline. Continue routine care and monitoring protocols.`;
      }

      const newNote = {
        category: selectedCategory,
        content: noteContent,
        sentiment,
        timestamp: new Date().toISOString()
      };

      setGenerationContext(prev => ({
        ...prev,
        generatedNotes: [...prev.generatedNotes, newNote],
        selectedWords: [] // Reset selected words
      }));

      toast({
        title: "AI Notes Generated",
        description: `Generated ${sentiment} notes for ${selectedCategory} category`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to generate AI notes",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'low': return 'bg-gray-100 text-gray-800 border-gray-200';
      default: return 'bg-blue-100 text-blue-800 border-blue-200';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'accepted': return 'bg-green-100 text-green-800 border-green-200';
      case 'rejected': return 'bg-red-100 text-red-800 border-red-200';
      case 'implemented': return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'reviewed': return 'bg-blue-100 text-blue-800 border-blue-200';
      default: return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'diagnosis': return <Stethoscope className="h-4 w-4" />;
      case 'treatment': return <FileText className="h-4 w-4" />;
      case 'medication': return <Pill className="h-4 w-4" />;
      default: return <Lightbulb className="h-4 w-4" />;
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Brain className="h-5 w-5" />
          AI Suggestions & Recommendations
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* AI Suggestion Generation Form */}
        <Card className="bg-gradient-to-r from-purple-50 to-blue-50 border-purple-200">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Brain className="h-5 w-5" />
              Generate AI Suggestions
            </CardTitle>
            <p className="text-sm text-purple-700">
              Provide patient information to generate AI-powered diagnostic and treatment suggestions
            </p>
          </CardHeader>
          <CardContent>
            {!showSuggestionForm ? (
              <Button
                onClick={() => setShowSuggestionForm(true)}
                className="w-full bg-purple-600 hover:bg-purple-700"
                size="lg"
              >
                <Brain className="h-4 w-4 mr-2" />
                Start AI Suggestion Generation
              </Button>
            ) : (
              <div className="space-y-4">
                {/* Symptoms */}
                <div>
                  <Label htmlFor="symptoms">Symptoms (comma-separated)</Label>
                  <Textarea
                    id="symptoms"
                    placeholder="e.g., lethargy, loss of appetite, vomiting, coughing"
                    value={suggestionForm.symptoms}
                    onChange={(e) => setSuggestionForm(prev => ({ ...prev, symptoms: e.target.value }))}
                    rows={2}
                  />
                </div>

                {/* Vital Signs */}
                <div>
                  <Label className="text-sm font-medium mb-2 block">Vital Signs</Label>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    <div>
                      <Label htmlFor="temperature" className="text-xs">Temperature (°C)</Label>
                      <Input
                        id="temperature"
                        type="number"
                        placeholder="38.5"
                        value={suggestionForm.vitalSigns.temperature}
                        onChange={(e) => setSuggestionForm(prev => ({
                          ...prev,
                          vitalSigns: { ...prev.vitalSigns, temperature: e.target.value }
                        }))}
                      />
                    </div>
                    <div>
                      <Label htmlFor="heartRate" className="text-xs">Heart Rate (bpm)</Label>
                      <Input
                        id="heartRate"
                        type="number"
                        placeholder="120"
                        value={suggestionForm.vitalSigns.heartRate}
                        onChange={(e) => setSuggestionForm(prev => ({
                          ...prev,
                          vitalSigns: { ...prev.vitalSigns, heartRate: e.target.value }
                        }))}
                      />
                    </div>
                    <div>
                      <Label htmlFor="weight" className="text-xs">Weight (kg)</Label>
                      <Input
                        id="weight"
                        type="number"
                        placeholder="25"
                        value={suggestionForm.vitalSigns.weight}
                        onChange={(e) => setSuggestionForm(prev => ({
                          ...prev,
                          vitalSigns: { ...prev.vitalSigns, weight: e.target.value }
                        }))}
                      />
                    </div>
                  </div>
                </div>

                {/* Previous Conditions */}
                <div>
                  <Label htmlFor="previousConditions">Previous Conditions (comma-separated)</Label>
                  <Input
                    id="previousConditions"
                    placeholder="e.g., diabetes, arthritis, allergies"
                    value={suggestionForm.previousConditions}
                    onChange={(e) => setSuggestionForm(prev => ({ ...prev, previousConditions: e.target.value }))}
                  />
                </div>

                {/* Current Medications */}
                <div>
                  <Label htmlFor="currentMedications">Current Medications (comma-separated)</Label>
                  <Input
                    id="currentMedications"
                    placeholder="e.g., insulin, pain medication, antibiotics"
                    value={suggestionForm.currentMedications}
                    onChange={(e) => setSuggestionForm(prev => ({ ...prev, currentMedications: e.target.value }))}
                  />
                </div>

                {/* Allergies */}
                <div>
                  <Label htmlFor="allergies">Known Allergies (comma-separated)</Label>
                  <Input
                    id="allergies"
                    placeholder="e.g., penicillin, food allergies"
                    value={suggestionForm.allergies}
                    onChange={(e) => setSuggestionForm(prev => ({ ...prev, allergies: e.target.value }))}
                  />
                </div>

                {/* Requested Categories */}
                <div>
                  <Label className="text-sm font-medium mb-2 block">Suggestion Categories</Label>
                  <div className="flex flex-wrap gap-2">
                    {['diagnosis', 'treatment', 'medication', 'diagnostic_tests', 'follow_up'].map((category) => (
                      <Badge
                        key={category}
                        variant={suggestionForm.requestedCategories.includes(category) ? "default" : "outline"}
                        className="cursor-pointer transition-all capitalize px-3 py-2"
                        onClick={() => {
                          const newCategories = suggestionForm.requestedCategories.includes(category)
                            ? suggestionForm.requestedCategories.filter(c => c !== category)
                            : [...suggestionForm.requestedCategories, category];
                          setSuggestionForm(prev => ({ ...prev, requestedCategories: newCategories }));
                        }}
                      >
                        {category.replace('_', ' ')}
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-3 pt-4">
                  <Button
                    onClick={handleGenerateSuggestions}
                    disabled={isGenerating || suggestionForm.requestedCategories.length === 0}
                    className="flex-1 bg-purple-600 hover:bg-purple-700"
                  >
                    {isGenerating ? (
                      <>
                        <Clock className="h-4 w-4 mr-2 animate-spin" />
                        Generating...
                      </>
                    ) : (
                      <>
                        <Brain className="h-4 w-4 mr-2" />
                        Generate AI Suggestions
                      </>
                    )}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => setShowSuggestionForm(false)}
                    disabled={isGenerating}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* AI Note Generation with Choice Chips */}
        <Card className="bg-blue-50 border-blue-200">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Wand2 className="h-5 w-5" />
              AI Note Generation
            </CardTitle>
            <p className="text-sm text-blue-700">
              Select sentiment words to generate contextual notes for appointment categories
            </p>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Category Selection */}
            <div>
              <Label className="text-sm font-medium mb-3 block">Select Category for Note Generation</Label>
              <div className="flex flex-wrap gap-2">
                {['consultation', 'vaccination', 'laboratory', 'surgery', 'grooming', 'dental', 'emergency', 'medication', 'therapy', 'imaging'].map((category) => (
                  <Badge
                    key={category}
                    variant={generationContext.selectedCategory === category ? "default" : "outline"}
                    className="cursor-pointer transition-all capitalize px-3 py-2"
                    onClick={() => setGenerationContext(prev => ({ ...prev, selectedCategory: category }))}
                  >
                    {category}
                  </Badge>
                ))}
              </div>
            </div>

            {/* Sentiment Choice Chips */}
            {generationContext.selectedCategory && (
              <div className="space-y-4">
                <div>
                  <Label className="text-sm font-medium text-red-700 mb-2 block">Negative Indicators</Label>
                  <div className="flex flex-wrap gap-2">
                    {['concerning', 'abnormal', 'declined', 'worsened', 'complications', 'adverse', 'poor response', 'deteriorated', 'critical', 'urgent'].map((word) => (
                      <Badge
                        key={word}
                        variant={generationContext.selectedWords?.includes(word) ? "destructive" : "outline"}
                        className="cursor-pointer transition-all text-xs hover:bg-red-100"
                        onClick={() => {
                          const currentWords = generationContext.selectedWords || [];
                          const newWords = currentWords.includes(word)
                            ? currentWords.filter(w => w !== word)
                            : [...currentWords, word];
                          setGenerationContext(prev => ({ ...prev, selectedWords: newWords }));
                        }}
                      >
                        {word}
                      </Badge>
                    ))}
                  </div>
                </div>

                <div>
                  <Label className="text-sm font-medium text-gray-700 mb-2 block">Neutral Indicators</Label>
                  <div className="flex flex-wrap gap-2">
                    {['stable', 'routine', 'standard', 'normal', 'baseline', 'maintained', 'consistent', 'regular', 'typical', 'expected'].map((word) => (
                      <Badge
                        key={word}
                        variant={generationContext.selectedWords?.includes(word) ? "secondary" : "outline"}
                        className="cursor-pointer transition-all text-xs hover:bg-gray-100"
                        onClick={() => {
                          const currentWords = generationContext.selectedWords || [];
                          const newWords = currentWords.includes(word)
                            ? currentWords.filter(w => w !== word)
                            : [...currentWords, word];
                          setGenerationContext(prev => ({ ...prev, selectedWords: newWords }));
                        }}
                      >
                        {word}
                      </Badge>
                    ))}
                  </div>
                </div>

                <div>
                  <Label className="text-sm font-medium text-green-700 mb-2 block">Positive Indicators</Label>
                  <div className="flex flex-wrap gap-2">
                    {['excellent', 'improved', 'responding well', 'healthy', 'optimal', 'successful', 'effective', 'progressing', 'recovered', 'thriving'].map((word) => (
                      <Badge
                        key={word}
                        variant={generationContext.selectedWords?.includes(word) ? "default" : "outline"}
                        className="cursor-pointer transition-all text-xs hover:bg-green-100 bg-green-600"
                        onClick={() => {
                          const currentWords = generationContext.selectedWords || [];
                          const newWords = currentWords.includes(word)
                            ? currentWords.filter(w => w !== word)
                            : [...currentWords, word];
                          setGenerationContext(prev => ({ ...prev, selectedWords: newWords }));
                        }}
                      >
                        {word}
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Generate Button */}
                <Button
                  onClick={() => handleGenerateNotes()}
                  disabled={isGenerating || !generationContext.selectedCategory || !generationContext.selectedWords?.length}
                  className="w-full bg-blue-600 hover:bg-blue-700"
                >
                  <Brain className="h-4 w-4 mr-2" />
                  {isGenerating ? 'Generating AI Notes...' : `Generate Notes for ${generationContext.selectedCategory}`}
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Generated Notes Display */}
        {generationContext.generatedNotes.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Generated AI Notes ({generationContext.generatedNotes.length})
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {generationContext.generatedNotes.map((note, index) => (
                <Card key={index} className={`border-l-4 ${
                  note.sentiment === 'positive' ? 'border-l-green-500 bg-green-50' :
                  note.sentiment === 'negative' ? 'border-l-red-500 bg-red-50' :
                  note.sentiment === 'mixed' ? 'border-l-yellow-500 bg-yellow-50' :
                  'border-l-gray-500 bg-gray-50'
                }`}>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="capitalize">
                          {note.category}
                        </Badge>
                        <Badge className={
                          note.sentiment === 'positive' ? 'bg-green-100 text-green-800' :
                          note.sentiment === 'negative' ? 'bg-red-100 text-red-800' :
                          note.sentiment === 'mixed' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-gray-100 text-gray-800'
                        }>
                          {note.sentiment} sentiment
                        </Badge>
                      </div>
                      <div className="flex gap-2">
                        <Button size="sm" variant="outline" className="text-xs">
                          Copy to Appointment
                        </Button>
                        <Button size="sm" variant="outline" className="text-xs">
                          Edit
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          className="text-xs text-red-600"
                          onClick={() => {
                            setGenerationContext(prev => ({
                              ...prev,
                              generatedNotes: prev.generatedNotes.filter((_, i) => i !== index)
                            }));
                          }}
                        >
                          Delete
                        </Button>
                      </div>
                    </div>
                    <p className="text-sm whitespace-pre-wrap text-gray-700">{note.content}</p>
                    <div className="text-xs text-gray-500 mt-2">
                      Generated: {new Date(note.timestamp).toLocaleString()}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </CardContent>
          </Card>
        )}

        {/* Existing Suggestions */}
        {isLoading ? (
          <div className="text-center py-8">Loading suggestions...</div>
        ) : suggestions.length === 0 && generationContext.generatedNotes.length === 0 ? (
          <Card>
            <CardContent className="text-center py-8">
              <Brain className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No AI notes generated yet</p>
              <p className="text-sm text-gray-400 mt-2">Select a category and sentiment words to generate contextual notes</p>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-4">
            <h3 className="font-semibold">AI Suggestions ({suggestions.length})</h3>
            {suggestions.map((suggestion) => (
              <Card key={suggestion.suggestionId} className="border-l-4 border-l-blue-500">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-2">
                      {getCategoryIcon(suggestion.category)}
                      <div>
                        <h4 className="font-medium">{suggestion.title}</h4>
                        <p className="text-sm text-gray-600">{suggestion.category} • {suggestion.suggestionType}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className={getPriorityColor(suggestion.priority)}>
                        {suggestion.priority}
                      </Badge>
                      <Badge className={getStatusColor(suggestion.status)}>
                        {suggestion.status}
                      </Badge>
                      <Badge variant="outline">
                        {suggestion.confidenceScore}% confidence
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-700 mb-4">{suggestion.description}</p>

                  <Accordion type="single" collapsible className="w-full">
                    {suggestion.medications.length > 0 && (
                      <AccordionItem value="medications">
                        <AccordionTrigger className="text-sm">
                          <div className="flex items-center gap-2">
                            <Pill className="h-4 w-4" />
                            Medications ({suggestion.medications.length})
                          </div>
                        </AccordionTrigger>
                        <AccordionContent>
                          <div className="space-y-2">
                            {suggestion.medications.map((med, index) => (
                              <div key={index} className="p-3 bg-gray-50 rounded-lg">
                                <div className="font-medium">{med.name}</div>
                                <div className="text-sm text-gray-600">
                                  {med.dosage} • {med.frequency} • {med.duration}
                                </div>
                                {med.instructions && (
                                  <div className="text-sm text-gray-500 mt-1">{med.instructions}</div>
                                )}
                              </div>
                            ))}
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    )}

                    {suggestion.treatments.length > 0 && (
                      <AccordionItem value="treatments">
                        <AccordionTrigger className="text-sm">
                          <div className="flex items-center gap-2">
                            <FileText className="h-4 w-4" />
                            Treatments ({suggestion.treatments.length})
                          </div>
                        </AccordionTrigger>
                        <AccordionContent>
                          <div className="space-y-2">
                            {suggestion.treatments.map((treatment, index) => (
                              <div key={index} className="p-3 bg-gray-50 rounded-lg">
                                <div className="font-medium">{treatment.name}</div>
                                <div className="text-sm text-gray-600">{treatment.description}</div>
                                {treatment.estimatedCost && (
                                  <div className="text-sm text-gray-500 mt-1">
                                    Estimated cost: {treatment.currency} {treatment.estimatedCost}
                                  </div>
                                )}
                              </div>
                            ))}
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    )}
                  </Accordion>

                  {/* Action Buttons */}
                  {suggestion.status === 'pending' && (
                    <div className="flex gap-2 mt-4">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => reviewSuggestionMutation.mutate({
                          suggestionId: suggestion.suggestionId,
                          status: 'accepted',
                          reviewNotes: 'Approved for implementation'
                        })}
                        className="flex items-center gap-1"
                      >
                        <ThumbsUp className="h-3 w-3" />
                        Accept
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => reviewSuggestionMutation.mutate({
                          suggestionId: suggestion.suggestionId,
                          status: 'rejected',
                          reviewNotes: 'Not suitable for this case'
                        })}
                        className="flex items-center gap-1"
                      >
                        <ThumbsDown className="h-3 w-3" />
                        Reject
                      </Button>
                    </div>
                  )}

                  {suggestion.status === 'accepted' && (
                    <Button
                      size="sm"
                      onClick={() => implementSuggestionMutation.mutate({
                        suggestionId: suggestion.suggestionId,
                        implementationNotes: 'Implemented as suggested'
                      })}
                      className="mt-4"
                    >
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Implement
                    </Button>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default AISuggestions;
