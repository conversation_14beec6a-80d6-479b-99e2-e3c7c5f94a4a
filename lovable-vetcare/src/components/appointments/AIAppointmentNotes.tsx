import React, { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  Brain, 
  FileText, 
  Wand2, 
  Download, 
  Copy, 
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { api } from '@/services/api';

interface AIAppointmentNotesProps {
  appointmentId: number;
  appointmentData?: any;
}

interface GeneratedNotesData {
  appointmentId: number;
  aiGeneratedNotes: string;
  generatedAt: string;
  sourceData: {
    categories: Array<{
      categoryName: string;
      notes: string;
      status: string;
      staffAssigned?: string;
    }>;
    services: Array<{
      serviceName: string;
      categoryName: string;
      notes: string;
      status: string;
      performedBy: string;
      recordedBy: string;
      price: number;
      currency: string;
    }>;
    generalNotes: string;
    recommendations: string;
  };
}

const AIAppointmentNotes: React.FC<AIAppointmentNotesProps> = ({ 
  appointmentId, 
  appointmentData 
}) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [generatedNotes, setGeneratedNotes] = useState<string>('');
  const [sourceData, setSourceData] = useState<any>(null);
  const [isGenerating, setIsGenerating] = useState(false);

  // Generate AI appointment notes mutation
  const generateNotesMutation = useMutation({
    mutationFn: async () => {
      const response = await api.post(`/appointments/${appointmentId}/generate-ai-notes`, {
        context: 'comprehensive_appointment_summary'
      });
      return response.data;
    },
    onSuccess: (data: { data: GeneratedNotesData }) => {
      setGeneratedNotes(data.data.aiGeneratedNotes);
      setSourceData(data.data.sourceData);
      setIsGenerating(false);
      queryClient.invalidateQueries({ queryKey: ['appointment', appointmentId] });
      toast({
        title: "Success",
        description: "AI appointment notes generated successfully",
      });
    },
    onError: (error: any) => {
      setIsGenerating(false);
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to generate AI notes",
        variant: "destructive",
      });
    }
  });

  const handleGenerateNotes = () => {
    setIsGenerating(true);
    generateNotesMutation.mutate();
  };

  const handleCopyNotes = async () => {
    if (generatedNotes) {
      try {
        await navigator.clipboard.writeText(generatedNotes);
        toast({
          title: "Copied",
          description: "Notes copied to clipboard",
        });
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to copy notes",
          variant: "destructive",
        });
      }
    }
  };

  const handleDownloadNotes = () => {
    if (generatedNotes) {
      const blob = new Blob([generatedNotes], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `appointment-${appointmentId}-notes.txt`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  const getSourceDataSummary = () => {
    if (!sourceData) return null;

    const totalCategories = sourceData.categories?.length || 0;
    const totalServices = sourceData.services?.length || 0;
    const hasGeneralNotes = !!sourceData.generalNotes;
    const hasRecommendations = !!sourceData.recommendations;

    return {
      totalCategories,
      totalServices,
      hasGeneralNotes,
      hasRecommendations
    };
  };

  const summary = getSourceDataSummary();

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Brain className="h-5 w-5 text-blue-600" />
          AI Appointment Summary
        </CardTitle>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Generate a comprehensive appointment summary using AI to consolidate all service notes, 
          category observations, and recommendations into a professional medical record.
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Source Data Summary */}
        {summary && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{summary.totalCategories}</div>
              <div className="text-xs text-gray-600 dark:text-gray-400">Categories</div>
            </div>
            <div className="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{summary.totalServices}</div>
              <div className="text-xs text-gray-600 dark:text-gray-400">Services</div>
            </div>
            <div className="text-center p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">
                {summary.hasGeneralNotes ? '✓' : '—'}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400">General Notes</div>
            </div>
            <div className="text-center p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">
                {summary.hasRecommendations ? '✓' : '—'}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400">Recommendations</div>
            </div>
          </div>
        )}

        {/* Generate Button */}
        <div className="flex justify-center">
          <Button
            onClick={handleGenerateNotes}
            disabled={isGenerating || generateNotesMutation.isPending}
            className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3"
            size="lg"
          >
            {isGenerating ? (
              <>
                <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                Generating AI Summary...
              </>
            ) : (
              <>
                <Wand2 className="h-5 w-5 mr-2" />
                Generate AI Appointment Summary
              </>
            )}
          </Button>
        </div>

        {/* Generated Notes Display */}
        {generatedNotes && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <span className="font-medium text-green-700 dark:text-green-400">
                  AI Summary Generated
                </span>
                <Badge variant="outline" className="text-xs">
                  {new Date().toLocaleString()}
                </Badge>
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCopyNotes}
                  className="flex items-center gap-2"
                >
                  <Copy className="h-4 w-4" />
                  Copy
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleDownloadNotes}
                  className="flex items-center gap-2"
                >
                  <Download className="h-4 w-4" />
                  Download
                </Button>
              </div>
            </div>

            <div className="border rounded-lg p-4 bg-gray-50 dark:bg-gray-900">
              <Textarea
                value={generatedNotes}
                readOnly
                className="min-h-[400px] resize-none border-0 bg-transparent p-0 focus:ring-0"
                style={{ fontFamily: 'monospace', fontSize: '14px', lineHeight: '1.5' }}
              />
            </div>

            {/* Source Data Details */}
            {sourceData && (
              <div className="mt-6 p-4 border rounded-lg bg-blue-50 dark:bg-blue-900/20">
                <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2 flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  Source Data Summary
                </h4>
                <div className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                  <p>• {sourceData.categories?.length || 0} category notes processed</p>
                  <p>• {sourceData.services?.length || 0} service notes processed</p>
                  {sourceData.generalNotes && <p>• General appointment notes included</p>}
                  {sourceData.recommendations && <p>• Recommendations included</p>}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Help Text */}
        {!generatedNotes && (
          <div className="text-center p-6 bg-gray-50 dark:bg-gray-900 rounded-lg">
            <AlertCircle className="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <p className="text-gray-600 dark:text-gray-400 text-sm">
              Click the button above to generate a comprehensive AI summary of this appointment.
              The summary will include all service notes, category observations, and recommendations
              in a professional medical record format.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default AIAppointmentNotes;
