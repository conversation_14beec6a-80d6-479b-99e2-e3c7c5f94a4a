import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { User, Search, Stethoscope } from 'lucide-react';

interface StaffMember {
  staffId: number;
  firstName: string;
  lastName: string;
  jobTitle: string;
  specialization?: string;
}

interface StaffSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (staffId: number, staffName: string) => void;
  staff: StaffMember[];
  title: string;
  currentSelection?: number;
}

const StaffSelectionModal: React.FC<StaffSelectionModalProps> = ({
  isOpen,
  onClose,
  onSelect,
  staff,
  title,
  currentSelection
}) => {
  const [searchTerm, setSearchTerm] = useState('');

  const filteredStaff = staff.filter(member =>
    `${member.firstName} ${member.lastName}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
    member.jobTitle.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSelect = (member: StaffMember) => {
    onSelect(member.staffId, `${member.firstName} ${member.lastName}`);
    onClose();
  };

  const handleClearSelection = () => {
    onSelect(0, '');
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            {title}
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search staff..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Clear Selection Button */}
          {currentSelection && (
            <Button
              variant="outline"
              onClick={handleClearSelection}
              className="w-full text-red-600 border-red-200 hover:bg-red-50"
            >
              Clear Selection
            </Button>
          )}

          {/* Staff List */}
          <div className="max-h-64 overflow-y-auto space-y-2">
            {filteredStaff.length > 0 ? (
              filteredStaff.map((member) => (
                <div
                  key={member.staffId}
                  className={`p-3 border rounded-lg cursor-pointer transition-all hover:bg-gray-50 ${
                    currentSelection === member.staffId
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200'
                  }`}
                  onClick={() => handleSelect(member)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-blue-100 rounded-full">
                        <Stethoscope className="h-4 w-4 text-blue-600" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">
                          {member.firstName} {member.lastName}
                        </p>
                        <p className="text-sm text-gray-600">{member.jobTitle}</p>
                      </div>
                    </div>
                    {member.specialization && (
                      <Badge variant="outline" className="text-xs">
                        {member.specialization}
                      </Badge>
                    )}
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8 text-gray-500">
                <User className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                <p>No staff members found</p>
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default StaffSelectionModal;
