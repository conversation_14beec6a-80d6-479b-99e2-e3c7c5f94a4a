import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import {
  Clock,
  DollarSign,
  User,
  Calendar,
  MapPin,
  Phone,
  Mail,
  PawPrint,
  AlertCircle,
  CheckCircle,
  XCircle,
  Pause,
  FileText,
  Edit,
  Eye,
  Receipt,
  Plus
} from 'lucide-react';
import { format } from 'date-fns';
import { getInvoiceByAppointment, generateInvoiceFromAppointment } from '@/services/invoices';

interface AppointmentSummaryProps {
  appointment: {
    appointmentId: number;
    clientName: string;
    clientPhone?: string;
    clientEmail?: string;
    petName: string;
    petSpecies?: string;
    petBreed?: string;
    appointmentDate: string | Date;
    estimatedDuration: number;
    status: string;
    priority: string;
    staffInChargeName?: string;
    clinicName?: string;
    generalNotes?: string;
    recommendations?: string;
    appointmentCategories?: Array<{
      appointmentCategoryId: number;
      categoryName: string;
      staffAssigned?: number;
      staffAssignedName?: string;
      estimatedDuration: number;
      categoryCharge?: number;
      categoryDiscount?: number;
      categoryDiscountPercentage?: number;
      categoryServices: Array<{
        categoryServiceId: number;
        serviceName: string;
        price: number;
        currency: string;
        notes?: string;
        status?: string;
        isCompleted?: boolean;
        quantity?: number;
        unitPrice?: number;
        serviceDiscount?: number;
      }>;
      categoryNotes?: string;
      categoryStatus?: string;
      isCompleted?: boolean;
    }>;
  };
  showPricing?: boolean;
  compact?: boolean;
}

const AppointmentSummary: React.FC<AppointmentSummaryProps> = ({
  appointment,
  showPricing = true,
  compact = false
}) => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [isGeneratingInvoice, setIsGeneratingInvoice] = useState(false);

  // Check for existing invoice
  const { data: invoiceData, isLoading: isLoadingInvoice } = useQuery({
    queryKey: ['invoice', 'appointment', appointment.appointmentId],
    queryFn: async () => {
      try {
        const response = await getInvoiceByAppointment(appointment.appointmentId);
        return response.success ? response.data : null;
      } catch (error: any) {
        if (error.status === 404) {
          return null; // No invoice exists
        }
        throw error;
      }
    },
    enabled: !!appointment.appointmentId
  });

  // Calculate category total with discounts
  const calculateCategoryTotal = (category: any) => {
    const servicesTotal = category.categoryServices.reduce((total: number, service: any) => {
      const servicePrice = service.unitPrice ? (service.unitPrice * (service.quantity || 1)) : service.price;
      const serviceDiscount = service.serviceDiscount || 0;
      return total + (servicePrice - serviceDiscount);
    }, 0);

    const categoryCharge = category.categoryCharge || 0;
    const categoryDiscount = category.categoryDiscount || 0;

    return servicesTotal + categoryCharge - categoryDiscount;
  };

  // Calculate total pricing with discounts
  const calculateTotalPrice = () => {
    if (!appointment.appointmentCategories) return 0;

    return appointment.appointmentCategories.reduce((total, category) => {
      return total + calculateCategoryTotal(category);
    }, 0);
  };

  const totalPrice = calculateTotalPrice();
  const currency = appointment.appointmentCategories?.[0]?.categoryServices?.[0]?.currency || 'KES';

  // Generate invoice mutation
  const generateInvoiceMutation = useMutation({
    mutationFn: async () => {
      setIsGeneratingInvoice(true);
      const response = await generateInvoiceFromAppointment(appointment.appointmentId);
      return response.data;
    },
    onSuccess: (data) => {
      setIsGeneratingInvoice(false);
      toast({
        title: "Invoice Generated",
        description: "Invoice has been generated successfully.",
      });
      queryClient.invalidateQueries({ queryKey: ['invoice', 'appointment', appointment.appointmentId] });
      navigate(`/invoices/appointment/${appointment.appointmentId}`);
    },
    onError: (error: any) => {
      setIsGeneratingInvoice(false);
      toast({
        title: "Error",
        description: error.message || "Failed to generate invoice.",
        variant: "destructive"
      });
    }
  });

  // Get status color and icon
  const getStatusDisplay = (status: string) => {
    switch (status.toLowerCase()) {
      case 'scheduled':
        return { color: 'bg-blue-100 text-blue-800', icon: <Calendar className="h-3 w-3" /> };
      case 'in_progress':
        return { color: 'bg-yellow-100 text-yellow-800', icon: <Pause className="h-3 w-3" /> };
      case 'completed':
        return { color: 'bg-green-100 text-green-800', icon: <CheckCircle className="h-3 w-3" /> };
      case 'cancelled':
        return { color: 'bg-red-100 text-red-800', icon: <XCircle className="h-3 w-3" /> };
      default:
        return { color: 'bg-gray-100 text-gray-800', icon: <AlertCircle className="h-3 w-3" /> };
    }
  };

  // Get priority color
  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'low':
        return 'bg-green-100 text-green-800';
      case 'normal':
        return 'bg-blue-100 text-blue-800';
      case 'high':
        return 'bg-orange-100 text-orange-800';
      case 'urgent':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const statusDisplay = getStatusDisplay(appointment.status);

  if (compact) {
    return (
      <Card className="w-full">
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              <Badge variant="outline" className={statusDisplay.color}>
                {statusDisplay.icon}
                <span className="ml-1">{appointment.status}</span>
              </Badge>
              <Badge variant="outline" className={getPriorityColor(appointment.priority)}>
                {appointment.priority}
              </Badge>
            </div>
            <span className="text-sm text-gray-500">#{appointment.appointmentId}</span>
          </div>
          
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <p className="font-medium">{appointment.clientName}</p>
              <p className="text-gray-600">{appointment.petName}</p>
            </div>
            <div className="text-right">
              <p className="font-medium">{format(new Date(appointment.appointmentDate), 'MMM dd, yyyy')}</p>
              <p className="text-gray-600">{format(new Date(appointment.appointmentDate), 'h:mm a')}</p>
            </div>
          </div>

          {showPricing && totalPrice > 0 && (
            <div className="mt-3 pt-3 border-t">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Total Estimate:</span>
                <span className="font-semibold text-green-600">{currency} {totalPrice.toLocaleString()}</span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Appointment #{appointment.appointmentId}
          </CardTitle>
          <div className="flex items-center gap-2">
            <Badge className={statusDisplay.color}>
              {statusDisplay.icon}
              <span className="ml-1">{appointment.status}</span>
            </Badge>
            <Badge variant="outline" className={getPriorityColor(appointment.priority)}>
              {appointment.priority} Priority
            </Badge>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Client and Pet Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-3">
            <h4 className="font-semibold flex items-center gap-2">
              <User className="h-4 w-4" />
              Client Information
            </h4>
            <div className="space-y-1 text-sm">
              <p className="font-medium">{appointment.clientName}</p>
              {appointment.clientPhone && (
                <p className="flex items-center gap-2 text-gray-600">
                  <Phone className="h-3 w-3" />
                  {appointment.clientPhone}
                </p>
              )}
              {appointment.clientEmail && (
                <p className="flex items-center gap-2 text-gray-600">
                  <Mail className="h-3 w-3" />
                  {appointment.clientEmail}
                </p>
              )}
            </div>
          </div>

          <div className="space-y-3">
            <h4 className="font-semibold flex items-center gap-2">
              <PawPrint className="h-4 w-4" />
              Pet Information
            </h4>
            <div className="space-y-1 text-sm">
              <p className="font-medium">{appointment.petName}</p>
              {appointment.petSpecies && (
                <p className="text-gray-600">Species: {appointment.petSpecies}</p>
              )}
              {appointment.petBreed && (
                <p className="text-gray-600">Breed: {appointment.petBreed}</p>
              )}
            </div>
          </div>
        </div>

        <Separator />

        {/* Appointment Details */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="space-y-1">
            <h4 className="font-semibold flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              Date & Time
            </h4>
            <p className="text-sm">{format(new Date(appointment.appointmentDate), 'EEEE, MMMM dd, yyyy')}</p>
            <p className="text-sm text-gray-600">{format(new Date(appointment.appointmentDate), 'h:mm a')}</p>
          </div>

          <div className="space-y-1">
            <h4 className="font-semibold flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Duration
            </h4>
            <p className="text-sm">{appointment.estimatedDuration} minutes</p>
          </div>

          <div className="space-y-1">
            <h4 className="font-semibold flex items-center gap-2">
              <User className="h-4 w-4" />
              Staff in Charge
            </h4>
            <p className="text-sm">{appointment.staffInChargeName || 'Not assigned'}</p>
            {appointment.clinicName && (
              <p className="text-sm text-gray-600 flex items-center gap-1">
                <MapPin className="h-3 w-3" />
                {appointment.clinicName}
              </p>
            )}
          </div>
        </div>

        {/* Categories and Services */}
        {appointment.appointmentCategories && appointment.appointmentCategories.length > 0 && (
          <>
            <Separator />
            <div className="space-y-4">
              <h4 className="font-semibold">Categories & Services</h4>
              {appointment.appointmentCategories.map((category) => {
                const categoryTotal = calculateCategoryTotal(category);
                return (
                <Card key={category.appointmentCategoryId} className="border-l-4 border-l-blue-500">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h5 className="font-medium">{category.categoryName}</h5>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-xs">
                          <Clock className="h-3 w-3 mr-1" />
                          {category.estimatedDuration}min
                        </Badge>
                        {category.staffAssignedName && (
                          <Badge variant="outline" className="text-xs">
                            <User className="h-3 w-3 mr-1" />
                            {category.staffAssignedName}
                          </Badge>
                        )}
                        {showPricing && categoryTotal > 0 && (
                          <Badge variant="secondary" className="text-xs font-semibold">
                            {currency} {categoryTotal.toLocaleString()}
                          </Badge>
                        )}
                      </div>
                    </div>

                    {/* Category charges and discounts */}
                    {showPricing && (category.categoryCharge || category.categoryDiscount) && (
                      <div className="mb-3 p-2 bg-gray-50 rounded text-xs">
                        {category.categoryCharge && (
                          <div className="flex justify-between">
                            <span>Category Charge:</span>
                            <span>{currency} {category.categoryCharge.toLocaleString()}</span>
                          </div>
                        )}
                        {category.categoryDiscount && (
                          <div className="flex justify-between text-red-600">
                            <span>Category Discount:</span>
                            <span>-{currency} {category.categoryDiscount.toLocaleString()}</span>
                          </div>
                        )}
                      </div>
                    )}

                    {category.categoryServices.length > 0 && (
                      <div className="space-y-2">
                        {category.categoryServices.map((service) => {
                          const servicePrice = service.unitPrice ? (service.unitPrice * (service.quantity || 1)) : service.price;
                          const serviceDiscount = service.serviceDiscount || 0;
                          const finalServicePrice = servicePrice - serviceDiscount;

                          return (
                          <div key={service.categoryServiceId} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                            <div className="flex-1">
                              <p className="text-sm font-medium">{service.serviceName}</p>
                              {service.quantity && service.quantity > 1 && (
                                <p className="text-xs text-gray-600">Quantity: {service.quantity}</p>
                              )}
                              {service.notes && (
                                <p className="text-xs text-gray-600">{service.notes}</p>
                              )}
                            </div>
                            {showPricing && (
                              <div className="text-right">
                                <div className="space-y-1">
                                  {service.unitPrice && service.quantity ? (
                                    <p className="text-xs text-gray-500">
                                      {service.currency} {service.unitPrice.toLocaleString()} × {service.quantity}
                                    </p>
                                  ) : null}
                                  {serviceDiscount > 0 && (
                                    <p className="text-xs text-red-600 line-through">
                                      {service.currency} {servicePrice.toLocaleString()}
                                    </p>
                                  )}
                                  <p className="text-sm font-semibold text-green-600">
                                    {service.currency} {finalServicePrice.toLocaleString()}
                                  </p>
                                  {serviceDiscount > 0 && (
                                    <p className="text-xs text-red-600">
                                      Discount: -{service.currency} {serviceDiscount.toLocaleString()}
                                    </p>
                                  )}
                                </div>
                                {service.status && (
                                  <Badge variant="outline" className="text-xs mt-1">
                                    {service.status}
                                  </Badge>
                                )}
                              </div>
                            )}
                          </div>
                        )})}
                      </div>
                    )}

                    {category.categoryNotes && (
                      <div className="mt-3 p-2 bg-blue-50 rounded">
                        <p className="text-xs text-blue-800">{category.categoryNotes}</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
                );
              })}
            </div>
          </>
        )}

        {/* Pricing Summary */}
        {showPricing && totalPrice > 0 && (
          <>
            <Separator />
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="font-semibold flex items-center gap-2">
                  <DollarSign className="h-4 w-4" />
                  Pricing Summary
                </h4>
                {/* Invoice Status and Actions */}
                <div className="flex items-center gap-2">
                  {isLoadingInvoice ? (
                    <Badge variant="outline">Checking invoice...</Badge>
                  ) : invoiceData ? (
                    <>
                      <Badge variant="default" className="bg-green-100 text-green-800">
                        <Receipt className="h-3 w-3 mr-1" />
                        Invoice #{invoiceData.invoiceNumber}
                      </Badge>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => navigate(`/invoices/appointment/${appointment.appointmentId}`)}
                      >
                        <Eye className="h-3 w-3 mr-1" />
                        View Invoice
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => navigate(`/invoices/appointment/${appointment.appointmentId}`)}
                      >
                        <Edit className="h-3 w-3 mr-1" />
                        Edit Charges
                      </Button>
                    </>
                  ) : (
                    <Button
                      size="sm"
                      onClick={() => generateInvoiceMutation.mutate()}
                      disabled={isGeneratingInvoice}
                    >
                      <Plus className="h-3 w-3 mr-1" />
                      {isGeneratingInvoice ? 'Generating...' : 'Generate Invoice'}
                    </Button>
                  )}
                </div>
              </div>

              {/* Category Breakdown */}
              {appointment.appointmentCategories && appointment.appointmentCategories.length > 0 && (
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h5 className="font-medium mb-3">Category Breakdown:</h5>
                  <div className="space-y-2">
                    {appointment.appointmentCategories.map((category) => {
                      const categoryTotal = calculateCategoryTotal(category);
                      return (
                        <div key={category.appointmentCategoryId} className="flex justify-between items-center text-sm">
                          <span>{category.categoryName}</span>
                          <span className="font-medium">{currency} {categoryTotal.toLocaleString()}</span>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}

              {/* Total */}
              <div className="bg-green-50 p-4 rounded-lg">
                <div className="flex justify-between items-center">
                  <span className="text-lg font-semibold">Total Estimate:</span>
                  <span className="text-2xl font-bold text-green-600">
                    {currency} {totalPrice.toLocaleString()}
                  </span>
                </div>
                {invoiceData && (
                  <div className="mt-2 pt-2 border-t border-green-200">
                    <div className="flex justify-between items-center text-sm">
                      <span>Invoice Status:</span>
                      <Badge variant={invoiceData.paymentStatus === 'paid' ? 'default' : 'secondary'}>
                        {invoiceData.paymentStatus}
                      </Badge>
                    </div>
                    {invoiceData.amountDue > 0 && (
                      <div className="flex justify-between items-center text-sm mt-1">
                        <span>Amount Due:</span>
                        <span className="font-medium text-red-600">
                          {currency} {invoiceData.amountDue.toLocaleString()}
                        </span>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </>
        )}

        {/* Notes */}
        {(appointment.generalNotes || appointment.recommendations) && (
          <>
            <Separator />
            <div className="space-y-3">
              <h4 className="font-semibold">Notes</h4>
              {appointment.generalNotes && (
                <div>
                  <h5 className="text-sm font-medium text-gray-700 mb-1">General Notes:</h5>
                  <p className="text-sm text-gray-600 bg-gray-50 p-3 rounded">{appointment.generalNotes}</p>
                </div>
              )}
              {appointment.recommendations && (
                <div>
                  <h5 className="text-sm font-medium text-gray-700 mb-1">Recommendations:</h5>
                  <p className="text-sm text-gray-600 bg-blue-50 p-3 rounded">{appointment.recommendations}</p>
                </div>
              )}
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default AppointmentSummary;
