import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import {
  ChevronDown,
  ChevronRight,
  CheckCircle,
  Clock,
  DollarSign,
  User,
  Wrench,
  GraduationCap,
  Plus,
  Minus,
  Percent
} from 'lucide-react';
import { getAppointmentCategories, getCategoryServices, getStaffForCategory } from '@/services/appointmentCategories';
import { useAuth } from '@/store';
import type { AppointmentCategory, CategoryService } from '@/services/appointmentCategories';

interface SelectedCategory {
  appointmentCategoryId: number;
  categoryName: string;
  assignedStaff?: number;
  assignedStaffName?: string;
  estimatedDuration: number;
  categoryServices: Array<{
    categoryServiceId: number;
    serviceName: string;
    price: number;
    currency: string;
    notes?: string;
  }>;
  categoryNotes?: string;
}

interface AppointmentCategorySelectorProps {
  selectedCategories: SelectedCategory[];
  onCategoriesChange: (categories: SelectedCategory[]) => void;
  disabled?: boolean;
  mode?: 'full' | 'categories-only'; // New prop to control behavior
}

const AppointmentCategorySelector: React.FC<AppointmentCategorySelectorProps> = ({
  selectedCategories,
  onCategoriesChange,
  disabled = false,
  mode = 'full'
}) => {
  const { clinic } = useAuth();
  const [expandedCategories, setExpandedCategories] = useState<Set<number>>(new Set());

  // Fetch appointment categories
  const { data: categoriesResponse, isLoading: isLoadingCategories } = useQuery({
    queryKey: ['appointmentCategories', clinic?.clinicId],
    queryFn: () => getAppointmentCategories({
      clinicId: clinic?.clinicId,
      isActive: true
    }),
    enabled: !!clinic?.clinicId
  });

  const categories = categoriesResponse?.data || [];

  // Toggle category expansion
  const toggleCategoryExpansion = (categoryId: number) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(categoryId)) {
      newExpanded.delete(categoryId);
    } else {
      newExpanded.add(categoryId);
    }
    setExpandedCategories(newExpanded);
  };

  // Check if category is selected
  const isCategorySelected = (categoryId: number) => {
    return selectedCategories.some(cat => cat.appointmentCategoryId === categoryId);
  };

  // Toggle category (add/remove)
  const toggleCategory = (category: AppointmentCategory) => {
    if (isCategorySelected(category.appointmentCategoryId)) {
      // Remove category
      removeCategory(category.appointmentCategoryId);
    } else {
      // Add category
      const newCategory: SelectedCategory = {
        appointmentCategoryId: category.appointmentCategoryId,
        categoryName: category.name,
        estimatedDuration: category.estimatedDuration,
        categoryServices: [],
        categoryNotes: ''
      };

      onCategoriesChange([...selectedCategories, newCategory]);
      setExpandedCategories(prev => new Set([...prev, category.appointmentCategoryId]));
    }
  };

  // Remove category
  const removeCategory = (categoryId: number) => {
    const updatedCategories = selectedCategories.filter(
      cat => cat.appointmentCategoryId !== categoryId
    );
    onCategoriesChange(updatedCategories);
    setExpandedCategories(prev => {
      const newSet = new Set(prev);
      newSet.delete(categoryId);
      return newSet;
    });
  };

  // Update category
  const updateCategory = (categoryId: number, updates: Partial<SelectedCategory>) => {
    const updatedCategories = selectedCategories.map(cat =>
      cat.appointmentCategoryId === categoryId ? { ...cat, ...updates } : cat
    );
    onCategoriesChange(updatedCategories);
  };

  // Get category icon component
  const getCategoryIcon = (iconName?: string) => {
    const iconProps = { className: "h-4 w-4" };

    switch (iconName) {
      case 'clock': return <Clock {...iconProps} />;
      case 'user': return <User {...iconProps} />;
      case 'wrench': return <Wrench {...iconProps} />;
      case 'graduation-cap': return <GraduationCap {...iconProps} />;
      default: return <Clock {...iconProps} />;
    }
  };

  if (isLoadingCategories) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Loading Categories...</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Add Categories Section - Simplified */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Plus className="h-5 w-5" />
            Appointment Categories & Services
          </CardTitle>
          <p className="text-sm text-gray-600">Select categories and services for this appointment</p>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <Label className="text-sm font-medium">Add Categories</Label>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
              {categories.map((category) => (
                <Button
                  key={category.appointmentCategoryId}
                  variant={isCategorySelected(category.appointmentCategoryId) ? "default" : "outline"}
                  size="sm"
                  className={`h-auto p-3 flex flex-col items-center gap-1 ${
                    isCategorySelected(category.appointmentCategoryId)
                      ? 'bg-green-100 border-green-500 text-green-800'
                      : 'hover:bg-gray-50'
                  }`}
                  onClick={() => !disabled && toggleCategory(category)}
                  disabled={disabled}
                >
                  {getCategoryIcon(category.icon)}
                  <span className="text-xs font-medium text-center">{category.name}</span>
                  <div className="flex items-center gap-1 text-xs text-gray-500">
                    <Clock className="h-3 w-3" />
                    <span>{category.estimatedDuration}min</span>
                  </div>
                  {(category.defaultCharge > 0 || category.defaultDiscountPercentage > 0) && (
                    <div className="flex flex-col items-center gap-1 text-xs">
                      {category.defaultCharge > 0 && (
                        <div className="flex items-center gap-1 text-blue-600">
                          <DollarSign className="h-3 w-3" />
                          <span>{category.currency} {category.defaultCharge}</span>
                        </div>
                      )}
                      {category.defaultDiscountPercentage > 0 && (
                        <div className="flex items-center gap-1 text-orange-600">
                          <Percent className="h-3 w-3" />
                          <span>{category.defaultDiscountPercentage}% off</span>
                        </div>
                      )}
                    </div>
                  )}
                  {isCategorySelected(category.appointmentCategoryId) && (
                    <CheckCircle className="h-3 w-3 text-green-600" />
                  )}
                </Button>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Selected Categories - Only show in full mode */}
      {mode === 'full' && selectedCategories.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              Selected Categories ({selectedCategories.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {selectedCategories.map((selectedCategory) => {
                const category = categories.find(c => c.appointmentCategoryId === selectedCategory.appointmentCategoryId);
                const isExpanded = expandedCategories.has(selectedCategory.appointmentCategoryId);

                return (
                  <div key={selectedCategory.appointmentCategoryId} className="border rounded-lg">
                    <Collapsible
                      open={isExpanded}
                      onOpenChange={() => toggleCategoryExpansion(selectedCategory.appointmentCategoryId)}
                    >
                      <CollapsibleTrigger asChild>
                        <div className="flex items-center justify-between p-3 cursor-pointer hover:bg-gray-50">
                          <div className="flex items-center gap-3">
                            {isExpanded ? (
                              <ChevronDown className="h-4 w-4" />
                            ) : (
                              <ChevronRight className="h-4 w-4" />
                            )}
                            {getCategoryIcon(category?.icon)}
                            <div>
                              <span className="font-medium">{selectedCategory.categoryName}</span>
                              <div className="flex items-center gap-2 text-sm text-gray-500">
                                <Clock className="h-3 w-3" />
                                <span>{selectedCategory.estimatedDuration}min</span>
                                <span>•</span>
                                <span>{selectedCategory.categoryServices.length} services</span>
                              </div>
                            </div>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              removeCategory(selectedCategory.appointmentCategoryId);
                            }}
                            disabled={disabled}
                          >
                            <Minus className="h-4 w-4" />
                          </Button>
                        </div>
                      </CollapsibleTrigger>
                      <CollapsibleContent>
                        <div className="px-3 pb-3">
                          <Separator className="mb-3" />
                          <CategoryServicesSelector
                            categoryId={selectedCategory.appointmentCategoryId}
                            selectedServices={selectedCategory.categoryServices}
                            onServicesChange={(services) =>
                              updateCategory(selectedCategory.appointmentCategoryId, { categoryServices: services })
                            }
                            disabled={disabled}
                          />
                        </div>
                      </CollapsibleContent>
                    </Collapsible>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Simple selected count for categories-only mode */}
      {mode === 'categories-only' && selectedCategories.length > 0 && (
        <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 text-green-800">
              <CheckCircle className="h-4 w-4" />
              <span className="text-sm font-medium">
                {selectedCategories.length} categor{selectedCategories.length === 1 ? 'y' : 'ies'} selected
              </span>
            </div>
            <div className="flex items-center gap-1 text-green-800">
              <Clock className="h-4 w-4" />
              <span className="text-sm font-medium">
                {selectedCategories.reduce((total, cat) => total + cat.estimatedDuration, 0)} minutes
              </span>
            </div>
          </div>
          <div className="text-xs text-green-700 mt-1">
            {selectedCategories.map(cat => cat.categoryName).join(', ')}
          </div>
        </div>
      )}
    </div>
  );
};

// Category Services Selector Component
interface CategoryServicesSelectorProps {
  categoryId: number;
  selectedServices: Array<{
    categoryServiceId: number;
    serviceName: string;
    price: number;
    currency: string;
    notes?: string;
  }>;
  onServicesChange: (services: Array<{
    categoryServiceId: number;
    serviceName: string;
    price: number;
    currency: string;
    notes?: string;
  }>) => void;
  disabled?: boolean;
}

const CategoryServicesSelector: React.FC<CategoryServicesSelectorProps> = ({
  categoryId,
  selectedServices,
  onServicesChange,
  disabled = false
}) => {
  const { clinic } = useAuth();

  // Fetch services for this category
  const { data: servicesResponse, isLoading } = useQuery({
    queryKey: ['categoryServices', categoryId, clinic?.clinicId],
    queryFn: () => getCategoryServices(categoryId, {
      clinicId: clinic?.clinicId,
      isActive: true
    }),
    enabled: !!categoryId && !!clinic?.clinicId
  });

  const availableServices = servicesResponse?.data || [];

  const toggleService = (service: CategoryService) => {
    const isSelected = selectedServices.some(s => s.categoryServiceId === service.categoryServiceId);

    if (isSelected) {
      // Remove service
      const updatedServices = selectedServices.filter(s => s.categoryServiceId !== service.categoryServiceId);
      onServicesChange(updatedServices);
    } else {
      // Add service
      const newService = {
        categoryServiceId: service.categoryServiceId,
        serviceName: service.categoryServiceName,
        price: service.defaultPrice,
        currency: service.currency,
        notes: ''
      };
      onServicesChange([...selectedServices, newService]);
    }
  };

  if (isLoading) {
    return <div className="text-sm text-gray-500">Loading services...</div>;
  }

  if (availableServices.length === 0) {
    return <div className="text-sm text-gray-500">No services available for this category.</div>;
  }

  return (
    <div className="space-y-2">
      <Label className="text-sm font-medium">Services</Label>
      <div className="grid grid-cols-1 gap-2">
        {availableServices.map((service) => {
          const isSelected = selectedServices.some(s => s.categoryServiceId === service.categoryServiceId);

          return (
            <div
              key={service.categoryServiceId}
              className="flex items-center space-x-3 p-2 border rounded hover:bg-gray-50"
            >
              <Checkbox
                id={`service-${service.categoryServiceId}`}
                checked={isSelected}
                onCheckedChange={() => !disabled && toggleService(service)}
                disabled={disabled}
              />
              <div className="flex-1">
                <Label
                  htmlFor={`service-${service.categoryServiceId}`}
                  className="text-sm font-medium cursor-pointer"
                >
                  {service.categoryServiceName}
                </Label>
                {service.description && (
                  <p className="text-xs text-gray-600">{service.description}</p>
                )}
                <div className="flex items-center gap-2 text-xs text-gray-500">
                  <DollarSign className="h-3 w-3" />
                  <span>{service.currency} {service.defaultPrice}</span>
                  <Clock className="h-3 w-3" />
                  <span>{service.estimatedDuration}min</span>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default AppointmentCategorySelector;
