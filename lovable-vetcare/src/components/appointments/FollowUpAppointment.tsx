import React, { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { format, addDays, addWeeks, addMonths } from 'date-fns';
import {
  Calendar,
  Clock,
  User,
  AlertCircle,
  CheckCircle,
  Plus,
  CalendarPlus,
  Stethoscope,
  FileText,
  Bell
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { useToast } from '@/hooks/use-toast';
import { createFollowUpAppointment, scheduleFollowUpReminder, FollowUpData, FollowUpReminderData } from '@/services/appointments';
import { Appointment } from '@/store/types';

interface FollowUpAppointmentProps {
  appointment: Appointment;
  onSuccess?: () => void;
}

const followUpTypes = [
  { value: 'routine_checkup', label: 'Routine Checkup', icon: Stethoscope },
  { value: 'medication_review', label: 'Medication Review', icon: FileText },
  { value: 'surgery_followup', label: 'Surgery Follow-up', icon: AlertCircle },
  { value: 'vaccination_reminder', label: 'Vaccination Reminder', icon: Calendar },
  { value: 'treatment_progress', label: 'Treatment Progress', icon: CheckCircle },
  { value: 'lab_results', label: 'Lab Results Review', icon: FileText },
  { value: 'behavioral_assessment', label: 'Behavioral Assessment', icon: User },
  { value: 'dental_checkup', label: 'Dental Checkup', icon: Stethoscope },
  { value: 'emergency_followup', label: 'Emergency Follow-up', icon: AlertCircle },
  { value: 'other', label: 'Other', icon: Calendar }
];

const quickScheduleOptions = [
  { label: '1 Week', value: () => addWeeks(new Date(), 1) },
  { label: '2 Weeks', value: () => addWeeks(new Date(), 2) },
  { label: '1 Month', value: () => addMonths(new Date(), 1) },
  { label: '3 Months', value: () => addMonths(new Date(), 3) },
  { label: '6 Months', value: () => addMonths(new Date(), 6) }
];

export const FollowUpAppointment: React.FC<FollowUpAppointmentProps> = ({
  appointment,
  onSuccess
}) => {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isReminderDialogOpen, setIsReminderDialogOpen] = useState(false);
  const [followUpData, setFollowUpData] = useState<FollowUpData>({
    appointmentDate: format(addWeeks(new Date(), 2), 'yyyy-MM-dd'),
    priority: 'normal',
    type: 'routine_checkup',
    reason: '',
    notes: ''
  });
  const [reminderData, setReminderData] = useState<FollowUpReminderData>({
    followUpDate: format(addWeeks(new Date(), 2), 'yyyy-MM-dd'),
    priority: 'normal',
    instructions: ''
  });

  const { toast } = useToast();
  const queryClient = useQueryClient();

  const createFollowUpMutation = useMutation({
    mutationFn: (data: FollowUpData) => createFollowUpAppointment(appointment.appointmentId, data),
    onSuccess: () => {
      toast({
        title: "Follow-up Appointment Created",
        description: "The follow-up appointment has been scheduled successfully.",
      });
      setIsCreateDialogOpen(false);
      queryClient.invalidateQueries({ queryKey: ['appointments'] });
      onSuccess?.();
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to create follow-up appointment",
        variant: "destructive",
      });
    }
  });

  const scheduleReminderMutation = useMutation({
    mutationFn: (data: FollowUpReminderData) => scheduleFollowUpReminder(appointment.appointmentId, data),
    onSuccess: () => {
      toast({
        title: "Follow-up Reminder Scheduled",
        description: "The follow-up reminder has been set successfully.",
      });
      setIsReminderDialogOpen(false);
      queryClient.invalidateQueries({ queryKey: ['appointments'] });
      onSuccess?.();
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to schedule follow-up reminder",
        variant: "destructive",
      });
    }
  });

  const handleQuickSchedule = (getDate: () => Date) => {
    const date = getDate();
    setFollowUpData(prev => ({
      ...prev,
      appointmentDate: format(date, 'yyyy-MM-dd')
    }));
  };

  const handleCreateFollowUp = () => {
    createFollowUpMutation.mutate(followUpData);
  };

  const handleScheduleReminder = () => {
    scheduleReminderMutation.mutate(reminderData);
  };

  const selectedType = followUpTypes.find(type => type.value === followUpData.type);
  const TypeIcon = selectedType?.icon || Calendar;

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CalendarPlus className="h-5 w-5" />
          Follow-up Management
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Current Follow-up Status */}
        {appointment.followUp?.hasScheduledFollowUps && (
          <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
            <div className="flex items-center gap-2 mb-2">
              <Bell className="h-4 w-4 text-blue-600" />
              <span className="font-medium text-blue-800">Follow-up Scheduled</span>
            </div>
            <p className="text-sm text-blue-700">
              Next follow-up: {appointment.followUp.nextFollowUpDate ? 
                format(new Date(appointment.followUp.nextFollowUpDate), 'PPP') : 'Not specified'}
            </p>
            {appointment.followUp.followUpInstructions && (
              <p className="text-sm text-blue-600 mt-1">
                Instructions: {appointment.followUp.followUpInstructions}
              </p>
            )}
          </div>
        )}

        {/* Follow-up Type Badge */}
        {appointment.followUp?.isFollowUp && (
          <div className="flex items-center gap-2">
            <Badge variant="secondary" className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              Follow-up Appointment
            </Badge>
            <span className="text-sm text-muted-foreground">
              Type: {followUpTypes.find(t => t.value === appointment.followUp?.followUpType)?.label || 'Unknown'}
            </span>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex gap-2">
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button className="flex-1">
                <Plus className="h-4 w-4 mr-2" />
                Create Follow-up
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Create Follow-up Appointment</DialogTitle>
                <DialogDescription>
                  Schedule a follow-up appointment for {appointment.petName}
                </DialogDescription>
              </DialogHeader>

              <div className="space-y-4">
                {/* Quick Schedule Options */}
                <div>
                  <Label className="text-sm font-medium">Quick Schedule</Label>
                  <div className="flex gap-2 mt-1">
                    {quickScheduleOptions.map((option) => (
                      <Button
                        key={option.label}
                        variant="outline"
                        size="sm"
                        onClick={() => handleQuickSchedule(option.value)}
                      >
                        {option.label}
                      </Button>
                    ))}
                  </div>
                </div>

                {/* Date Selection */}
                <div>
                  <Label htmlFor="appointmentDate">Appointment Date</Label>
                  <Input
                    id="appointmentDate"
                    type="date"
                    value={followUpData.appointmentDate}
                    onChange={(e) => setFollowUpData(prev => ({
                      ...prev,
                      appointmentDate: e.target.value
                    }))}
                    min={format(new Date(), 'yyyy-MM-dd')}
                  />
                </div>

                {/* Follow-up Type */}
                <div>
                  <Label>Follow-up Type</Label>
                  <Select
                    value={followUpData.type}
                    onValueChange={(value) => setFollowUpData(prev => ({
                      ...prev,
                      type: value as any
                    }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {followUpTypes.map((type) => {
                        const Icon = type.icon;
                        return (
                          <SelectItem key={type.value} value={type.value}>
                            <div className="flex items-center gap-2">
                              <Icon className="h-4 w-4" />
                              {type.label}
                            </div>
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                </div>

                {/* Priority */}
                <div>
                  <Label>Priority</Label>
                  <Select
                    value={followUpData.priority}
                    onValueChange={(value) => setFollowUpData(prev => ({
                      ...prev,
                      priority: value as any
                    }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Low</SelectItem>
                      <SelectItem value="normal">Normal</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                      <SelectItem value="emergency">Emergency</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Reason */}
                <div>
                  <Label htmlFor="reason">Reason for Follow-up</Label>
                  <Input
                    id="reason"
                    placeholder="e.g., Check healing progress, medication review..."
                    value={followUpData.reason}
                    onChange={(e) => setFollowUpData(prev => ({
                      ...prev,
                      reason: e.target.value
                    }))}
                  />
                </div>

                {/* Notes */}
                <div>
                  <Label htmlFor="notes">Additional Notes</Label>
                  <Textarea
                    id="notes"
                    placeholder="Any additional instructions or notes..."
                    value={followUpData.notes}
                    onChange={(e) => setFollowUpData(prev => ({
                      ...prev,
                      notes: e.target.value
                    }))}
                    rows={3}
                  />
                </div>
              </div>

              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => setIsCreateDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleCreateFollowUp}
                  disabled={createFollowUpMutation.isPending}
                >
                  {createFollowUpMutation.isPending ? (
                    <>
                      <Clock className="h-4 w-4 mr-2 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Create Follow-up
                    </>
                  )}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          <Dialog open={isReminderDialogOpen} onOpenChange={setIsReminderDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" className="flex-1">
                <Bell className="h-4 w-4 mr-2" />
                Set Reminder
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Schedule Follow-up Reminder</DialogTitle>
                <DialogDescription>
                  Set a reminder for future follow-up without creating an appointment
                </DialogDescription>
              </DialogHeader>

              <div className="space-y-4">
                <div>
                  <Label htmlFor="followUpDate">Follow-up Date</Label>
                  <Input
                    id="followUpDate"
                    type="date"
                    value={reminderData.followUpDate}
                    onChange={(e) => setReminderData(prev => ({
                      ...prev,
                      followUpDate: e.target.value
                    }))}
                    min={format(new Date(), 'yyyy-MM-dd')}
                  />
                </div>

                <div>
                  <Label>Priority</Label>
                  <Select
                    value={reminderData.priority}
                    onValueChange={(value) => setReminderData(prev => ({
                      ...prev,
                      priority: value as any
                    }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Low</SelectItem>
                      <SelectItem value="normal">Normal</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                      <SelectItem value="urgent">Urgent</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="instructions">Instructions</Label>
                  <Textarea
                    id="instructions"
                    placeholder="Follow-up instructions or notes..."
                    value={reminderData.instructions}
                    onChange={(e) => setReminderData(prev => ({
                      ...prev,
                      instructions: e.target.value
                    }))}
                    rows={3}
                  />
                </div>
              </div>

              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => setIsReminderDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleScheduleReminder}
                  disabled={scheduleReminderMutation.isPending}
                >
                  {scheduleReminderMutation.isPending ? (
                    <>
                      <Clock className="h-4 w-4 mr-2 animate-spin" />
                      Scheduling...
                    </>
                  ) : (
                    <>
                      <Bell className="h-4 w-4 mr-2" />
                      Set Reminder
                    </>
                  )}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </CardContent>
    </Card>
  );
};

export default FollowUpAppointment;
