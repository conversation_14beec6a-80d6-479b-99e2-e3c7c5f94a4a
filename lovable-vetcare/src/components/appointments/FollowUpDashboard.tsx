import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { format, isAfter, isBefore, addDays } from 'date-fns';
import {
  Calendar,
  Clock,
  AlertTriangle,
  CheckCircle,
  User,
  Phone,
  Filter,
  RefreshCw,
  Bell,
  CalendarX,
  TrendingUp
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import LoadingPage from '@/components/common/LoadingPage';
import { getOverdueFollowUps, getUpcomingFollowUps } from '@/services/appointments';
import { Appointment } from '@/store/types';
import { useStore } from '@/store';

interface FollowUpDashboardProps {
  clinicId?: string | number;
}

const priorityColors = {
  low: 'bg-gray-100 text-gray-800',
  normal: 'bg-blue-100 text-blue-800',
  high: 'bg-orange-100 text-orange-800',
  urgent: 'bg-red-100 text-red-800',
  emergency: 'bg-red-100 text-red-800'
};

const statusColors = {
  scheduled: 'bg-blue-100 text-blue-800',
  in_progress: 'bg-yellow-100 text-yellow-800',
  completed: 'bg-green-100 text-green-800',
  cancelled: 'bg-gray-100 text-gray-800',
  no_show: 'bg-red-100 text-red-800'
};

export const FollowUpDashboard: React.FC<FollowUpDashboardProps> = ({ clinicId }) => {
  const [daysOverdue, setDaysOverdue] = useState(0);
  const [daysAhead, setDaysAhead] = useState(7);
  const [searchTerm, setSearchTerm] = useState('');
  const [priorityFilter, setPriorityFilter] = useState<string>('all');

  const { clinic } = useStore();
  const effectiveClinicId = clinicId || clinic?.clinicId;

  const {
    data: overdueData,
    isLoading: isLoadingOverdue,
    refetch: refetchOverdue
  } = useQuery({
    queryKey: ['overdue-followups', effectiveClinicId, daysOverdue],
    queryFn: () => getOverdueFollowUps(effectiveClinicId, daysOverdue),
    enabled: !!effectiveClinicId
  });

  const {
    data: upcomingData,
    isLoading: isLoadingUpcoming,
    refetch: refetchUpcoming
  } = useQuery({
    queryKey: ['upcoming-followups', effectiveClinicId, daysAhead],
    queryFn: () => getUpcomingFollowUps(effectiveClinicId, daysAhead),
    enabled: !!effectiveClinicId
  });

  const overdueAppointments = overdueData?.data?.data || [];
  const upcomingAppointments = upcomingData?.data?.data || [];

  const filteredOverdue = overdueAppointments.filter((appointment: Appointment) => {
    const matchesSearch = !searchTerm || 
      appointment.petName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      appointment.clientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      appointment.clientPhone.includes(searchTerm);
    
    const matchesPriority = priorityFilter === 'all' || 
      appointment.followUp?.followUpPriority === priorityFilter;
    
    return matchesSearch && matchesPriority;
  });

  const filteredUpcoming = upcomingAppointments.filter((appointment: Appointment) => {
    const matchesSearch = !searchTerm || 
      appointment.petName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      appointment.clientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      appointment.clientPhone.includes(searchTerm);
    
    const matchesPriority = priorityFilter === 'all' || 
      appointment.followUp?.followUpPriority === priorityFilter;
    
    return matchesSearch && matchesPriority;
  });

  const handleRefresh = () => {
    refetchOverdue();
    refetchUpcoming();
  };

  const getOverdueDays = (date: string) => {
    const followUpDate = new Date(date);
    const today = new Date();
    const diffTime = today.getTime() - followUpDate.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const getDaysUntil = (date: string) => {
    const followUpDate = new Date(date);
    const today = new Date();
    const diffTime = followUpDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const AppointmentRow: React.FC<{ appointment: Appointment; isOverdue?: boolean }> = ({ 
    appointment, 
    isOverdue = false 
  }) => (
    <TableRow>
      <TableCell>
        <div className="flex flex-col">
          <span className="font-medium">{appointment.petName}</span>
          <span className="text-sm text-muted-foreground">{appointment.petSpecies} • {appointment.petBreed}</span>
        </div>
      </TableCell>
      <TableCell>
        <div className="flex flex-col">
          <span className="font-medium">{appointment.clientName}</span>
          <span className="text-sm text-muted-foreground flex items-center gap-1">
            <Phone className="h-3 w-3" />
            {appointment.clientPhone}
          </span>
        </div>
      </TableCell>
      <TableCell>
        <div className="flex flex-col">
          <span className="font-medium">
            {appointment.followUp?.nextFollowUpDate ? 
              format(new Date(appointment.followUp.nextFollowUpDate), 'MMM dd, yyyy') : 
              'Not specified'
            }
          </span>
          {appointment.followUp?.nextFollowUpDate && (
            <span className={`text-sm ${isOverdue ? 'text-red-600' : 'text-muted-foreground'}`}>
              {isOverdue ? 
                `${getOverdueDays(appointment.followUp.nextFollowUpDate)} days overdue` :
                `In ${getDaysUntil(appointment.followUp.nextFollowUpDate)} days`
              }
            </span>
          )}
        </div>
      </TableCell>
      <TableCell>
        <Badge 
          className={priorityColors[appointment.followUp?.followUpPriority as keyof typeof priorityColors] || priorityColors.normal}
        >
          {appointment.followUp?.followUpPriority || 'normal'}
        </Badge>
      </TableCell>
      <TableCell>
        <Badge 
          className={statusColors[appointment.status as keyof typeof statusColors] || statusColors.scheduled}
        >
          {appointment.status}
        </Badge>
      </TableCell>
      <TableCell>
        <span className="text-sm">{appointment.staffInChargeName}</span>
      </TableCell>
      <TableCell>
        <div className="flex gap-1">
          <Button size="sm" variant="outline">
            <Calendar className="h-3 w-3 mr-1" />
            Schedule
          </Button>
          <Button size="sm" variant="ghost">
            <User className="h-3 w-3" />
          </Button>
        </div>
      </TableCell>
    </TableRow>
  );

  if (isLoadingOverdue || isLoadingUpcoming) {
    return <LoadingPage />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Follow-up Dashboard</h2>
          <p className="text-muted-foreground">Track and manage follow-up appointments</p>
        </div>
        <Button onClick={handleRefresh} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-500" />
              <div>
                <p className="text-sm text-muted-foreground">Overdue</p>
                <p className="text-2xl font-bold text-red-600">{filteredOverdue.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Clock className="h-5 w-5 text-orange-500" />
              <div>
                <p className="text-sm text-muted-foreground">Upcoming (7 days)</p>
                <p className="text-2xl font-bold text-orange-600">{filteredUpcoming.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-blue-500" />
              <div>
                <p className="text-sm text-muted-foreground">Total Follow-ups</p>
                <p className="text-2xl font-bold text-blue-600">{filteredOverdue.length + filteredUpcoming.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-sm text-muted-foreground">Completion Rate</p>
                <p className="text-2xl font-bold text-green-600">
                  {overdueAppointments.length + upcomingAppointments.length > 0 ? 
                    Math.round((upcomingAppointments.length / (overdueAppointments.length + upcomingAppointments.length)) * 100) : 0}%
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex gap-4 items-end">
            <div className="flex-1">
              <Label htmlFor="search">Search</Label>
              <Input
                id="search"
                placeholder="Search by pet name, client name, or phone..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            
            <div>
              <Label>Priority</Label>
              <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All</SelectItem>
                  <SelectItem value="low">Low</SelectItem>
                  <SelectItem value="normal">Normal</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="urgent">Urgent</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label>Days Overdue</Label>
              <Select value={daysOverdue.toString()} onValueChange={(value) => setDaysOverdue(parseInt(value))}>
                <SelectTrigger className="w-24">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="0">0+</SelectItem>
                  <SelectItem value="7">7+</SelectItem>
                  <SelectItem value="14">14+</SelectItem>
                  <SelectItem value="30">30+</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label>Days Ahead</Label>
              <Select value={daysAhead.toString()} onValueChange={(value) => setDaysAhead(parseInt(value))}>
                <SelectTrigger className="w-24">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7">7</SelectItem>
                  <SelectItem value="14">14</SelectItem>
                  <SelectItem value="30">30</SelectItem>
                  <SelectItem value="60">60</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Follow-up Tables */}
      <Tabs defaultValue="overdue" className="w-full">
        <TabsList>
          <TabsTrigger value="overdue" className="flex items-center gap-2">
            <AlertTriangle className="h-4 w-4" />
            Overdue ({filteredOverdue.length})
          </TabsTrigger>
          <TabsTrigger value="upcoming" className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            Upcoming ({filteredUpcoming.length})
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="overdue">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-red-600">
                <AlertTriangle className="h-5 w-5" />
                Overdue Follow-ups
              </CardTitle>
            </CardHeader>
            <CardContent>
              {filteredOverdue.length === 0 ? (
                <div className="text-center py-8">
                  <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                  <p className="text-lg font-medium">No overdue follow-ups!</p>
                  <p className="text-muted-foreground">All follow-ups are up to date.</p>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Pet</TableHead>
                      <TableHead>Client</TableHead>
                      <TableHead>Follow-up Date</TableHead>
                      <TableHead>Priority</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Staff</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredOverdue.map((appointment) => (
                      <AppointmentRow 
                        key={appointment.appointmentId} 
                        appointment={appointment} 
                        isOverdue={true}
                      />
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="upcoming">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-blue-600">
                <Clock className="h-5 w-5" />
                Upcoming Follow-ups
              </CardTitle>
            </CardHeader>
            <CardContent>
              {filteredUpcoming.length === 0 ? (
                <div className="text-center py-8">
                  <CalendarX className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-lg font-medium">No upcoming follow-ups</p>
                  <p className="text-muted-foreground">No follow-ups scheduled for the next {daysAhead} days.</p>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Pet</TableHead>
                      <TableHead>Client</TableHead>
                      <TableHead>Follow-up Date</TableHead>
                      <TableHead>Priority</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Staff</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredUpcoming.map((appointment) => (
                      <AppointmentRow 
                        key={appointment.appointmentId} 
                        appointment={appointment} 
                        isOverdue={false}
                      />
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default FollowUpDashboard;
