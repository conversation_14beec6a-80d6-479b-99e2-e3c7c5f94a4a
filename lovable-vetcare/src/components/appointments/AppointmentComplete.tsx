import React, { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  CheckCircle, 
  FileText, 
  Receipt, 
  CreditCard, 
  Download,
  Printer,
  Send,
  Heart,
  Calendar,
  DollarSign,
  Clock
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { api } from '@/services/api';
import AIAppointmentNotes from './AIAppointmentNotes';

interface AppointmentCompleteProps {
  appointmentId: number;
  appointmentData?: any;
}

interface CompletionSummary {
  totalCategories: number;
  completedCategories: number;
  totalServices: number;
  completedServices: number;
  totalCharges: number;
  currency: string;
  completionPercentage: number;
}

const AppointmentComplete: React.FC<AppointmentCompleteProps> = ({ 
  appointmentId, 
  appointmentData 
}) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isGeneratingInvoice, setIsGeneratingInvoice] = useState(false);
  const [isGeneratingReceipt, setIsGeneratingReceipt] = useState(false);
  const [isCreatingMedicalRecord, setIsCreatingMedicalRecord] = useState(false);
  const [isCompletingAppointment, setIsCompletingAppointment] = useState(false);
  const [finalNotes, setFinalNotes] = useState('');
  const [recommendations, setRecommendations] = useState('');

  // Calculate completion summary
  const getCompletionSummary = (): CompletionSummary => {
    if (!appointmentData?.appointmentCategories) {
      return {
        totalCategories: 0,
        completedCategories: 0,
        totalServices: 0,
        completedServices: 0,
        totalCharges: 0,
        currency: 'KES',
        completionPercentage: 0
      };
    }

    const categories = appointmentData.appointmentCategories;
    const totalCategories = categories.length;
    const completedCategories = categories.filter((cat: any) => cat.isCompleted).length;
    
    let totalServices = 0;
    let completedServices = 0;
    let totalCharges = 0;
    let currency = 'KES';

    categories.forEach((category: any) => {
      if (category.categoryServices) {
        totalServices += category.categoryServices.length;
        completedServices += category.categoryServices.filter((s: any) => s.isCompleted).length;
        
        category.categoryServices.forEach((service: any) => {
          totalCharges += service.price || 0;
          if (service.currency) currency = service.currency;
        });
      }
    });

    const completionPercentage = totalServices > 0 ? Math.round((completedServices / totalServices) * 100) : 0;

    return {
      totalCategories,
      completedCategories,
      totalServices,
      completedServices,
      totalCharges,
      currency,
      completionPercentage
    };
  };

  const summary = getCompletionSummary();

  // Complete appointment mutation
  const completeAppointmentMutation = useMutation({
    mutationFn: async (completionData: any) => {
      const response = await api.put(`/appointments/${appointmentId}/complete`, completionData);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['appointment', appointmentId] });
      toast({
        title: "Success",
        description: "Appointment completed successfully",
      });
      setIsCompletingAppointment(false);
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to complete appointment",
        variant: "destructive",
      });
      setIsCompletingAppointment(false);
    }
  });

  // Generate Invoice
  const generateInvoiceMutation = useMutation({
    mutationFn: async () => {
      const response = await api.post(`/invoices/generate/${appointmentId}`);
      return response.data;
    },
    onSuccess: (data) => {
      setIsGeneratingInvoice(false);
      toast({
        title: "Invoice Generated",
        description: "Invoice has been generated successfully",
      });
      // Handle invoice download or display
    },
    onError: (error: any) => {
      setIsGeneratingInvoice(false);
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to generate invoice",
        variant: "destructive",
      });
    }
  });

  // Generate Receipt
  const generateReceiptMutation = useMutation({
    mutationFn: async () => {
      const response = await api.post(`/receipts/generate/${appointmentId}`);
      return response.data;
    },
    onSuccess: (data) => {
      setIsGeneratingReceipt(false);
      toast({
        title: "Receipt Generated",
        description: "Receipt has been generated successfully",
      });
      // Handle receipt download or display
    },
    onError: (error: any) => {
      setIsGeneratingReceipt(false);
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to generate receipt",
        variant: "destructive",
      });
    }
  });

  // Create Medical Record
  const createMedicalRecordMutation = useMutation({
    mutationFn: async () => {
      const response = await api.post(`/appointments/${appointmentId}/generate-medical-record`);
      return response.data;
    },
    onSuccess: (data) => {
      setIsCreatingMedicalRecord(false);
      toast({
        title: "Medical Record Created",
        description: "Medical record has been created successfully",
      });
      queryClient.invalidateQueries({ queryKey: ['medicalRecords', appointmentData?.petId] });
    },
    onError: (error: any) => {
      setIsCreatingMedicalRecord(false);
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to create medical record",
        variant: "destructive",
      });
    }
  });

  const handleGenerateInvoice = () => {
    setIsGeneratingInvoice(true);
    generateInvoiceMutation.mutate();
  };

  const handleGenerateReceipt = () => {
    setIsGeneratingReceipt(true);
    generateReceiptMutation.mutate();
  };

  const handleCreateMedicalRecord = () => {
    setIsCreatingMedicalRecord(true);
    createMedicalRecordMutation.mutate();
  };

  const handleCompleteAppointment = () => {
    setIsCompletingAppointment(true);
    completeAppointmentMutation.mutate({
      finalNotes,
      recommendations,
      generateInvoice: false,
      generateReceipt: false,
      createMedicalRecord: false
    });
  };

  const isFullyCompleted = summary.completionPercentage === 100;
  const isAppointmentCompleted = appointmentData?.status === 'completed';

  return (
    <div className="space-y-6">
      {/* Completion Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className={`h-5 w-5 ${isFullyCompleted ? 'text-green-600' : 'text-yellow-600'}`} />
            Appointment Completion Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">
                {summary.completedCategories}/{summary.totalCategories}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Categories</div>
            </div>
            <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {summary.completedServices}/{summary.totalServices}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Services</div>
            </div>
            <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">{summary.completionPercentage}%</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Complete</div>
            </div>
            <div className="text-center p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">
                {summary.currency} {summary.totalCharges}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Total Charges</div>
            </div>
          </div>

          <div className="flex items-center justify-center">
            <Badge 
              variant={isFullyCompleted ? "default" : "secondary"}
              className={`px-4 py-2 text-sm ${
                isFullyCompleted 
                  ? 'bg-green-100 text-green-800 border-green-200' 
                  : 'bg-yellow-100 text-yellow-800 border-yellow-200'
              }`}
            >
              {isFullyCompleted ? 'Appointment Fully Completed' : 'Appointment In Progress'}
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* AI Generated Summary */}
      <AIAppointmentNotes
        appointmentId={appointmentId}
        appointmentData={appointmentData}
      />

      {/* Complete Appointment Form */}
      {isFullyCompleted && !isAppointmentCompleted && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              Complete Appointment
            </CardTitle>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Add final notes and recommendations before marking the appointment as completed
            </p>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="finalNotes">Final Notes</Label>
              <Textarea
                id="finalNotes"
                placeholder="Add any final observations, outcomes, or important notes..."
                value={finalNotes}
                onChange={(e) => setFinalNotes(e.target.value)}
                rows={3}
              />
            </div>
            <div>
              <Label htmlFor="recommendations">Recommendations</Label>
              <Textarea
                id="recommendations"
                placeholder="Add follow-up recommendations, care instructions, or next steps..."
                value={recommendations}
                onChange={(e) => setRecommendations(e.target.value)}
                rows={3}
              />
            </div>
            <Button
              onClick={handleCompleteAppointment}
              disabled={isCompletingAppointment}
              className="w-full bg-green-600 hover:bg-green-700"
              size="lg"
            >
              {isCompletingAppointment ? (
                <>
                  <Clock className="h-4 w-4 mr-2 animate-spin" />
                  Completing Appointment...
                </>
              ) : (
                <>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Complete Appointment
                </>
              )}
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Completion Actions */}
      {isAppointmentCompleted && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Post-Completion Actions
            </CardTitle>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Generate final documents and records for this completed appointment
            </p>
          </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Generate Invoice */}
            <Card className="border-2 border-dashed border-blue-200 hover:border-blue-400 transition-colors">
              <CardContent className="p-6 text-center">
                <Receipt className="h-8 w-8 text-blue-600 mx-auto mb-3" />
                <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-2">Generate Invoice</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                  Create detailed invoice with all services and charges
                </p>
                <Button
                  onClick={handleGenerateInvoice}
                  disabled={isGeneratingInvoice}
                  className="w-full"
                  variant="outline"
                >
                  {isGeneratingInvoice ? (
                    <>
                      <Clock className="h-4 w-4 mr-2 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <DollarSign className="h-4 w-4 mr-2" />
                      Generate Invoice
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>

            {/* Generate Receipt */}
            <Card className="border-2 border-dashed border-green-200 hover:border-green-400 transition-colors">
              <CardContent className="p-6 text-center">
                <CreditCard className="h-8 w-8 text-green-600 mx-auto mb-3" />
                <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-2">Generate Receipt</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                  Create payment receipt for completed services
                </p>
                <Button
                  onClick={handleGenerateReceipt}
                  disabled={isGeneratingReceipt}
                  className="w-full"
                  variant="outline"
                >
                  {isGeneratingReceipt ? (
                    <>
                      <Clock className="h-4 w-4 mr-2 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Printer className="h-4 w-4 mr-2" />
                      Generate Receipt
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>

            {/* Create Medical Record */}
            <Card className="border-2 border-dashed border-purple-200 hover:border-purple-400 transition-colors">
              <CardContent className="p-6 text-center">
                <Heart className="h-8 w-8 text-purple-600 mx-auto mb-3" />
                <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-2">Create Medical Record</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                  Add appointment to pet's medical history
                </p>
                <Button
                  onClick={handleCreateMedicalRecord}
                  disabled={isCreatingMedicalRecord}
                  className="w-full"
                  variant="outline"
                >
                  {isCreatingMedicalRecord ? (
                    <>
                      <Clock className="h-4 w-4 mr-2 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    <>
                      <Calendar className="h-4 w-4 mr-2" />
                      Create Record
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          </div>
        </CardContent>
        </Card>
      )}

      {/* Status Messages */}
      {!isFullyCompleted && (
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <Clock className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                Appointment In Progress
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Complete all appointment services before you can mark the appointment as completed.
              </p>
              <div className="mt-4">
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${summary.completionPercentage}%` }}
                  ></div>
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  {summary.completedServices} of {summary.totalServices} services completed
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {isAppointmentCompleted && (
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                Appointment Completed
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                This appointment has been successfully completed. You can now generate invoices, receipts, and medical records.
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default AppointmentComplete;
