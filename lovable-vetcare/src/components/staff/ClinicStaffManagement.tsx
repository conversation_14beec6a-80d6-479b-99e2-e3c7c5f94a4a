import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from "@/components/ui/dialog";
import { 
  Users, 
  UserPlus, 
  Settings, 
  Shield, 
  Building2, 
  CheckCircle, 
  XCircle,
  Edit,
  Trash2
} from "lucide-react";
import { useAuth } from "@/store";
import { api } from "@/services/api";
import { toast } from "@/components/ui/use-toast";

interface StaffMember {
  staffId: number;
  name: string;
  email: string;
  phoneNumber: string;
  jobTitle: string;
  role: string;
  permissions: string[];
  specialPermissions: string[];
  specializations: string[];
  isOwner: boolean;
  isManager: boolean;
  isPrimaryClinic: boolean;
  isActive: boolean;
}

interface AssignStaffForm {
  staffId: number;
  roleId: number;
  permissions: number[];
}

export function ClinicStaffManagement() {
  const { currentClinic, staff: currentUser } = useAuth();
  const [loading, setLoading] = useState(false);
  const [staffMembers, setStaffMembers] = useState<StaffMember[]>([]);
  const [availableStaff, setAvailableStaff] = useState<any[]>([]);
  const [roles, setRoles] = useState<any[]>([]);
  const [permissions, setPermissions] = useState<any[]>([]);
  const [assignDialogOpen, setAssignDialogOpen] = useState(false);
  
  const [assignForm, setAssignForm] = useState<AssignStaffForm>({
    staffId: 0,
    roleId: 0,
    permissions: []
  });

  useEffect(() => {
    if (currentClinic) {
      fetchClinicStaff();
      fetchAvailableStaff();
      fetchRoles();
      fetchPermissions();
    }
  }, [currentClinic]);

  const fetchClinicStaff = async () => {
    if (!currentClinic) return;
    
    setLoading(true);
    try {
      const response = await api.get(`/clinics/${currentClinic.clinicId}/staff`);
      if (response.success) {
        setStaffMembers(response.data.data || []);
      }
    } catch (error) {
      console.error('Error fetching clinic staff:', error);
      toast({
        title: "Error",
        description: "Failed to fetch clinic staff",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchAvailableStaff = async () => {
    try {
      const response = await api.get('/staff');
      if (response.success) {
        setAvailableStaff(response.data.data || []);
      }
    } catch (error) {
      console.error('Error fetching available staff:', error);
    }
  };

  const fetchRoles = async () => {
    try {
      const response = await api.get('/roles');
      if (response.success) {
        setRoles(response.data.data || []);
      }
    } catch (error) {
      console.error('Error fetching roles:', error);
    }
  };

  const fetchPermissions = async () => {
    try {
      const response = await api.get('/permissions');
      if (response.success) {
        setPermissions(response.data.data || []);
      }
    } catch (error) {
      console.error('Error fetching permissions:', error);
    }
  };

  const handleAssignStaff = async () => {
    if (!currentClinic || !assignForm.staffId || !assignForm.roleId) {
      toast({
        title: "Error",
        description: "Please select staff member and role",
        variant: "destructive"
      });
      return;
    }

    setLoading(true);
    try {
      const response = await api.post(`/clinics/${currentClinic.clinicId}/assign-staff`, {
        staffId: assignForm.staffId,
        roleId: assignForm.roleId,
        permissions: assignForm.permissions
      });

      if (response.success) {
        toast({
          title: "Success",
          description: "Staff member assigned successfully",
        });
        setAssignDialogOpen(false);
        setAssignForm({ staffId: 0, roleId: 0, permissions: [] });
        fetchClinicStaff();
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to assign staff",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const getStaffBadgeVariant = (member: StaffMember) => {
    if (member.isOwner) return "default";
    if (member.isManager) return "secondary";
    return "outline";
  };

  const getStaffBadgeText = (member: StaffMember) => {
    if (member.isOwner) return "Owner";
    if (member.isManager) return "Manager";
    return member.role;
  };

  const canManageStaff = currentUser?.isClinicOwner || 
    staffMembers.find(m => m.staffId === currentUser?.staffId)?.isManager;

  if (!currentClinic) {
    return (
      <Alert>
        <Building2 className="h-4 w-4" />
        <AlertDescription>
          Please select a clinic to manage staff.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Staff Management - {currentClinic.clinicName}
            </CardTitle>
            {canManageStaff && (
              <Dialog open={assignDialogOpen} onOpenChange={setAssignDialogOpen}>
                <DialogTrigger asChild>
                  <Button className="flex items-center gap-2">
                    <UserPlus className="h-4 w-4" />
                    Assign Staff
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-md">
                  <DialogHeader>
                    <DialogTitle>Assign Staff to Clinic</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="staffSelect">Select Staff Member</Label>
                      <Select
                        value={assignForm.staffId.toString()}
                        onValueChange={(value) => 
                          setAssignForm(prev => ({ ...prev, staffId: parseInt(value) }))
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Choose staff member" />
                        </SelectTrigger>
                        <SelectContent>
                          {availableStaff.map((staff) => (
                            <SelectItem key={staff.staffId} value={staff.staffId.toString()}>
                              {staff.firstName} {staff.lastName} - {staff.jobTitle}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="roleSelect">Select Role</Label>
                      <Select
                        value={assignForm.roleId.toString()}
                        onValueChange={(value) => 
                          setAssignForm(prev => ({ ...prev, roleId: parseInt(value) }))
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Choose role" />
                        </SelectTrigger>
                        <SelectContent>
                          {roles.map((role) => (
                            <SelectItem key={role.roleId} value={role.roleId.toString()}>
                              {role.roleName}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="flex justify-end gap-2">
                      <Button 
                        variant="outline" 
                        onClick={() => setAssignDialogOpen(false)}
                      >
                        Cancel
                      </Button>
                      <Button 
                        onClick={handleAssignStaff}
                        disabled={loading}
                      >
                        {loading ? "Assigning..." : "Assign Staff"}
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">Loading staff members...</div>
          ) : staffMembers.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No staff members assigned to this clinic yet.
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead>Job Title</TableHead>
                  <TableHead>Specializations</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Clinic Type</TableHead>
                  {canManageStaff && <TableHead>Actions</TableHead>}
                </TableRow>
              </TableHeader>
              <TableBody>
                {staffMembers.map((member) => (
                  <TableRow key={member.staffId}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{member.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {member.email}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={getStaffBadgeVariant(member)}>
                        {getStaffBadgeText(member)}
                      </Badge>
                    </TableCell>
                    <TableCell>{member.jobTitle}</TableCell>
                    <TableCell>
                      <div className="flex flex-wrap gap-1">
                        {member.specializations.map((spec, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {spec}
                          </Badge>
                        ))}
                      </div>
                    </TableCell>
                    <TableCell>
                      {member.isActive ? (
                        <div className="flex items-center gap-1 text-green-600">
                          <CheckCircle className="h-4 w-4" />
                          Active
                        </div>
                      ) : (
                        <div className="flex items-center gap-1 text-red-600">
                          <XCircle className="h-4 w-4" />
                          Inactive
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      <Badge variant={member.isPrimaryClinic ? "default" : "secondary"}>
                        {member.isPrimaryClinic ? "Primary" : "Additional"}
                      </Badge>
                    </TableCell>
                    {canManageStaff && (
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Button variant="outline" size="sm">
                            <Edit className="h-3 w-3" />
                          </Button>
                          {!member.isOwner && (
                            <Button variant="outline" size="sm">
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    )}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Staff Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">{staffMembers.length}</div>
            <div className="text-sm text-muted-foreground">Total Staff</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">
              {staffMembers.filter(m => m.isActive).length}
            </div>
            <div className="text-sm text-muted-foreground">Active Staff</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">
              {staffMembers.filter(m => m.isPrimaryClinic).length}
            </div>
            <div className="text-sm text-muted-foreground">Primary Staff</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">
              {staffMembers.filter(m => !m.isPrimaryClinic).length}
            </div>
            <div className="text-sm text-muted-foreground">Additional Staff</div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
