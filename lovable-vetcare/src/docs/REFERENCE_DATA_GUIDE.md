# Reference Data Management Guide

## Overview

This guide explains how to use the centralized reference data management system to avoid multiple API calls for frequently accessed data like species, breeds, staff, roles, and appointment categories.

## Problem Solved

Previously, components were making individual API calls for reference data, leading to:
- Multiple redundant API calls for the same data
- Poor performance and increased server load
- Inconsistent data across components
- No caching mechanism

## Solution

We've implemented a centralized reference data store using Zustand that:
- Caches frequently accessed data with TTL (Time To Live)
- Provides React Query-like hooks for easy integration
- Automatically fetches data when needed
- Prevents duplicate API calls
- Maintains data consistency across the application

## Architecture

### Store Structure
```
src/store/slices/referenceDataSlice.ts - Main store slice
src/hooks/useReferenceDataQuery.ts - React Query-like hooks
src/store/index.ts - Combined store with reference data
```

### Data Types Cached
- **Species**: Animal species data
- **Breeds**: Breed data (cached by species)
- **Staff**: All staff members
- **Roles**: User roles
- **Appointment Categories**: Available appointment types
- **Clients**: Client data (optional, for frequently accessed clients)

## Usage

### 1. Using Reference Data Hooks

Replace your existing React Query calls with the new reference data hooks:

#### Before (Multiple API calls):
```typescript
// ❌ Old way - Multiple API calls
const { data: speciesData } = useQuery({
  queryKey: ['species'],
  queryFn: () => getSpecies({ limit: 100 })
});

const { data: staffData } = useQuery({
  queryKey: ['staff'],
  queryFn: () => getAllStaff()
});

const species = speciesData?.data?.data || [];
const staff = staffData?.data?.data || [];
```

#### After (Centralized cache):
```typescript
// ✅ New way - Centralized cache
import { useSpeciesQuery, useStaffQuery } from '@/hooks/useReferenceDataQuery';

const { data: species, isLoading: isLoadingSpecies } = useSpeciesQuery();
const { data: staff, isLoading: isLoadingStaff } = useStaffQuery();
```

### 2. Available Hooks

```typescript
import {
  useSpeciesQuery,
  useBreedsQuery,
  useBreedsBySpeciesQuery,
  useStaffQuery,
  useRolesQuery,
  useAppointmentCategoriesQuery,
  useClientsQuery,
  usePreloadReferenceData
} from '@/hooks/useReferenceDataQuery';
```

### 3. Hook Examples

#### Species Data
```typescript
const { data: species, isLoading, error, refetch } = useSpeciesQuery();
```

#### Breeds by Species
```typescript
const [selectedSpecies, setSelectedSpecies] = useState<string | null>(null);
const { data: breeds, isLoading } = useBreedsBySpeciesQuery(selectedSpecies);
```

#### Staff Data
```typescript
const { data: staff, isLoading } = useStaffQuery();
```

### 4. Preloading Data

For better UX, preload reference data when the app starts:

```typescript
import { usePreloadReferenceData } from '@/hooks/useReferenceDataQuery';

const App = () => {
  const { preloadAll } = usePreloadReferenceData();
  
  useEffect(() => {
    preloadAll(); // Preload all reference data
  }, []);
  
  return <YourApp />;
};
```

### 5. Direct Store Access

For advanced use cases, access the store directly:

```typescript
import { useReferenceData } from '@/store';

const MyComponent = () => {
  const { 
    species, 
    staff, 
    fetchSpecies, 
    clearCache 
  } = useReferenceData();
  
  const handleRefresh = () => {
    fetchSpecies(true); // Force refresh
  };
  
  const handleClearCache = () => {
    clearCache(); // Clear all cached data
  };
};
```

## Migration Guide

### Step 1: Update Imports
```typescript
// Remove these imports
import { getSpecies, getBreeds, getAllStaff } from '@/services/api';

// Add these imports
import { useSpeciesQuery, useStaffQuery } from '@/hooks/useReferenceDataQuery';
```

### Step 2: Replace useQuery Calls
```typescript
// Before
const { data: staffData } = useQuery({
  queryKey: ['staff'],
  queryFn: () => getAllStaff()
});

// After
const { data: staff, isLoading } = useStaffQuery();
```

### Step 3: Update Data Access
```typescript
// Before
const staff = staffData?.data?.data || [];

// After
// staff is already the processed array
```

## Cache Configuration

### TTL (Time To Live)
- Default: 5 minutes
- Configurable in `referenceDataSlice.ts`
- Data automatically refreshes when stale

### Manual Cache Control
```typescript
const { clearCache, clearSpecificCache } = useReferenceData();

// Clear all cache
clearCache();

// Clear specific data type
clearSpecificCache('species');
```

## Performance Benefits

1. **Reduced API Calls**: Data is fetched once and cached
2. **Faster Loading**: Subsequent access is instant
3. **Better UX**: No loading states for cached data
4. **Server Load**: Reduced server requests
5. **Consistency**: Same data across all components

## Best Practices

1. **Use hooks for components**: Always use the provided hooks in React components
2. **Preload critical data**: Preload frequently used data on app start
3. **Handle loading states**: Always handle loading states for better UX
4. **Force refresh when needed**: Use force refresh for data that changes frequently
5. **Clear cache on logout**: Clear sensitive data when user logs out

## Example Component

```typescript
import React from 'react';
import { useSpeciesQuery, useStaffQuery } from '@/hooks/useReferenceDataQuery';

const ExampleComponent = () => {
  const { data: species, isLoading: loadingSpecies } = useSpeciesQuery();
  const { data: staff, isLoading: loadingStaff } = useStaffQuery();
  
  if (loadingSpecies || loadingStaff) {
    return <div>Loading...</div>;
  }
  
  return (
    <div>
      <h2>Species ({species.length})</h2>
      {species.map(s => <div key={s.speciesId}>{s.speciesName}</div>)}
      
      <h2>Staff ({staff.length})</h2>
      {staff.map(s => <div key={s.staffId}>{s.firstName} {s.lastName}</div>)}
    </div>
  );
};
```

## Troubleshooting

### Data Not Loading
- Check if the hook is being called in a React component
- Verify network connectivity
- Check browser console for errors

### Stale Data
- Use `refetch()` to manually refresh
- Check TTL configuration
- Use `force: true` parameter in fetch functions

### Memory Issues
- Call `clearCache()` when appropriate
- Monitor cache size in development
- Consider reducing TTL for large datasets
